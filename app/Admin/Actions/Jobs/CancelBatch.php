<?php

namespace App\Admin\Actions\Jobs;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Support\Facades\Bus;

class CancelBatch extends RowAction
{
    protected string $title = '取消任务';

    public function handle(): Response
    {
        $bus = Bus::findBatch($this->getKey());
        $bus->cancel();

        return $this->response()
            ->success($this->getKey().'批处理任务取消成功')
            ->refresh();
    }

    public function confirm(): array
    {
        return [
            '取消任务？',
            '确定要取消当前的批处理任务么？',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}