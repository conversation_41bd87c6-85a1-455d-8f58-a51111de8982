<?php

namespace App\Admin\Actions\Jobs;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Support\Facades\Artisan;

class Flush extends AbstractTool
{
    protected string $title = '清理所有失败任务';

    public function handle(): Response
    {
        Artisan::call('queue:flush');
        return $this->response()->success('清理所有失败任务成功')->refresh();
    }

    public function confirm(): array
    {
        return [
            '清理队列',
            '确定要清理所有失败的任务么？'
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}