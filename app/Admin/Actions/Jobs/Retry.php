<?php

namespace App\Admin\Actions\Jobs;

use App\Models\FailedJob;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class Retry extends BatchAction
{
    protected string $title = '重试失败任务';

    public function handle(Request $request): Response
    {
        $keys = $request->post('_key');
        $jobs = FailedJob::whereIn('id', $keys)->pluck('uuid');

        Artisan::call('queue:retry '.implode(' ', $jobs->toArray()));

        return $this->response()->success('加入队列成功')->refresh();
    }

    public function confirm(): array
    {
        return [
            '重试队列',
            '确定要重试选中的失败任务么？',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}