<?php

namespace App\Admin\Actions\Jobs;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Support\Facades\Artisan;

class RetryAll extends AbstractTool
{
    protected string $title = '重试所有失败任务';

    public function handle(): Response
    {
        Artisan::call('queue:retry all');
        return $this->response()->success('重试所有失败任务成功')->refresh();
    }

    public function confirm(): array
    {
        return [
            '重试失败任务',
            '确定要重试所有失败任务么？',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}