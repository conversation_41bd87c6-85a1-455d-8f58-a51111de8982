<?php

namespace App\Admin\Actions\Message;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Tools\AbstractTool;

class CleanAll extends AbstractTool
{
    protected string $title = '清空已读';

    protected string $style = 'btn btn-danger';

    public function handle(): Response
    {
        Admin::user()->readNotifications()->delete();

        return $this->response()->success('标记成功')->refresh();
    }

    public function confirm(): array
    {
        return [
            '清空已读',
            '确定要删除所有已读消息么？',
        ];
    }
}