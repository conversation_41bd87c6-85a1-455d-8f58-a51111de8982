<?php

namespace App\Admin\Actions\Modules;

use App\Admin\Forms\Config;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Nwidart\Modules\Facades\Module;

class ConfigModule extends RowAction
{
    protected string $title = '配置';

    public function render(): string
    {
        $name = Module::find($this->getKey())->get('alias');

        return Modal::make()
            ->xl()
            ->centered()
            ->title($name.'模块 '.$this->title)
            ->body(Config::make([], $this->getKey())->payload(['module' => $this->getKey()]))
            ->button($this->title);
    }
}