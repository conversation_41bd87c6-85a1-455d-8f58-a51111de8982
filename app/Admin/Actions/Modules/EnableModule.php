<?php

namespace App\Admin\Actions\Modules;

use App\Models\Configuration;
use App\Models\Module;
use App\Models\ModuleLog;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\RowAction;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;

class EnableModule extends RowAction
{
    protected string $title = '启用';

    public function handle(Request $request): Response
    {
        try {
            $module = app('modules')->find($this->getKey());
            # 检查依赖模块
            $requires = $module->get('requires');

            foreach ($requires as $require) {
                if (Str::contains($require, '|')) {
                    $contain = array_reduce(explode('|', $require), function (bool $carry, string $module) {
                        return $carry || Module::isEnabled($module);
                    }, false);

                    if (! $contain) {
                        throw new Exception(sprintf('依赖模块缺失，请先安装【%s】模块', $require));
                    }
                } elseif (! app('modules')->isEnabled($require)) {
                    throw new Exception(sprintf('依赖模块缺失，请先安装【%s】模块', $require));
                }
            }

            $class = sprintf('\\%s\\%s\\Bootstrap', config('modules.namespace'), $module->getName());
            if (class_exists($class)) {
                call_user_func([$class, 'install']);
            }
            $configFile = module_path($module->getName(), 'config.json');
            $configJson = [];

            if (file_exists($configFile)) {
                $configJson = json_decode(file_get_contents($configFile), true);

                if (! $configJson) {
                    throw new Exception('配置文件有误，请检查');
                }
                foreach ($configJson as $item) {
                    if ($item['type'] == 'tabs') {
                        foreach ($item['list'] as $listItem) {
                            if (! isset($listItem['key'])) {
                                continue;
                            }
                            Configuration::create([
                                'module' => $module->getName(),
                                'key'    => $listItem['key'],
                                'values' => $listItem['value'],
                                'type'   => $listItem['type'],
                                'source' => $listItem['source'] ?? null,
                            ]);
                        }
                    } elseif (! isset($item['key'])) {
                        continue;
                    } else {
                        Configuration::create([
                            'module' => $module->getName(),
                            'key'    => $item['key'],
                            'values' => $item['value'],
                            'type'   => $item['type'],
                            'source' => $item['source'] ?? null,
                        ]);
                    }
                }
            }

            ModuleLog::create([
                'name'    => $module->getName(),
                'user'    => Admin::user(),
                'action'  => 'enable',
                'config'  => $configJson,
                'version' => $module->get('version'),
            ]);

            $module->enable();
            Artisan::call('modelCache:clear');

            $this->updateCurrentVersion($module);
            return $this->response()
                ->success($module->get('alias').' 模块启用成功')
                ->refresh();
        } catch (Exception $exception) {
            return $this->response()
                ->error($exception->getMessage())
                ->refresh();
        }
    }

    /**
     * Notes   : 更新本地已安装版本信息
     *
     * @Date   : 2023/9/4 11:40
     * <AUTHOR> <Jason.C>
     */
    public function updateCurrentVersion(\Nwidart\Modules\Laravel\Module $module): void
    {
        $files  = app('files');
        $status = base_path('modules_versions.json');
        if (!$files->exists($status)) {
            $files->put($status, '[]');
        }
        $version = $module->get('version');

        $file = $files->get($status);
        $json = json_decode($file, true);

        $json[$module->getName()] = $version;

        $files->put($status, json_encode($json, JSON_PRETTY_PRINT));
    }

    public function confirm(): array
    {
        return [
            '确定要启用[ '.$this->getRow()->name.' ]模块么？',
            '启用模块前，请确认模块与其他模块无冲突！',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}
