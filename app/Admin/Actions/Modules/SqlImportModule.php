<?php

namespace App\Admin\Actions\Modules;

use App\Admin\Forms\SqlImportForm;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Nwidart\Modules\Facades\Module;

class SqlImportModule extends RowAction
{
    protected string $title = '导入';

    public function render(): string
    {
        $name = Module::find($this->getKey())->get('alias');

        return Modal::make()
            ->xl()
            ->centered()
            ->title($name.'模块 数据'.$this->title)
            ->body(SqlImportForm::make([], $this->getKey())->payload(['module' => $this->getKey()]))
            ->button($this->title);
    }
}