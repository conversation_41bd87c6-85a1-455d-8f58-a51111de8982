<?php

namespace App\Admin\Actions;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;

class Restore extends RowAction
{
    protected string $title = '恢复';

    public function __construct(protected ?string $model = null)
    {
        parent::__construct();
    }

    public function handle(Request $request): Response
    {
        $key   = $this->getKey();
        $model = $request->get('model');

        $model::withTrashed()->findOrFail($key)->restore();

        return $this->response()->success('已恢复')->refresh();
    }

    public function confirm(): array
    {
        return [
            '恢复数据',
            '确定恢复选中的数据么？'
        ];
    }

    public function parameters(): array
    {
        return [
            'model' => $this->model,
        ];
    }
}