<?php

namespace App\Admin\Actions\Shinami;

use App\Jobs\Shinami\WalletTransactionJob;
use App\Models\WalletTransaction;
use Dcat\Admin\Grid\RowAction;

class Transaction extends RowAction
{
    protected string $title = '转币';

    public function handle()
    {
        try {
            $transaction = WalletTransaction::find($this->getKey());
            WalletTransactionJob::dispatch($transaction);
            return $this->response()->success('操作成功')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}