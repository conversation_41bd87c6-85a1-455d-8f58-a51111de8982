<?php

namespace App\Admin\Actions\Shinami;

use App\Admin\Forms\Shinami\AddBalanceForm;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;

class addBalance extends RowAction
{
    protected string $title = '转币';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->title($this->title)
            ->centered()
            ->button($this->title)
            ->body(AddBalanceForm::make()->payload(['id' => $this->getKey()]));
    }
}