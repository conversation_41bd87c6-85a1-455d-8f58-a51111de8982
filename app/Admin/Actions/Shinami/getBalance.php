<?php

namespace App\Admin\Actions\Shinami;

use App\Models\UserWallet;
use App\Models\WalletTransaction;
use App\Packages\Shinami\Shinami;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;

class getBalance extends RowAction
{
    protected string $title = '获取用户余额';

    public function handle(): Response
    {
        try {
            $wallet   = UserWallet::find($this->getKey());
            $coinType = WalletTransaction::TOKEN_TYPES_VALUES[WalletTransaction::TOKEN_TYPE_WIKE_TOKEN];
            $response = Shinami::node()
                ->getBalance($wallet->wallet_address, $coinType);

            if ($response->isError()) {
                return $this->response()->error($response->getMessage());
            }
            $balance = $response->totalBalance;
            $balance = mist2num($balance);
            return $this->response()->success('操作成功, 余额为：'.$balance)->refresh();
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}