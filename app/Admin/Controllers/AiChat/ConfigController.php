<?php

namespace App\Admin\Controllers\AiChat;

use App\Admin\Traits\WithUploads;
use App\Models\AiChatConfig;
use App\Models\AiChatEngine;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;

class ConfigController extends AdminController
{
    use WithUploads;

    protected string $title = 'AI模型参数配置';

    public function ajaxEngine(Request $request)
    {
        $key = $request->input('q');
        return AiChatEngine::where('type', $key)
            ->select('title', 'name')
            ->get()
            ->toArray();
    }

    protected function grid(): Grid
    {
        return Grid::make(AiChatConfig::with([
            'engine'
        ])->orderByDesc('is_default')->orderBy('order')->orderByDesc('id'), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name', '名称')->editable();
            $grid->column('key', '模型')->using(AiChatConfig::KEYS);
            $grid->column('engine.title', '引擎');
            $grid->column('apikey', 'apikey');
            $grid->column('status', '状态')->switch();
            $grid->column('order', '排序(小到大)')->editable();
            $grid->column('is_default', '默认')->switch();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        });
    }

    protected function form(): Form
    {
        return Form::make(AiChatConfig::class, function (Form $form) {
            $form->text('name', '名称')->required();
            $form->select('key', '模型')
                ->options(AiChatConfig::KEYS)
                ->load('engine_name', admin_url('ai/config/get_engine'), 'name', 'title')
                ->required();
            $form->select('engine_name', '引擎')
                ->options()
                ->required();
            $form->text('apikey', 'Api Key')->required();
            $form->switch('status', '状态')->default(1);
            $form->number('order', '排序(小到大)')->default(0);
            $form->switch('is_default', '默认')->default(1);
        });
    }
}