<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Traits\HasUploadedFile;
use Illuminate\Http\Request;

class ConfigController extends AdminController
{
    use HasUploadedFile;

    public function upload(Request $request)
    {
        $disk = $request->input('ext.disk', config('filesystems.default'));
        $disk = $this->disk($disk);

        if ($this->isDeleteRequest()) {
            return $this->deleteFileAndResponse($disk);
        }
        $file   = $this->file();
        $name   = md5(uniqid()).'.'.$file->getClientOriginalExtension();
        $dir    = date('Y/m/d');
        $result = $disk->putFileAs($dir, $file, $name);

        $path = "$dir/$name";

        return $result
            ? $this->responseUploaded($path, $disk->url($path))
            : $this->responseErrorMessage('文件上传失败');
    }
}