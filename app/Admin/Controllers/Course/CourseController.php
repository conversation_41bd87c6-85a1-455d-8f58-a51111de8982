<?php

namespace App\Admin\Controllers\Course;

use App\Admin\Traits\WithUploads;
use App\Models\Course;
use App\Models\CourseCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class CourseController extends AdminController
{
    use WithUploads;

    protected string $title = '课程平台';

    public function grid(): Grid
    {
        return Grid::make(Course::with(['category']), function (Grid $grid) {
            $grid->showBatchDelete();
            $grid->model()->orderByDesc('sort');

            $grid->quickSearch(['title'])
                ->placeholder('标题');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '内容标题');
                $filter->like('category_id', '分类')
                    ->select(function ($query) {
                        return CourseCategory::pluck('title', 'id');
                    });
            });

            $grid->column('id', '#ID#');
            $grid->column('category.title', '分类名称');
            $grid->column('title', '标题');
            $grid->column('cover', '封面图')
                ->image('', 50);
            $grid->column('description', '简介');
            $grid->column('url', '地址');
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(Course::with('category'), function (Form $form) {
            $form->title('基础信息');
            $form->text('title', '标题')
                ->required();
            $form->select('category_id', '分类')
                ->options(function () {
                    return CourseCategory::pluck('title', 'id');
                })
                ->required();
            $this->cover($form);
            $form->textarea('description', '内容简介');
            $form->text('url', '跳转地址')->required();
        });
    }
}
