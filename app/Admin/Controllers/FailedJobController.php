<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Jobs\Flush;
use App\Admin\Actions\Jobs\Retry;
use App\Admin\Actions\Jobs\RetryAll;
use App\Admin\Renders\DisplayFailedJob;
use App\Models\FailedJob;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class FailedJobController extends AdminController
{
    protected string $title = '失败队列';

    public function grid(): Grid
    {
        return Grid::make(FailedJob::orderByDesc('id'), function (Grid $grid) {
            $grid->async();

            $grid->disableCreateButton();
            $grid->disableEditButton();

            $grid->tools([new Flush, new RetryAll]);
            $grid->batchActions([new Retry]);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('connection', '链接');
                $filter->equal('queue', '队列名称');
            });

            $grid->column('id', '#ID#');
            $grid->column('uuid', '#ID#')->copyable();
            $grid->column('connection', '链接')
                ->dot([
                    'database' => 'primary',
                    'redis'    => 'danger',
                ]);
            $grid->column('payload.displayName', '任务名称');
            $grid->column('queue', '队列名称')
                ->label();
            $grid->column('failed_at', '失败时间');
            $grid->column('详情')
                ->display('详情')
                ->modal(DisplayFailedJob::make());
        });
    }

    public function form(): Form
    {
        return Form::make(FailedJob::class);
    }
}