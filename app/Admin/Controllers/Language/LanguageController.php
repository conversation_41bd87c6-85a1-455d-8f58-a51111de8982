<?php

namespace App\Admin\Controllers\Language;

use App\Models\Language;
use App\Models\LanguageType;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class LanguageController extends AdminController
{
    protected string $title = '语言包管理';

    protected function grid(): Grid
    {
        return Grid::make(Language::class, function (Grid $grid) {
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('key', '关键字');
                foreach (LanguageType::all() as $languageType) {
                    $filter->like("config->{$languageType->key}", $languageType->name);
                }
            });
            $grid->column('key', '关键字');
            foreach (LanguageType::all() as $languageType) {
                $grid->column("config.{$languageType->key}", $languageType->name);
            }
        });
    }

    protected function form(): Form
    {
        return Form::make(Language::class, function (Form $form) {
            $form->text('key', '关键字')->required()
                ->creationRules(['unique:languages,key'], [
                    'unique' => '关键字不可重复'
                ]);
            $form->embeds('config', '语言配置', function (Form\EmbeddedForm $form) {
                foreach (LanguageType::pluck('name', 'key') as $key => $name) {
                    dump($key, $name);
                    $form->text($key, $name);
                }
            });
        });
    }
}