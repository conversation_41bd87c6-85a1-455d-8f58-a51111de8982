<?php

namespace App\Admin\Controllers\Language;

use App\Models\LanguageType;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class LanguageTypeController extends AdminController
{
    protected string $title = '语言设定';

    protected function grid(): Grid
    {
        return Grid::make(new LanguageType(), function (Grid $grid) {
            $grid->column('id');
            $grid->column('name', '语言名称')->editable();
            $grid->column('key', '关键字');
            $grid->column('status', '状态')->switch();
            $grid->column('is_default', '默认')->switch();
        });
    }

    protected function form(): Form
    {
        return Form::make(new LanguageType(), function (Form $form) {
            $form->text('name', '语言名称')->required();
            $form->text('key', '关键字')
                ->required()
                ->rules(['unique:language_types,key'], [
                    'unique' => '关键字不可重复',
                ]);
            $form->switch('status', '状态')->default(0);
            $form->switch('is_default', '默认')->default(0);
        });
    }
}