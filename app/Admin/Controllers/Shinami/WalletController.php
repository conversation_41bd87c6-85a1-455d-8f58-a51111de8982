<?php

namespace App\Admin\Controllers\Shinami;

use App\Admin\Actions\Shinami\addBalance;
use App\Admin\Actions\Shinami\getBalance;
use App\Admin\Renders\Shinami\AllCoins;
use App\Admin\Traits\WithUploads;
use App\Models\SystemConfig as SystemConfigModel;
use App\Models\UserWallet;
use App\Models\WalletTransaction;
use App\Packages\Shinami\Shinami;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Show;

class WalletController extends AdminController
{
    use WithUploads;

    protected string $title = '用户钱包';

    public function grid(): Grid
    {
        return Grid::make(UserWallet::with(['user']), function (Grid $grid) {
            $grid->showBatchDelete();
            $grid->model()->orderByDesc('id');
            $grid->disableCreateButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '所属用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
                $filter->like('wallet_id', '钱包id');
                $filter->like('wallet_address', '钱包地址');
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit();
                $actions->disableDelete();
                $actions->append(new getBalance());
                $actions->append(new addBalance());
            });

            $grid->column('id', '#ID#');
            $grid->column('user', '用户')
                ->display(function () {
                    return $this->user->show_name;
                });
            $grid->column('wallet_id', '钱包id');
            $grid->column('wallet_address', '钱包地址')->limit(20);
//            $grid->column('wallet_secret', '钱包密钥')
//                ->display(function () {
//                    return Crypt::decryptString($this->wallet_secret);
//                })->limit(20);
            $grid->column('is_active', '是否激活');
            $grid->column('all_balances', '所有账户Coins')
                ->display('详情')
                ->modal(AllCoins::make());
            $grid->column('created_at');
        });
    }

    /**
     * Notes: 运营账户
     *
     * @Author: 玄尘
     * @Date: 2025/4/27 09:27
     * @param  \Dcat\Admin\Layout\Content  $content
     * @return \Dcat\Admin\Layout\Content
     */
    public function adminWallet(Content $content)
    {
        $this->title = '运营账户';
        return $content
            ->header('运营账户')
            ->description('Admin Wallet')
            ->body(function (Row $row) {
                $config = SystemConfigModel::updateOrCreate([
                    'type' => 'wallet',
                ])->toArray();
                $show   = new Show();
                $show->disableDeleteButton();
                $show->disableEditButton();
                $show->disableListButton();

                $wtBalance   = Shinami::node()
                    ->getBalance($config['config']['wallet_admin_address'], WalletTransaction::TOKEN_TYPE_WIKE_TOKEN);
                $usdcBalance = Shinami::node()
                    ->getBalance($config['config']['wallet_admin_address'], WalletTransaction::TOKEN_TYPE_USDC_TOKEN);
                $show->field('wallet_admin_id', '管理员运营钱包 id')
                    ->value($config['config']['wallet_admin_id']);
                $show->field('wallet_admin_address', '管理员运营钱包地址')
                    ->value($config['config']['wallet_admin_address']);
                $show->field('wallet_admin_wt_balance', 'WT钱包余额')
                    ->value(mist2num($wtBalance->totalBalance));
                $show->field('wallet_admin_usdc_balance', 'USDC钱包余额')
                    ->value(mist2num($usdcBalance->totalBalance));
                return $row->column(12, $show->render());
            });
    }

}
