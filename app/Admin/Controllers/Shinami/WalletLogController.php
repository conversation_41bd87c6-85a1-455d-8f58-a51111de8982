<?php

namespace App\Admin\Controllers\Shinami;

use App\Admin\Renders\Shinami\LogResult;
use App\Admin\Traits\WithUploads;
use App\Models\WalletOperation;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class WalletLogController extends AdminController
{
    use WithUploads;

    protected string $title = '操作日志';

    public function grid(): Grid
    {
        return Grid::make(WalletOperation::class, function (Grid $grid) {
            $grid->showBatchDelete();
            $grid->model()->orderByDesc('id');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('wallet_id', '钱包id');
                $filter->like('wallet_address', '钱包地址');;
            });

            $grid->disableActions();

            $grid->column('id', '#ID#');
            $grid->column('base_url', '主地址');
            $grid->column('endpoint', '请求地址');
            $grid->column('数据')
                ->display('详情')
                ->modal(LogResult::make());
            $grid->column('is_success', '是否成功')->bool();
            $grid->column('error_message', '错误信息');
            $grid->column('created_at');
        });
    }

}
