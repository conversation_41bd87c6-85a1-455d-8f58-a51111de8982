<?php

namespace App\Admin\Controllers\Shinami;

use App\Admin\Actions\Shinami\Transaction;
use App\Admin\Renders\Shinami\TransactionInfo;
use App\Admin\Traits\WithUploads;
use App\Models\WalletTransaction;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class WalletTransactionController extends AdminController
{
    use WithUploads;

    protected string $title = '转账记录';

    public function grid(): Grid
    {
        return Grid::make(WalletTransaction::class, function (Grid $grid) {
            $grid->model()->orderByDesc('id');
            $grid->disableCreateButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('from_wallet_id', '发送方钱包id');
                $filter->like('to_wallet_id', '接收方钱包id');
                $filter->like('token_type', '代币类型');
                $filter->like('transaction_type', '交易类型')
                    ->select(WalletTransaction::TRANSACTION_TYPES);
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                if ($actions->row->canDo()) {
                    $actions->append(new Transaction());
                }
            });

            $grid->column('id', '#ID#');
            $grid->column('transaction_id', '交易ID')->limit(20);
            $grid->column('from_user', '转出人')
                ->display(function () {
                    return $this->fromUser?->show_name ?? lecho('Operation Wallet', 'zh-CN');
                });
            $grid->column('to_user', '转入人')
                ->display(function () {
                    return $this->toUser?->show_name ?? lecho('Operation Wallet', 'zh-CN');
                });
//            $grid->column('from_wallet_id', '发送方钱包id');
//            $grid->column('to_wallet_id', '接收方钱包id');
            $grid->column('token_type', '代币类型')->using(WalletTransaction::TOKEN_TYPES);
            $grid->column('amount', '转账金额')
                ->display(fn($amount) => formatNumber($amount));
            $grid->column('fee', '手续费')
                ->display(fn($amount) => formatNumber($amount));
            $grid->column('actual_amount', '实际到账金额')
                ->display(fn($amount) => formatNumber($amount));
            $grid->column('transaction_type', '交易类型')
                ->using(WalletTransaction::TRANSACTION_TYPES);
            $grid->column('status', '状态')
                ->using(WalletTransaction::STATUS_TEXTS)
                ->label(WalletTransaction::STATUS_TEXT_LABEL);
            $grid->column('transaction_data', '交易数据');
            $grid->column('error_message', '错误信息');
            // 添加查询交易详情按钮
            $grid->column('详情')
                ->display('详情')
                ->modal(TransactionInfo::make());
            $grid->column('created_at', '创建时间');
            $grid->column('completed_at', '完成时间');
        });
    }
}
