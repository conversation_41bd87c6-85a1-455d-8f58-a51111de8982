<?php

namespace App\Admin\Controllers\SystemConfig;

use App\Models\SystemConfig;
use App\Models\SystemConfig as SystemConfigModel;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Widgets\Form;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Crypt;

class IndexController extends Controller
{

    public function bonus(Content $content): Content
    {
        return $content
            ->header('佣金设定')
            ->description('Bonus Setting')
            ->body(function (Row $row) {
                $config = SystemConfigModel::updateOrCreate([
                    'type' => 'bonus',
                ])->toArray();
                $form   = new Form($config);
                $form->action(admin_url('systemconfig/save'));
                $form->hidden('type', 'bonus');
                $form->rate('config.bonus_1', '第一层比例');
                $form->rate('config.bonus_2', '第二层比例');
                $form->rate('config.bonus_3', '第三层比例');
                $form->rate('config.bonus_4', '第四层比例');
                $form->rate('config.bonus_5', '第五层比例');
                $form->rate('config.bonus_6', '第六层比例');
                return $row->column(12, $form->render());
            });
    }

    public function score(Content $content): Content
    {
        return $content
            ->header('积分设定')
            ->description('Score Setting')
            ->body(function (Row $row) {
                $config = SystemConfigModel::updateOrCreate([
                    'type' => 'score',
                ])->toArray();
                $form   = new Form($config);
                $form->action(admin_url('systemconfig/save'));
                $form->hidden('type');
                $form->number('config.register', '注册赠送');
                $form->number('config.day_login', '登录赠送');
                $form->number('config.create_blog', '创建博文');
                $form->number('config.browse_blog', '博文被浏览');
                $form->number('config.look_blog', '观看博文');
                $form->number('config.blog_share', '分享博文');
                $form->number('config.recommend_user', '推荐用户');
                $form->number('config.like', '点赞');
                $form->number('config.day_like_max', '每日点赞上限');
                $form->number('config.comment', '评论');
                $form->number('config.day_comment_max', '每日评论上限');
                $form->number('config.favorite', '收藏');
                $form->number('config.day_favorite_max', '每日收藏上限');
                $form->number('config.ai_ask', 'AI积分');

                return $row->column(12, $form->render());
            });
    }

    /**
     * Notes: wt钱包设置
     *
     * @Author: 玄尘
     * @Date: 2025/4/22 09:06
     */
    public function wallet(Content $content): Content
    {
        return $content
            ->header('钱包设置')
            ->description('Score Setting')
            ->body(function (Row $row) {
                $config = SystemConfigModel::updateOrCreate([
                    'type' => 'wallet',
                ])->toArray();
                // 如果存在管理员钱包密钥，解密显示
                if (isset($config['config']['wallet_admin_secret'])) {
                    $config['config']['wallet_admin_secret'] = '';
                }
                $form = new Form($config);
                $form->action(admin_url('systemconfig/save'));
                $form->hidden('type');
                $form->url('config.shinami_url', '钱包地址');
                $form->url('config.node_server_url', 'node server 地址')
                    ->help('获取封装的txBytes');
                $form->text('config.wallet_access_key', '钱包访问密钥');
                //币种
                $form->text('config.wallet_coin_type', '钱包币种');
                //管理员运营钱包 id  和 密钥 和地址
                $form->text('config.wallet_admin_id', '管理员运营钱包 id');
                $form->text('config.wallet_admin_secret', '管理员运营钱包密钥');
                $form->text('config.wallet_admin_address', '管理员运营钱包地址');

                $form->number('config.points_to_wt_rate', '点数兑换比例(100:1)')
                    ->help('兑换比例');
                $form->number('config.points_conversion_threshold', '点数兑换阈值(100)')
                    ->help('低于阈值时不能兑换');
                $form->number('config.points_conversion_max', '点数兑换最大数量(10000)')
                    ->help('单次最多兑换数量：0不限制');
                $form->number('config.min_exchange_amount', 'WT最小兑换数量')
                    ->help('最小兑换数量：0不限制');
                $form->rate('config.transfer_management_fee_rate', '转账管理费比例(0.5%)');
                // WT代币购买价格
                $form->currency('config.wt_price', 'WT代币价格')
                    ->symbol('$')
                    ->default(1.00)
                    ->help('用户购买WT代币的单价:美元');
                $form->rate('config.wt_to_usdc_rate', 'wt转usdc比例(1wt=0.01usdc)');
                $form->rate('config.external_transfer_fee_rate', 'wt转usdc服务费率');
                //单账户最多转账数量
                $form->number('config.max_wt_to_usdc', 'wt单账户最多转usdc数量');
                $form->number('config.vip_wt', '开通会员赠送wt数');
                $form->rate('config.vip_wt_fee_rate', '会员赠送WT服务费率')
                    ->default(0.3)
                    ->help('从赠送金额中扣除的服务费率(%)');
                $form->number('config.vip_wt_lock_days', '会员赠送WT锁仓天数')
                    ->default(730)
                    ->help('赠送WT的锁仓天数，默认730天(2年)');
                return $row->column(12, $form->render());
            });
    }

    public function save(Request $request): JsonResponse
    {
        $type   = $request->type;
        $config = $request->config;

        if ($type == 'wallet') {
            // 如果存在管理员钱包密钥，加密保存
            if (isset($config['wallet_admin_secret']) && ! empty($config['wallet_admin_secret'])) {
                $config['wallet_admin_secret'] = Crypt::encryptString($config['wallet_admin_secret']);
            } else {
                $wallet_admin_secret           = SystemConfig::getValue('wallet', 'wallet_admin_secret');
                $config['wallet_admin_secret'] = $wallet_admin_secret;
            }
        }

        SystemConfigModel::updateOrCreate([
            'type' => $type,
        ], [
            'config' => $config,
        ]);
        return JsonResponse::make()->success('成功！')->refresh();
    }
}