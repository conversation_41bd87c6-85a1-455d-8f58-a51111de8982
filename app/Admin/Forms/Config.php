<?php

namespace App\Admin\Forms;

use App\Enums\ConfigType;
use App\Models\Configuration;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Form\Field;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class Config extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(): JsonResponse
    {
        foreach (request()->except('module', '_file_', '_form_', '_current_', '_token') as $key => $value) {
            if (is_array($value) && isset($value['keys']) && isset($value['values'])) {
                $save = array_combine($value['keys'], $value['values']);

                Configuration::ofModule($this->payload['module'])
                    ->where('key', $key)
                    ->update(['values' => json_encode($save, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)]);
            } elseif (is_array($value)) {
                Configuration::ofModule($this->payload['module'])
                    ->where('key', $key)
                    ->update(['values' => array_filter($value)]);
            } else {
                if ($value == ["_def_" => null]) {
                    $value = null;
                }

                Configuration::ofModule($this->payload['module'])
                    ->where('key', $key)
                    ->update(['values' => $value]);
            }
        }

        return $this->response()->success('配置修改成功')->refresh();
    }

    public function form(): void
    {
        $jsonFile = module_path($this->payload['module'], 'config.json');

        if (file_exists($jsonFile)) {
            $configs = json_decode(file_get_contents($jsonFile), true);
        } else {
            $configs = [];
        }

        foreach ($configs as $item) {
            if ($item['type'] == ConfigType::TABS->value) {
                $this->parseTabs($item);
            } elseif (isset($item['help'])) {
                $this->parseItem($item)->help($item['help']);
            } else {
                $this->parseItem($item);
            }
        }
    }

    protected function parseTabs($tab): void
    {
        $this->tab($tab['name'], function () use ($tab) {
            foreach ($tab['list'] as $item) {
                if (isset($item['help'])) {
                    $this->parseItem($item)->help($item['help']);
                } else {
                    $this->parseItem($item);
                }
            }
        }, false, $tab['key']);
    }

    protected function parseItem($item): Field
    {
        return match ($item['type']) {
            ConfigType::DIVIDER->value => $this->divider($item['name']),
            ConfigType::PASSWORD->value => $this->password($item['key'], $item['name'])
                ->width($item['width'] ?? 4)
                ->default($this->getValue($item)),
            ConfigType::URL->value => $this->url($item['key'], $item['name'])
                ->width($item['width'] ?? 4)
                ->default($this->getValue($item)),
            ConfigType::NUMBER->value => $this->number($item['key'], $item['name'])
                ->default($this->getValue($item))
                ->width($item['width'] ?? 4),
            ConfigType::RATE->value => $this->rate($item['key'], $item['name'])
                ->default($this->getValue($item))
                ->width($item['width'] ?? 4),
            ConfigType::IMAGE->value => $this->image($item['key'], $item['name'])
                ->downloadable()
                ->autoUpload()
                ->accept('jpg,png,gif,jpeg')
                ->override()
                ->width($item['width'] ?? 4)
                ->url(route('admin.configs.upload', ['ext' => $item['ext'] ?? '']))
                ->default($this->getValue($item)),
            ConfigType::MULTIPLE_IMAGE->value => $this->multipleImage($item['key'], $item['name'])
                ->removable()
                ->downloadable()
                ->autoUpload()
                ->accept('jpg,png,gif,jpeg')
                ->override()
                ->width($item['width'] ?? 4)
                ->url(route('admin.configs.upload', ['ext' => $item['ext'] ?? '']))
                ->default($this->getValue($item)),
            ConfigType::FILE->value => $this->file($item['key'], $item['name'])
                ->removable()
                ->downloadable()
                ->autoUpload()
                ->override()
                ->width($item['width'] ?? 4)
                ->url(route('admin.configs.upload', ['ext' => $item['ext'] ?? '']))
                ->default($this->getValue($item)),
            ConfigType::MULTIPLE_FILE->value => $this->multipleFile($item['key'], $item['name'])
                ->removable()
                ->downloadable()
                ->autoUpload()
                ->override()
                ->width($item['width'] ?? 4)
                ->url(route('admin.configs.upload', ['ext' => $item['ext'] ?? '']))
                ->default($this->getValue($item)),
            ConfigType::SELECT->value => $this->select($item['key'], $item['name'])
                ->options($item['source'])
                ->width($item['width'] ?? 4)
                ->default($this->getValue($item)),
            ConfigType::MULTIPLE_SELECT->value => $this->multipleSelect($item['key'], $item['name'])
                ->options($item['source'])
                ->width($item['width'] ?? 4)
                ->default($this->getValue($item)),
            ConfigType::CHECKBOX->value => $this->checkbox($item['key'], $item['name'])
                ->options($item['source'])
                ->default($this->getValue($item)),
            ConfigType::RADIO->value => $this->radio($item['key'], $item['name'])
                ->options($item['source'])
                ->default($this->getValue($item)),
            ConfigType::ARRAY->value => $this->keyValue($item['key'], $item['name'])
                ->setKeyLabel('键名')
                ->setValueLabel('键值')
                ->default($this->getValue($item)),
            ConfigType::TEXTAREA->value => $this->textarea($item['key'], $item['name'])
                ->default($this->getValue($item)),
            default => $this->text($item['key'], $item['name'])
                ->width($item['width'] ?? 4)
                ->default($this->getValue($item)),
        };
    }

    protected function getValue(array $item): mixed
    {
        $value = Configuration::ofModule($this->payload['module'])
            ->where('key', $item['key'])
            ->first();

        if ($value == null) {
            return $item['value'];
        } else {
            return $value->values ?? '';
        }
    }
}