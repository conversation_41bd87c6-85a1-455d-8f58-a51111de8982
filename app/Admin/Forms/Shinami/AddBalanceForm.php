<?php

namespace App\Admin\Forms\Shinami;

use App\Models\SystemConfig;
use App\Models\UserWallet;
use App\Models\WalletTransaction;
use App\Packages\Shinami\Shinami;
use App\Packages\Shinami\ShinamiException;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\Log;

class AddBalanceForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        try {
            // 获取转币数量
            $amount = (float) $input['amount'];
            if ($amount <= 0) {
                return $this->response()->error('转币数量必须大于0');
            }

            // 获取用户钱包
            $wallet = UserWallet::find($this->payload['id']);
            if (! $wallet) {
                return $this->response()->error('钱包不存在');
            }

            // 获取用户
            $user = $wallet->user;
            if (! $user) {
                return $this->response()->error('用户不存在');
            }

            // 获取管理员钱包ID
            $adminWalletId = SystemConfig::getValue('wallet', 'wallet_admin_id');
            if (empty($adminWalletId)) {
                return $this->response()->error('管理员钱包未配置');
            }

            // 获取管理员钱包密钥
            $adminWalletSecret = Shinami::key()->getAdminSessionToken();
            if (empty($adminWalletSecret)) {
                return $this->response()->error('管理员钱包密钥未配置。请联系管理员');
            }

            // 创建管理员会话令牌
            $adminSessionToken = Shinami::key()->getAdminSessionToken();

            // 简写形式用于数据库存储
            $coinType = WalletTransaction::TOKEN_TYPE_WIKE_TOKEN;

            // 执行转账操作
            $response = Shinami::transaction()
                ->transferBetweenWallets(
                    $adminWalletId,
                    $adminSessionToken,
                    $wallet->wallet_id,
                    $coinType,
                    $amount,
                    ['showBalanceChanges' => true]
                );

            if ($response->isError()) {
                return $this->response()->error('转账失败: '.$response->getMessage());
            }

            Log::channel('shinami')
                ->info('管理员转币执行成功', [
                    'admin_id'       => auth('admin')->id(),
                    'admin_name'     => auth('admin')->user()->name,
                    'to_user_id'     => $user->id,
                    'to_wallet_id'   => $wallet->wallet_id,
                    'amount'         => $amount,
                    'transaction_id' => $response->digest
                ]);
            $admin = Admin::user();

            // 直接使用create方法创建交易记录
            WalletTransaction::create([
                'transaction_id'   => $response->digest,
                'from_user_id'     => null, // 管理员用户ID
                'from_wallet_id'   => $adminWalletId,
                'to_user_id'       => $user->id,
                'to_wallet_id'     => $wallet->wallet_id,
                'token_type'       => $coinType,
                'amount'           => $amount,
                'fee'              => 0,
                'actual_amount'    => $amount,
                'transaction_type' => WalletTransaction::TRANSACTION_TYPE_ADJUST,
                'status'           => WalletTransaction::STATUS_COMPLETED,
                'transaction_data' => [
                    'admin_id'   => $admin->id,
                    'admin_name' => $admin->name,
                    'remark'     => $input['remark'] ?? '管理员转币'
                ],
            ]);

            // 记录日志
            Log::channel('shinami')
                ->info('管理员转币成功', [
                    'admin_id'       => auth('admin')->id(),
                    'admin_name'     => auth('admin')->user()->name,
                    'to_user_id'     => $user->id,
                    'to_wallet_id'   => $wallet->wallet_id,
                    'amount'         => $amount,
                    'transaction_id' => $response->digest
                ]);

            return $this->response()->success('转币成功，交易ID: '.$response->digest)->refresh();
        } catch (ShinamiException $e) {
            return $this->response()->error('转币失败: '.$e->getMessage());
        } catch (\Exception $e) {
            Log::channel('shinami')
                ->error('管理员转币失败', [
                    'admin_id'   => auth('admin')->id(),
                    'admin_name' => auth('admin')->user()->name,
                    'wallet_id'  => $this->payload['id'],
                    'amount'     => $input['amount'] ?? 0,
                    'error'      => $e->getMessage()
                ]);
            return $this->response()->error('系统错误: '.$e->getMessage());
        }
    }

    public function form(): void
    {
        $this->number('amount', '转币数量')->required()->min(0.000001)->help('请输入要转入的币数量');
        $this->text('remark', '备注')->help('可选，记录转币原因');
    }
}