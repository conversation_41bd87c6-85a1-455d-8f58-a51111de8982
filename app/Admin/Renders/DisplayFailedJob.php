<?php

namespace App\Admin\Renders;

use App\Models\FailedJob;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Card;

class DisplayFailedJob extends LazyRenderable
{
    public function render(): string
    {
        $info = FailedJob::find($this->key);

        $row     = new Row();
        $data    = json_encode($info->payload, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        $payload = Card::make('载荷', "<pre>$data</pre>");
        $row->column(12, $payload);
        $exception = Card::make('错误信息', "<code>$info->exception</code>");
        $row->column(12, $exception);

        return $row->render();
    }
}