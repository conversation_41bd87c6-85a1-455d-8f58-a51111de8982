<?php

namespace App\Admin\Renders\Shinami;

use App\Models\WalletOperation;
use Dcat\Admin\Support\LazyRenderable;

class LogResult extends LazyRenderable
{
    public function render(): string
    {
        $log = WalletOperation::find($this->key);

        if (!$log) {
            return '<div class="alert alert-danger">日志记录不存在</div>';
        }

        // 构建详情HTML
        $html = "<div class='wallet-operation-log'>";
        $html .= "<h4>钱包操作日志详情</h4>";

        // 基本信息表格
        $html .= "<div class='table-responsive mb-3'>";
        $html .= "<table class='table table-bordered'>";
        $html .= "<tbody>";

        // 操作基本信息
        $html .= "<tr><th style='width: 150px;'>操作ID</th><td>{$log->id}</td></tr>";
        $html .= "<tr><th>请求地址</th><td>{$log->base_url}{$log->endpoint}</td></tr>";
        $html .= "<tr><th>请求方法</th><td>{$log->method}</td></tr>";
        $html .= "<tr><th>操作状态</th><td>" . ($log->is_success ?
            '<span class="badge badge-success">成功</span>' :
            '<span class="badge badge-danger">失败</span>') . "</td></tr>";

        if (!$log->is_success && $log->error_message) {
            $html .= "<tr><th>错误信息</th><td class='text-danger'>{$log->error_message}</td></tr>";
        }

        $html .= "<tr><th>操作时间</th><td>{$log->created_at}</td></tr>";
        $html .= "</tbody></table>";
        $html .= "</div>";

        // 请求数据
        $html .= "<h5>请求数据</h5>";
        $html .= "<div class='card mb-3'>";
        $html .= "<div class='card-body'>";

        $request_data = $log->input_data;
        if (empty($request_data)) {
            $html .= "<p class='text-muted'>无请求数据</p>";
        } else {
            if (is_array($request_data)) {
                $formatted_request = json_encode($request_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                $html .= "<pre class='json-highlight' style='background-color: #f8f9fa; padding: 10px; border-radius: 4px;'>";
                $html .= htmlspecialchars($formatted_request);
                $html .= "</pre>";
            } else {
                $html .= "<pre style='background-color: #f8f9fa; padding: 10px; border-radius: 4px;'>";
                $html .= htmlspecialchars($request_data);
                $html .= "</pre>";
            }
        }

        $html .= "</div>";
        $html .= "</div>";

        // 响应数据
        $html .= "<h5>响应数据</h5>";
        $html .= "<div class='card'>";
        $html .= "<div class='card-body'>";

        $response_data = $log->response_data;
        if (empty($response_data)) {
            $html .= "<p class='text-muted'>无响应数据</p>";
        } else {
            if (is_array($response_data)) {
                $formatted_response = json_encode($response_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                $html .= "<pre class='json-highlight' style='background-color: #f8f9fa; padding: 10px; border-radius: 4px;'>";
                $html .= htmlspecialchars($formatted_response);
                $html .= "</pre>";
            } else if (is_string($response_data) && $this->isJson($response_data)) {
                // 尝试解析JSON字符串并美化
                $decoded = json_decode($response_data, true);
                $formatted_response = json_encode($decoded, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                $html .= "<pre class='json-highlight' style='background-color: #f8f9fa; padding: 10px; border-radius: 4px;'>";
                $html .= htmlspecialchars($formatted_response);
                $html .= "</pre>";
            } else {
                $html .= "<pre style='background-color: #f8f9fa; padding: 10px; border-radius: 4px;'>";
                $html .= htmlspecialchars($response_data);
                $html .= "</pre>";
            }
        }

        $html .= "</div>";
        $html .= "</div>";

        // 添加简单的JS来增强显示效果
        $html .= "<script>
            document.addEventListener('DOMContentLoaded', function() {
                // 可以在这里添加额外的JS增强，如复制按钮等
            });
        </script>";

        $html .= "</div>";

        return $html;
    }

    /**
     * 检查字符串是否为有效的JSON
     */
    private function isJson($string): bool
    {
        if (!is_string($string)) {
            return false;
        }

        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }
}