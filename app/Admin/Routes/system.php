<?php

use App\Admin\Controllers\SystemConfig\IndexController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'prefix'    => 'systemconfig',
    'namespace' => 'SystemConfig',
], function (Router $router) {
    $router->get('bonus', [IndexController::class, 'bonus']);
    $router->get('score', [IndexController::class, 'score']);
    $router->get('wallet', [IndexController::class, 'wallet']);
    $router->post('save', [IndexController::class, 'save']);
});
