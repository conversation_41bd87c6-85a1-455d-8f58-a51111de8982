<?php

use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Layout\Navbar;

Admin::navbar(function (Navbar $navbar) {
    $navbar->right(view('admin.right'));
});

if (config('app.debug')) {
    Admin::menu()->add(include __DIR__.'/menu.php', 0);
}

// button,点击的时候，会有loading效果
Admin::script(<<<JS
    $('#submit-button').buttonLoading();
JS
);

Grid::resolving(function (Grid $grid) {
    $grid->disableViewButton();
    $grid->disableBatchDelete();
});

Form::resolving(function (Form $form) {
    $form->disableViewCheck();
    $form->disableViewButton();
    $form->disableDeleteButton();
});
