<?php

return [
    [
        'id'        => 'tests',
        'title'     => '系统维护(勿动)',
        'icon'      => 'fa-codepen',
        'uri'       => '',
        'parent_id' => 0,
        'children'  => [
            [
                'id'     => 'redis',
                'title'  => 'Redis队列监控',
                'icon'   => 'fa-reddit-alien',
                'uri'    => 'horizon/dashboard',
                'target' => '_blank',
            ],
            [
                'id'    => 'jobs',
                'title' => '数据库队列',
                'icon'  => 'fa-sort-numeric-asc',
                'uri'   => 'jobs',
            ],
            [
                'id'    => 'batches',
                'title' => '批处理任务',
                'icon'  => 'fa-dedent',
                'uri'   => 'jobs/batches',
            ],
            [
                'id'    => 'failed',
                'title' => '失败队列',
                'icon'  => 'fa-map-signs',
                'uri'   => 'jobs/failed',
            ],
            [
                'id'    => 'modules',
                'title' => 'Modules',
                'icon'  => 'fa-codepen',
                'uri'   => 'modules',
            ],
            [
                'id'    => 'module_logs',
                'title' => '模块日志',
                'icon'  => 'fa-align-left',
                'uri'   => 'modules/logs',
            ],
            [
                'id'     => 'test',
                'title'  => '后果自付',
                'icon'   => 'fa-bug',
                'uri'    => 'test',
                'target' => '_blank',
            ],
        ],
    ],
];