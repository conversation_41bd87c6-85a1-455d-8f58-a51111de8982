<?php

use App\Admin\Controllers\BatchJobController;
use App\Admin\Controllers\ConfigController;
use App\Admin\Controllers\FailedJobController;
use App\Admin\Controllers\HomeController;
use App\Admin\Controllers\JobController;
use App\Admin\Controllers\MessageController;
use App\Admin\Controllers\ModuleController;
use App\Admin\Controllers\TestController;
use Dcat\Admin\Admin;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {
    $router->get('/', [HomeController::class, 'index'])
        ->name('index');
    $router->get('modules', [ModuleController::class, 'index']);
    $router->get('modules/logs', [ModuleController::class, 'logs']);
    $router->any('configs/upload', [ConfigController::class, 'upload'])
        ->name('configs.upload');
    # 队列相关
    $router->get('jobs', [JobController::class, 'index']);
    $router->get('jobs/batches', [BatchJobController::class, 'index']);
    $router->get('jobs/failed', [FailedJobController::class, 'index']);
    $router->delete('jobs/failed/{ids}', [FailedJobController::class, 'destroy']);

    $router->get('test', [TestController::class, 'index']);

    $router->get('messages', [MessageController::class, 'index'])
        ->name('messages.index');
    $router->delete('messages/{id}', [MessageController::class, 'destroy'])
        ->name('messages.destroy');
    $router->get('messages/{notification}', [MessageController::class, 'show'])
        ->name('messages.show');

    foreach (glob(admin_path('Routes').'/*.php') as $routeFile) {
        require $routeFile;
    }
});
