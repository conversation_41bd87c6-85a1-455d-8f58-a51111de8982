<?php

namespace App\Console\Back;

use App\Jobs\Imports\ImportUserJob;
use App\Models\Old\OldUser;
use Illuminate\Console\Command;

class ImportUserCommand extends Command
{
    protected $signature = 'import:user {password}';

    #描述
    protected $description = '老系统导入新系统用户';

    public function handle(): void
    {
        $password = $this->argument('password');
        if ($password !== 'Anetadmin1') {
            echo 'password failed';
        } else {
            $oldUsers = OldUser::query()->get();
            foreach ($oldUsers as $oldUser) {
                ImportUserJob::dispatch($oldUser);
            }
        }
    }
}