<?php

namespace App\Console\Back;

use App\Jobs\Imports\ImportWhoBlogJob;
use App\Models\Old\OldBlog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Modules\Cms\Models\Category;

class ImportWhoBlogCommand extends Command
{
    protected $signature = 'import:whoblog {password}';

    #描述
    protected $description = 'WHOSWHO老系统导入新系统博客';

    public function handle(): void
    {
        $password = $this->argument('password');
        if ($password !== 'Anetadmin1') {
            echo 'password failed';
        } else {
            $categories = DB::connection('old_user')
                ->table('p_blogcate')
                ->where('is_hide', 0)
                ->orderBy('sort', 'asc')
                ->get();
            foreach ($categories as $category) {
                Category::updateOrCreate([
                    'description' => $category->id,
                ], [
                    'name'      => $category->cate,
                    'parent_id' => 1,
                    'status'    => 1,
                ]);
            }
            $oldUsers = OldBlog::query()
                ->where('status', 1)
                ->orderBy('id')->get();
            foreach ($oldUsers as $oldUser) {
                ImportWhoBlogJob::dispatch($oldUser);
            }
        }
    }
}