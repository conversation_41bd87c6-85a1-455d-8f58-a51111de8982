<?php

namespace App\Console\Back;

use App\Jobs\Imports\ImportWhoUserJob;
use App\Models\Old\OldUser;
use Illuminate\Console\Command;

class ImportWhoUserCommand extends Command
{
    protected $signature = 'import:whouser {password}';

    #描述
    protected $description = 'WHOSWHO老系统导入新系统用户';

    public function handle(): void
    {
        $password = $this->argument('password');
        if ($password !== 'Anetadmin1') {
            echo 'password failed';
        } else {
            $oldUsers = OldUser::query()->orderBy('id')->get();
            foreach ($oldUsers as $oldUser) {
                ImportWhoUserJob::dispatch($oldUser);
            }
        }
    }
}