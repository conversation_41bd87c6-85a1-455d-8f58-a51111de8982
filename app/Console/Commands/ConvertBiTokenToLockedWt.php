<?php

namespace App\Console\Commands;

use App\Models\BiTokenConversionRecord;
use App\Models\LockedWt;
use App\Models\LockedWtOperation;
use App\Models\SystemConfig;
use App\Models\User;
use App\Packages\AiWikiUserCenter\AiWikiUserCenter;
use App\Services\LockedWtService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ConvertBiTokenToLockedWt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bitoken:convert-to-locked-wt
                            {--dry-run : 预览模式，不实际执行转换}
                            {--user-id= : 指定特定用户ID进行转换}
                            {--min-bitoken=0 : 最小BiToken数量阈值}
                            {--test-mode : 测试模式，使用模拟数据}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '将用户的BiToken按1:20比例转换为锁仓WT';

    /**
     * 转换比例
     */
    const CONVERSION_RATE = 20;

    /**
     * 统计信息
     */
    protected array $stats = [
        'total_users'     => 0,
        'processed_users' => 0,
        'success_count'   => 0,
        'failed_count'    => 0,
        'total_bitoken'   => 0,
        'total_locked_wt' => 0,
    ];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 开始BiToken转锁仓WT转换任务...');
        
        $isDryRun = $this->option('dry-run');
        $userId = $this->option('user-id');
        $minBiToken = (float) $this->option('min-bitoken');
        $testMode = $this->option('test-mode');

        if ($isDryRun) {
            $this->warn('⚠️  预览模式：将显示转换信息但不实际执行');
        }

        if ($testMode) {
            $this->warn('🧪 测试模式：使用模拟数据进行测试');
        }

        try {
            // 获取锁仓天数配置
            $lockDays = SystemConfig::getValue('wallet', 'vip_wt_lock_days', 730);
            $this->info("📅 锁仓天数: {$lockDays} 天");
            $this->info("💱 转换比例: 1 BiToken = " . self::CONVERSION_RATE . " WT");
            $this->info("📊 最小BiToken阈值: {$minBiToken}");

            // 获取BiToken数据
            $this->info('📡 正在获取BiToken数据...');
            $biTokenData = $testMode ? $this->getTestBiTokenData() : $this->getBiTokenData();
            
            if (empty($biTokenData)) {
                $this->error('❌ 未获取到BiToken数据');
                return 1;
            }

            $this->stats['total_users'] = count($biTokenData);
            $this->info("👥 获取到 {$this->stats['total_users']} 个用户的BiToken数据");

            // 处理转换
            $this->processConversions($biTokenData, $lockDays, $minBiToken, $userId, $isDryRun);

            // 显示统计信息
            $this->displayStats($isDryRun);

            return 0;
        } catch (Exception $e) {
            $this->error("❌ 转换过程中发生错误: {$e->getMessage()}");
            Log::error('BiToken转换失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * 获取BiToken数据
     */
    protected function getBiTokenData(): array
    {
        try {
            $result = AiWikiUserCenter::user()->getAllCoin();

            if (!$result->isSuccess()) {
                throw new Exception('获取BiToken数据失败: ' . $result->getMessage());
            }

            $data = $result->toArray();
            return $data['coins'] ?? [];
        } catch (Exception $e) {
            throw new Exception('调用用户中心API失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取测试BiToken数据
     */
    protected function getTestBiTokenData(): array
    {
        // 获取一些有center_key的用户作为测试数据
        $users = User::whereNotNull('center_key')
            ->limit(5)
            ->get();

        $testData = [];
        foreach ($users as $user) {
            // 生成随机的BiToken数量用于测试
            $testData[$user->center_key] = rand(1, 100) / 10; // 0.1 到 10.0 之间的随机数
        }

        $this->info('🧪 生成测试数据: ' . json_encode($testData, JSON_UNESCAPED_UNICODE));

        return $testData;
    }

    /**
     * 处理转换
     */
    protected function processConversions(array $biTokenData, int $lockDays, float $minBiToken, ?string $userId, bool $isDryRun): void
    {
        $progressBar = $this->output->createProgressBar(count($biTokenData));
        $progressBar->start();

        foreach ($biTokenData as $centerKey => $biTokenAmount) {
            $progressBar->advance();
            
            // 过滤条件检查
            if ($biTokenAmount <= $minBiToken) {
                continue;
            }

            // 查找用户
            $user = User::where('center_key', $centerKey)->first();
            if (!$user) {
                continue;
            }

            // 如果指定了用户ID，只处理该用户
            if ($userId && $user->id != $userId) {
                continue;
            }

            $this->stats['processed_users']++;
            
            try {
                if ($isDryRun) {
                    $this->previewConversion($user, $biTokenAmount, $lockDays);
                } else {
                    $this->executeConversion($user, $centerKey, $biTokenAmount, $lockDays);
                }
                
                $this->stats['success_count']++;
                $this->stats['total_bitoken'] += $biTokenAmount;
                $this->stats['total_locked_wt'] += $biTokenAmount * self::CONVERSION_RATE;
                
            } catch (Exception $e) {
                $this->stats['failed_count']++;
                $this->error("\n❌ 用户 {$user->username} 转换失败: {$e->getMessage()}");
                
                Log::error('BiToken转换失败', [
                    'user_id'      => $user->id,
                    'center_key'   => $centerKey,
                    'bitoken'      => $biTokenAmount,
                    'error'        => $e->getMessage()
                ]);
            }
        }

        $progressBar->finish();
        $this->newLine();
    }

    /**
     * 预览转换信息
     */
    protected function previewConversion(User $user, float $biTokenAmount, int $lockDays): void
    {
        $lockedWtAmount = $biTokenAmount * self::CONVERSION_RATE;
        $expiredAt = now()->addDays($lockDays)->format('Y-m-d H:i:s');

        $this->line("\n👤 用户: {$user->username} (ID: {$user->id})");
        $this->line("💰 BiToken: {$biTokenAmount}");
        $this->line("🔒 将转换为锁仓WT: {$lockedWtAmount}");
        $this->line("⏰ 锁仓到期时间: {$expiredAt}");
    }

    /**
     * 执行转换
     */
    protected function executeConversion(User $user, string $centerKey, float $biTokenAmount, int $lockDays): void
    {
        $lockedWtAmount = $biTokenAmount * self::CONVERSION_RATE;

        DB::transaction(function () use ($user, $centerKey, $biTokenAmount, $lockedWtAmount, $lockDays) {
            // 1. 创建转换记录
            $conversionRecord = BiTokenConversionRecord::create([
                'user_id'          => $user->id,
                'center_key'       => $centerKey,
                'bitoken_amount'   => $biTokenAmount,
                'locked_wt_amount' => $lockedWtAmount,
                'conversion_rate'  => self::CONVERSION_RATE,
                'status'           => BiTokenConversionRecord::STATUS_PENDING,
            ]);

            try {
                // 2. 在coins账户中增加锁仓WT
                $user->account->exec('bitoken_convert', $lockedWtAmount, null, [
                    'type'                     => 'bitoken_conversion',
                    'bitoken_amount'           => $biTokenAmount,
                    'conversion_rate'          => self::CONVERSION_RATE,
                    'conversion_record_id'     => $conversionRecord->id,
                    'remark'                   => "BiToken转换锁仓WT：{$biTokenAmount} BiToken → {$lockedWtAmount} WT"
                ]);

                // 3. 创建锁仓记录
                $expiredAt = now()->addDays($lockDays);
                $lockedWt = LockedWt::create([
                    'user_id'          => $user->id,
                    'amount'           => $lockedWtAmount,
                    'remaining_amount' => $lockedWtAmount,
                    'expired_at'       => $expiredAt,
                    'status'           => LockedWt::STATUS_LOCKED,
                ]);

                // 4. 创建操作记录
                $lockedWtService = new LockedWtService();
                $lockedWtService->createOperationRecord(
                    $user->id,
                    $lockedWt->id,
                    LockedWtOperation::TYPE_BITOKEN_CONVERSION,
                    $lockedWtAmount,
                    0,
                    $lockedWtAmount,
                    null,
                    "BiToken转换：{$biTokenAmount} BiToken → {$lockedWtAmount} WT",
                    [
                        'bitoken_amount'       => $biTokenAmount,
                        'conversion_rate'      => self::CONVERSION_RATE,
                        'conversion_record_id' => $conversionRecord->id,
                        'lock_days'            => $lockDays,
                    ]
                );

                // 5. 从用户中心扣除BiToken
                $deductResult = AiWikiUserCenter::user()->addBalance(
                    $centerKey,
                    'coin',
                    -$biTokenAmount,
                    "BiToken转换锁仓WT：{$biTokenAmount} BiToken → {$lockedWtAmount} WT",
                    '',
                    [
                        'conversion_type'      => 'bitoken_to_locked_wt',
                        'locked_wt_amount'     => $lockedWtAmount,
                        'conversion_rate'      => self::CONVERSION_RATE,
                        'conversion_record_id' => $conversionRecord->id,
                        'local_user_id'        => $user->id,
                    ]
                );

                if (!$deductResult->isSuccess()) {
                    throw new \Exception('扣除用户中心BiToken失败: ' . $deductResult->getMessage());
                }

                // 6. 更新转换记录，标记为完成并记录扣除结果
                $conversionRecord->update([
                    'status'               => BiTokenConversionRecord::STATUS_COMPLETED,
                    'locked_wt_id'         => $lockedWt->id,
                    'center_deduct_result' => $deductResult->toArray(),
                    'center_deducted'      => true,
                ]);

                Log::info('BiToken转换成功', [
                    'user_id'              => $user->id,
                    'username'             => $user->username,
                    'bitoken_amount'       => $biTokenAmount,
                    'locked_wt_amount'     => $lockedWtAmount,
                    'locked_wt_id'         => $lockedWt->id,
                    'conversion_record_id' => $conversionRecord->id,
                    'expired_at'           => $expiredAt->format('Y-m-d H:i:s'),
                    'center_deduct_result' => $deductResult->toArray()
                ]);

            } catch (Exception $e) {
                // 标记转换记录为失败，并记录详细错误信息
                $conversionRecord->update([
                    'status'        => BiTokenConversionRecord::STATUS_FAILED,
                    'error_message' => $e->getMessage(),
                ]);
                throw $e;
            }
        });
    }

    /**
     * 显示统计信息
     */
    protected function displayStats(bool $isDryRun): void
    {
        $this->newLine();
        $this->info('📊 转换统计信息:');
        $this->table(
            ['项目', '数量'],
            [
                ['总用户数', $this->stats['total_users']],
                ['处理用户数', $this->stats['processed_users']],
                ['成功数量', $this->stats['success_count']],
                ['失败数量', $this->stats['failed_count']],
                ['总BiToken', number_format($this->stats['total_bitoken'], 8)],
                ['总锁仓WT', number_format($this->stats['total_locked_wt'], 8)],
            ]
        );

        if ($isDryRun) {
            $this->warn('⚠️  以上为预览信息，未实际执行转换');
            $this->info('💡 要执行实际转换，请移除 --dry-run 参数');
        } else {
            $this->info('✅ BiToken转锁仓WT转换任务完成！');
        }
    }
}
