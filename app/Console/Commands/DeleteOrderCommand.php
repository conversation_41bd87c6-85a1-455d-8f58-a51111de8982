<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\User\Enums\IdentityOrderStatus;
use Modules\User\Models\IdentityOrder;

class DeleteOrderCommand extends Command
{
    protected $signature = 'aiwiki:clear-order';

    #描述
    protected $description = '定时清除平台无效订单';

    public function handle(): void
    {
        IdentityOrder::where('status', IdentityOrderStatus::UNPAY)
            ->where('created_at', '<', now()->subMinutes(30))
            ->delete();
    }
}