<?php

namespace App\Console\Commands\Shinami;

use App\Enums\WtStatus;
use App\Models\WalletOrder;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class AutoClearOrderCommand extends Command
{
    protected $signature = 'wallet:clear-unpaid-orders {--hours=24 : 超时时间(小时)}';

    protected $description = '自动取消超过指定时间未支付的WT订单';

    public function handle(): void
    {
        $hours      = $this->option('hours');
        $cutoffTime = Carbon::now()->subHours($hours);

        $this->info("开始查询超过{$hours}小时未支付的WT订单");

        // 查询超过指定时间未支付的订单
        $unpaidOrders = WalletOrder::where('status', WtStatus::UNPAY)
            ->where('created_at', '<', $cutoffTime)
            ->get();

        $totalOrders = $unpaidOrders->count();

        if ($totalOrders === 0) {
            $this->info('没有找到需要取消的订单');
            return;
        }

        $this->info("找到 {$totalOrders} 个需要取消的订单");

        if ($this->confirm("是否开始取消这些订单？")) {
            // 初始化进度条
            $bar = $this->output->createProgressBar($totalOrders);
            $bar->setFormat(
                '当前%current%/%max% [%bar%] %percent:3s%% '.
                "\n耗时: %elapsed:6s%/%estimated:-6s% ".
                "\n内存: %memory:6s%"
            );

            $bar->start();

            $processedCount = 0;

            foreach ($unpaidOrders as $order) {
                // 更新订单状态为失败（已取消）
                $order->status = WtStatus::CANCEL;
                $order->save();

                $processedCount++;
                $bar->advance();
            }

            $bar->finish();
            $this->newLine(2);

            $this->info("订单取消完成:");
            $this->info("总处理订单数: {$processedCount}");
        } else {
            $this->info('操作已取消');
        }
    }
}
