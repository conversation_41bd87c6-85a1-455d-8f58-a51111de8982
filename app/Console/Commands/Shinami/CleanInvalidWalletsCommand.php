<?php

namespace App\Console\Commands\Shinami;

use App\Models\User;
use App\Models\WalletTransaction;
use Illuminate\Console\Command;

class CleanInvalidWalletsCommand extends Command
{
    protected $signature = 'wallet:clean-invalid {--chunk=100 : 每批处理的用户数量}';

    protected $description = '清理不符合条件的用户钱包';

    public function handle(): void
    {
        $chunkSize = $this->option('chunk');

        $this->info('开始查询需要清理的钱包...');

        // 查询有钱包的用户总数
        $totalUsers = User::whereHas('wallet')->count();

        if ($totalUsers === 0) {
            $this->info('没有找到需要清理的钱包');
            return;
        }

        $this->info("找到 {$totalUsers} 个有钱包的用户");

        if ($this->confirm("是否开始清理不符合条件的钱包？")) {
            // 初始化进度条
            $bar = $this->output->createProgressBar($totalUsers);
            $bar->setFormat(
                '当前%current%/%max% [%bar%] %percent:3s%% '.
                "\n进度: 批次:%currentBatch% ".
                "\n耗时: %elapsed:6s%/%estimated:-6s% ".
                "\n内存: %memory:6s%"
            );

            $bar->start();

            $processedCount = 0;
            $batchCount     = 0;
            $deletedCount   = 0;

            // 分批处理用户
            User::whereHas('wallet')
                ->chunkById($chunkSize, function ($users) use (&$processedCount, &$batchCount, &$deletedCount, $bar) {
                    $batchCount++;
                    $bar->setMessage((string) $batchCount, 'currentBatch');

                    foreach ($users as $user) {
                        // 检查用户是否是会员
                        $isMember = $user->identities()->where('identity_id', '>', 1)->exists();

                        // 检查用户是否有转账记录
                        $hasTransactions = WalletTransaction::where('from_user_id', $user->id)
                            ->orWhere('to_user_id', $user->id)
                            ->exists();

                        // 如果既不是会员也没有转账记录，删除钱包
                        if (! $isMember && ! $hasTransactions) {
                            $user->wallet->delete();
                            $deletedCount++;
                        }
                    }

                    $processedCount += count($users);
                    $bar->advance(count($users));
                });

            $bar->finish();
            $this->newLine(2);

            $this->info("钱包清理完成:");
            $this->info("总处理用户数: {$processedCount}");
            $this->info("删除钱包数: {$deletedCount}");
            $this->info("总批次数: {$batchCount}");
        } else {
            $this->info('操作已取消');
        }
    }
}