<?php

namespace App\Console\Commands\Shinami;

use App\Jobs\Shinami\CreateUserWalletJob;
use App\Models\User;
use Bus;
use Illuminate\Console\Command;

class GenerateMissingWalletsCommand extends Command
{
    protected $signature = 'wallet:generate-missing {--chunk=100 : 每批处理的用户数量}';

    protected $description = '查询没有钱包的会员用户并为他们生成钱包';

    public function handle(): void
    {
        $chunkSize = $this->option('chunk');

        $this->info('开始查询没有钱包的会员用户...');

        // 查询没有钱包的会员用户总数
        $totalUsers = User::query()
            ->where(function ($query) {
                $query->whereDoesntHave('wallet')
                    ->orWhereHas('wallet', function ($q) {
                        $q->whereNull('wallet_id');
                    });
            })
            ->whereHas('identities', function ($query) {
                $query->where('identity_id', '>', 1);
            })
            ->count();

        if ($totalUsers === 0) {
            $this->info('没有找到需要创建钱包的会员用户');
            return;
        }

        $this->info("找到 {$totalUsers} 个需要创建钱包的会员用户");

        if ($this->confirm("是否开始为这些会员用户创建钱包？")) {
            // 初始化进度条，使用查询到的用户总数作为初始值
            $bar = $this->output->createProgressBar($totalUsers);

            // 设置显示格式
            $bar->setFormat(
                '当前%current%/%max% [%bar%] %percent:3s%% '.
                "\n进度: 批次:%currentBatch% ".
                "\n耗时: %elapsed:6s%/%estimated:-6s% ".
                "\n内存: %memory:6s%"
            );

            $bar->start();

            $processedCount = 0;
            $batchCount     = 0;

            // 分批处理用户
            User::query()
                ->where(function ($query) {
                    $query->whereDoesntHave('wallet')
                        ->orWhereHas('wallet', function ($q) {
                            $q->whereNull('wallet_id');
                        });
                })
                ->whereHas('identities', function ($query) {
                    $query->where('identity_id', '>', 1);
                })
                ->chunkById($chunkSize, function ($users) use (&$processedCount, &$batchCount, $bar) {
                    $batchCount++;
                    $bar->setMessage((string) $batchCount, 'currentBatch');

                    // 创建任务数组
                    $jobs = [];

                    foreach ($users as $user) {
                        // 如果用户已有钱包，跳过
                        if ($user->wallet && ! empty($user->wallet->wallet_id)) {
                            continue;
                        }

                        // 再次验证会员身份
                        if (! $user->identities()->where('identity_id', '>', 1)->exists()) {
                            continue;
                        }

                        // 添加到队列任务
                        $jobs[] = new CreateUserWalletJob($user);
                    }

                    // 分发任务到队列
                    if (! empty($jobs)) {
                        Bus::batch($jobs)
                            ->name("创建钱包批次 {$batchCount}")
                            ->onQueue('shinami')
                            ->allowFailures()
                            ->dispatch();
                    }

                    // 只计算实际添加到队列的用户数
                    $jobCount       = count($jobs);
                    $processedCount += $jobCount;

                    // 前进进度条
                    $bar->advance($jobCount);
                });

            $bar->finish();
            $this->newLine(2);

            $this->info("批量创建钱包任务已分发到队列:");
            $this->info("总处理用户数: {$processedCount}");
            $this->info("总批次数: {$batchCount}");
            $this->info("可以在后台管理面板中查看队列任务执行情况");
        } else {
            $this->info('操作已取消');
        }
    }
}