<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('horizon:snapshot')->everyFiveMinutes();
        $schedule->command('aiwiki:clear-order')->everyMinute();
        $schedule->command('wallet:clear-unpaid-orders')->everyMinute();
    }

    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');
//        require base_path('routes/console.php');
    }
}
