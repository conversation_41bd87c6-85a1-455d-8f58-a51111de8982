<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Payment\Models\AccountRule;

class ScoreAddEvent
{
    use Dispatchable,
        SerializesModels;

    /**
     * @param  \App\Models\User  $user
     * @param  \Modules\Payment\Models\AccountRule|string  $rule
     * @param  array  $data
     */
    public function __construct(public User $user, public AccountRule|string $rule, public array $data)
    {
    }
}