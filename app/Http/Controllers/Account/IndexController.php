<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\ApiController;
use App\Http\Requests\User\TradingRequest;
use App\Models\User;
use App\Models\UserTransfer;
use App\Models\WalletTransaction;
use App\Packages\AiWikiUserCenter\AiWikiUserCenter;
use App\Packages\Shinami\ShinamiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;

class IndexController extends ApiController
{
    public function balance(Request $request)
    {
        View::share('sub_title', lecho('My wallet'));
        View::share('menu', 'balance');
        $user = Auth::user();
        if ($this->needEmail($user)) {
            return view('email.need');
        }

        $account = $user->getCenterAccountAll('balance');
        $usd     = $user->getCenterAccountAll('usd');
        $where   = [
            'amount' => [
                'operator' => '>',
                'value'    => 0
            ],
        ];
        if ($request->sub == 1) {
            $where = [
                'amount' => [
                    'operator' => '<',
                    'value'    => 0
                ],
            ];
        }
        $result = AiWikiUserCenter::user()->getList(
            $user->center_key,
            $request->usd ? 'usd' : 'balance',
            $request->page ?: 1,
            10,
            $where);
        $pages  = [];
        $logs   = [];
        if ($result->isSuccess()) {
            $logs  = $result->toArray()['data'];
            $pages = $result->toArray()['page'];
        }
        return view('account.balance', [
            'account' => $account,
            'user'    => $user,
            'usd'     => $usd,
            'logs'    => $logs,
            'pages'   => $pages,
        ]);
    }

    public function coin(Request $request)
    {
        View::share('sub_title', lecho('My BitToken'));
        View::share('menu', 'coin');
        $user = Auth::user();
        if ($this->needEmail($user)) {
            return view('email.need');
        }

        $account = $user->getCenterAccountAll('coin');
        $where   = [
            'amount' => [
                'operator' => '>',
                'value'    => 0
            ],
        ];
        if ($request->sub == 1) {
            $where = [
                'amount' => [
                    'operator' => '<',
                    'value'    => 0
                ],
            ];
        }
        $result = AiWikiUserCenter::user()->getList(
            $user->center_key,
            'coin',
            $request->page ?: 1,
            10,
            $where);
        $pages  = [];
        $logs   = [];
        if ($result->isSuccess()) {
            $logs  = $result->toArray()['data'];
            $pages = $result->toArray()['page'];
        }
        return view('account.coin', [
            'account' => $account,
            'logs'    => $logs,
            'pages'   => $pages,
        ]);
    }

    /**
     * Notes: wt币
     *
     * @Author: 玄尘
     * @Date: 2025/4/23 14:56
     * @param  \Illuminate\Http\Request  $request
     */
    public function wt(Request $request)
    {
        View::share('sub_title', lecho('My WtToken'));
        View::share('menu', 'wt');
        $user = Auth::user();

        if ($this->needWallet($user)) {
            return view('wallet.need');
        }

        $balance = $user->getWTBalance();
        $sub     = $request->sub ?? 2;
        $logs    = $pages = WalletTransaction::query()
            ->whereNotIn('transaction_type', [WalletTransaction::TRANSACTION_TYPE_USDC])
            ->where(function ($query) use ($user) {
                $query->where('from_user_id', $user->id)
                    ->orWhere('to_user_id', $user->id);
            })
            ->when($sub, function ($query) use ($sub, $user) {
                //支出
                if ($sub == 1) {
                    $query->where('from_user_id', $user->id);
                } else {
                    $query->where('to_user_id', $user->id);
                }
            })
            ->latest('id')
            ->paginate();

        return view('account.wt', [
            'balance'            => $balance,
            'logs'               => $logs,
            'pages'              => $pages,
            'canConvertWTToUSDC' => $user->canConvertWTToUSDC(),
        ]);
    }

    public function transfer(TradingRequest $request)
    {
        $user = Auth::user();
        if (! in_array($request->type, UserTransfer::TYPES)) {
            return $this->failed(lecho('Transfer out account error'));
        }
        if (! $user->security || ! $user->security->verify($request->password)) {
            return $this->failed(lecho('Payment password not set or incorrect payment password'));
        }
        $toUserName = $request->to_username;
        if (is_numeric($toUserName)) {
            $toUser = User::where('id', $toUserName)->first();
        } else {
            $toUser = User::where('username', $toUserName)
                ->orWhere('email', $toUserName)->first();
        }
        if (! $toUser) {
            return $this->failed(lecho('The target user does not exist or has not activated their account yet'));
        }
        if ($toUser->id == $user->id) {
            return $this->failed(lecho('Cannot transfer money to oneself'));
        }
        if ($toUser && $toUser->center_key) {
            $log = UserTransfer::create([
                'from_user_id' => $user->id,
                'to_user_id'   => $toUser->id,
                'type'         => $request->type,
                'class'        => 'Transfer',
                'amount'       => $request->amount,
            ]);
            if ($log) {
                return $this->success([
                    'log_id'  => $log->id,
                    'message' => lecho('The order was successfully created, please wait for the result'),
                ]);
            } else {
                return $this->failed(lecho('Failed to create transaction'));
            }
        } else {
            return $this->failed(lecho('The target user does not exist or has not activated their account yet'));
        }
    }

    /**
     * Notes: wt转账
     *
     * @Author: 玄尘
     * @Date: 2025/4/23 15:06
     * @param  \App\Http\Requests\User\TradingRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function wtTransfer(TradingRequest $request)
    {
        try {
            $user = Auth::user();

            if ($request->type != WalletTransaction::TOKEN_TYPE_WIKE_TOKEN) {
                return $this->failed(lecho('Transfer out account error'));
            }

            if (! $user->security || ! $user->security->verify($request->password)) {
                return $this->failed(lecho('Payment password not set or incorrect payment password'));
            }

            $toUserName = $request->to_username;
            if (is_numeric($toUserName)) {
                $toUser = User::where('id', $toUserName)->first();
            } else {
                $toUser = User::where('username', $toUserName)
                    ->orWhere('email', $toUserName)->first();
            }
            if (! $toUser) {
                return $this->failed(lecho('The target user does not exist or has not activated their account yet'));
            }
            if ($toUser->id == $user->id) {
                return $this->failed(lecho('Cannot transfer money to oneself'));
            }
            if (! $toUser->wallet || empty($toUser->wallet->wallet_address)) {
                return $this->failed(lecho('The target user does not exist or has not activated their account yet'));
            }
            if ($toUser->wallet->wallet_address == $user->wallet->wallet_address) {
                return $this->failed(lecho('Cannot transfer money to oneself'));
            }
            if (! $user->canConvertWTToUSDC()) {
                return $this->failed(lecho('no_permission'));
            }

            $transfer = $user->transferToAddress(
                $toUser->wallet->wallet_address,
                $request->type,
                $request->amount,
                WalletTransaction::TRANSACTION_TYPE_TRANSFER,
                ['to_user_id' => $toUser->id]
            );

            if ($transfer instanceof ShinamiResponse) {
                if ($transfer->isError()) {
                    return $this->failed($transfer->getMessage());
                }
            }
            return $this->success([
                'log_id'  => $transfer->id,
                'message' => lecho('The order was successfully created, please wait for the result'),
            ]);
        } catch (\Exception $e) {
            return $this->failed($e->getMessage());
        }
    }

    public function result(UserTransfer $transfer)
    {
        if ($transfer->status == UserTransfer::STATUS_SUCCESS) {
            return $this->success([
                'code'    => 1,
                'message' => lecho('Transfer successful'),
            ]);
        } elseif ($transfer->status == UserTransfer::STATUS_ERROR) {
            return $this->success([
                'code'    => 2,
                'message' => $transfer->remark,
            ]);
        } else {
            return $this->success([
                'code'      => 0,
                'new_token' => csrf_token(),
            ]);
        }
    }

    public function wtResult(WalletTransaction $transfer)
    {
        if (in_array($transfer->status, [WalletTransaction::STATUS_COMPLETED])) {
            return $this->success([
                'code'    => 1,
                'message' => lecho('Transfer successful'),
            ]);
        } elseif (in_array($transfer->status, [WalletTransaction::STATUS_FAILED])) {
            return $this->success([
                'code'    => 2,
                'message' => $transfer->error_message ?? '转账失败',
            ]);
        } else {
            return $this->success([
                'code'      => 0,
                'new_token' => csrf_token(),
            ]);
        }
    }
}
