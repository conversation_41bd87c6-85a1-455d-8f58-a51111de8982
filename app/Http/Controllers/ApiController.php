<?php

namespace App\Http\Controllers;

use App\Facades\Api;
use App\Models\User;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\View;
use Modules\Cms\Models\Category;

abstract class ApiController extends Controller
{
    public function __construct()
    {
        View::share('categories', Category::ofEnabled()
            ->where('parent_id', 1)
            ->orderBy('order')
            ->get());
    }

    public function checkPassWord(string $username, string $password, string $attr = 'username')
    {
        $user = User::query()->whereRaw(" LOWER({$attr}) = LOWER('{$username}')")->first();
        if ($user) {
            $type = env('AUTH_PASSWORD_TYPE');
            $res  = match ($type) {
                'hash' => password_verify($password, $user->password),
                'md5salt' => md5(md5($password).$user->salt) == $user->password,
                default => $password == $user->password,
            };

            return $res ? $user : false;
        } else {
            return false;
        }
    }

    /**
     * Notes   : 成功数据
     *
     * @Date   : 2022/12/23 21:48
     * <AUTHOR> <Jason.C>
     * @param  mixed  $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function success(mixed $data = '', string|null $callback = ''): JsonResponse
    {
        return $this->respond(
            data: is_array($data) ? $data : [],
            message: is_string($data) ? $data : 'SUCCESS',
            callback: $callback ?: '',
        );
    }

    /**
     * Notes   : 结果返回
     *
     * @Date   : 2022/12/23 21:48
     * <AUTHOR> <Jason.C>
     * @param  mixed  $data
     * @param  int  $code
     * @param  string  $message
     * @param  array  $header
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respond(
        mixed $data = '',
        int $code = 200,
        string $message = '',
        string $callback = '',
        array $header = []
    ): JsonResponse {
        $response = [
            'code'    => $code,
            'message' => $message,
            'url'     => $callback,
            'data'    => $data,
        ];

        config('app.debug') && $response['time'] = number_format((microtime(true) - LARAVEL_START) * 1000, 2);

        return Response::json($response, 200, $header);
    }

    /**
     * Notes   : 失败消息
     *
     * @Date   : 2022/12/23 21:48
     * <AUTHOR> <Jason.C>
     * @param  string  $message
     * @param  int  $code
     * @return \Illuminate\Http\JsonResponse
     */
    public function failed(
        string $message = '',
        int $code = 500,
        string $callback = '',
    ): JsonResponse {
        return $this->respond(
            code: $code,
            message: $message,
            callback: $callback,
        );
    }

    /**
     * @throws \Exception
     */
    protected function checkPermission(Model $model, string $message = 'Data does not exist')
    {
        if ($model->user->isNot(Api::user())) {
            throw new Exception($message, 404);
        }
    }

    protected function needEmail(User $user): bool
    {
        return blank($user->center_key);
    }

    protected function needWallet(User $user): bool
    {
        return blank($user->wallet);
    }
}
