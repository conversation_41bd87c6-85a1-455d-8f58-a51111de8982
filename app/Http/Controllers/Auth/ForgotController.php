<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\ApiController;
use App\Http\Requests\User\ForgotDoRequest;
use App\Http\Requests\User\ForgotEmailSendRequest;
use App\Jobs\Email\SendBladeMessageJob;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\View;

class ForgotController extends ApiController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        View::share('sub_title', lecho('Forgot password'));

        return view('auth.forgot');
    }

    public function forgot(ForgotDoRequest $request)
    {
        $email     = $request->email;
        $checkCode = Cache::get('user_forgot_set_'.$email);
        if (! $checkCode) {
            return $this->failed(lecho('The verification code has expired'));
        }
        if ($request->code != $checkCode) {
            return $this->failed(lecho('The verification code is incorrect'));
        }
        $user           = User::where('email', $email)->first();
        $user->password = $request->new_password;
        $user->save();
        return $this->success(lecho('Successfully'), route('login'));
    }

    public function email(ForgotEmailSendRequest $request)
    {
        $code = sprintf("%06d", mt_rand(1, 999999));
        Cache::set('user_forgot_set_'.$request->send_mail, $code, 300);
        SendBladeMessageJob::dispatch($request->send_mail, lecho('Dear user'),
            lecho('Forgot_Account_Password_Verification_Code'),
            view('email.forgot', ['code' => $code, 'mail' => $request->send_mail]));
        return $this->success([
            'message' => lecho('Successfully sent'),
            'email'   => $request->send_mail,
        ]);
    }
}
