<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\ApiController;
use App\Http\Requests\User\RegisterRequest;
use App\Models\SystemConfig;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Modules\User\Events\Authenticated;

class RegisterController extends ApiController
{
    public function index(string $code = '')
    {
        View::share('sub_title', lecho('register'));
        if (Auth::check()) {
            return redirect()->route('index');
        }
        return view('auth.register', [
            'code' => $code,
        ]);
    }

    public function registerDo(RegisterRequest $request)
    {
        $user    = User::create([
            'username' => $request->username,
            'password' => $request->password,
        ]);
        $message = lecho('Successfully registered');
        if ($code = $request->code) {
            $parentIds = app('user.hashids')->decode($code);
            if (count($parentIds) > 0) {
                try {
                    $user->relation->changeParent($parentIds[0]);
                } catch (\Exception $e) {
                    Log::channel('user')->info('--------------用户注册绑定关系失败----------------');
                    Log::channel('user')->info('UserName：'.$user->username);
                    Log::channel('user')->info('Email：'.$user->email);
                    Log::channel('user')->info('Code：'.$code);
                    Log::channel('user')->info($parentIds);
                    Log::channel('user')->info('ParentId：'.$parentIds[0]);
                    Log::channel('user')->info(lecho('Error').':'.$e->getMessage());
                    Log::channel('user')->info('--------------错误信息结束----------------');
                }
            }
        }
        Auth::login($user);
        $score = SystemConfig::getValue('score', 'register', 0);
        if ($score > 0) {
            $user->account->exec('register', $score, null, [
                'remark' => lecho('Registration gift'),
            ]);
        }
        Authenticated::dispatch($user, 'WEB');
        return $this->success($message, $request->callback ?: route('index'));
    }
}
