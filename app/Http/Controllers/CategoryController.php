<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Modules\Cms\Models\Category;
use Modules\Cms\Models\Content;
use Modules\Interaction\Enums\CommentStatus;

class CategoryController extends ApiController
{
    public function index(Category $category)
    {
        $language_type = session('language_type', config('languageDefault.key'));
        View::share('sub_title', lecho($category->name));
        $lists = $category->contents()
            ->withCount([
                'comments' => function ($query) {
                    $query->where('status', CommentStatus::NORMAL);
                }
            ])
            ->where('language', $language_type)
            ->ofEnabled()
            ->ofPassed()
            ->orderByDesc('sort')
            ->orderByDesc('created_at')
            ->paginate(10);
        $cids  = Category::where('parent_id', 1)
            ->ofEnabled()
            ->pluck('id')->toArray();

        $news = Content::ofEnabled()
//            ->whereIn('category_id', $cids ?: [])
            ->withCount([
                'comments' => function ($query) {
                    $query->where('status', CommentStatus::NORMAL);
                }
            ])
            ->where('language', $language_type)
            ->ofPassed()
            ->orderByDesc('created_at')
            ->limit(3)
            ->get();

        $popularJoin = DB::table('interaction_comments')
            ->where('commentable_type', (new Category())->getMorphClass())
            ->select('commentable_id', DB::raw('count(id) as comment_count'))
            ->groupBy('commentable_id');
        $popular     = Content::query()
//            ->whereIn('category_id', $cids ?: [])
            ->leftJoinSub($popularJoin, 'popularJoin', function ($join) {
                $join->on('cms_contents.id', '=', 'popularJoin.commentable_id');
            })
            ->where('language', $language_type)
            ->ofEnabled()
            ->ofPassed()
            ->orderByDesc('comment_count')
            ->limit(3)
            ->get();

        return view('category/index', [
            'category' => $category,
            'lists'    => $lists,
            'news'     => $news,
            'popular'  => $popular,
        ]);
    }
}
