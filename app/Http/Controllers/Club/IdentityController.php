<?php

namespace App\Http\Controllers\Club;

use App\Http\Controllers\ApiController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;
use Modules\User\Models\Identity;

class IdentityController extends ApiController
{
    public function index()
    {
        View::share('menu', 'vip');
        View::share('sub_title', lecho('My members'));

        $user     = Auth::user();
        $identity = $user->identityFirst();
        return view('identity.index', [
            'identity' => $identity,
        ]);
    }

    public function openList()
    {
        View::share('sub_title', lecho("Open membership"));
        $user = Auth::user();
        if ($this->needEmail($user)) {
            return view('email.need');
        }
        $user       = Auth::user();
        $myIdentity = $user->identityFirst();
        $identities = Identity::where('can_subscribe', 1)
            ->where('status', 1)
            ->orderBy('price', 'asc')
            ->get();
        return view('identity.open', [
            'user'       => $user,
            'identities' => $identities,
            'myIdentity' => $myIdentity,
        ]);
    }

    public function buyIdentity(Identity $identity)
    {
        if (! $identity->status) {
            return $this->failed(lecho('Invalid identity status'));
        }
        if ($identity->can_subscribe != 1) {
            return $this->failed(lecho('The current identity cannot be subscribed to'));
        }
        $user  = Auth::user();
        $order = $identity->orders()->create([
            'user_id' => $user->id,
            'qty'     => 1,
            'usd'     => $identity->price,
            'amount'  => $identity->price,
        ]);
        if ($order) {
            return $this->success(lecho('The order has been successfully created and will now proceed to the checkout counter'),
                route('payment.index', [
                    'type' => $order->getMorphClass(),
                    'no'   => $order->no,
                ]));
        } else {
            return $this->failed(lecho('Order creation failed'));
        }
    }

}
