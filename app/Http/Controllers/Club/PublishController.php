<?php

namespace App\Http\Controllers\Club;

use App\Enums\ApplyStatus;
use App\Facades\Api;
use App\Http\Controllers\ApiController;
use App\Http\Requests\User\PublishStoreRequest;
use App\Jobs\Sensitive;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Intervention\Image\Laravel\Facades\Image;
use Modules\Cms\Models\Category;
use Modules\Cms\Models\Content;
use Modules\Interaction\Enums\CommentStatus;
use Modules\Storage\Models\Upload;

class PublishController extends ApiController
{

    protected array $imageMimes = [
        'image/png',
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/bmp',
        'image/webp',
    ];

    public function __construct()
    {
        parent::__construct();
        View::share('menu', 'publish');
        config([
            'app.debug' => true,
        ]);
    }

    public function index()
    {
        View::share('sub_title', lecho('My Publish'));

        $category = Category::where('parent_id', 1)
            ->ofEnabled()
            ->orderBy('order')
            ->get();
        return view('publish/index', [
            'category' => $category,
        ]);
    }

    public function store(PublishStoreRequest $request)
    {
        $user    = Auth::user();
        $tags    = $request->tags;
        $content = $request->full_content;
        if (Str::length(strip_tags($content)) < 30) {
            return $this->failed(lecho('The minimum length of content is 30 characters'));
        }
        $tags = explode(",", $tags);
        if (count($tags) > 5) {
            return $this->failed(lecho('The number of tags cannot exceed').' 5');
        }

        $files = $request->file('files') ?: [];

        $pictures = [];
        $pathBase = date('Y/m/d');
        foreach ($files as $file) {
            $hash      = File::hash($file);
            $existFile = Upload::where('hash', $hash)->first();
            if (! $existFile) {
                $extension = $file->guessClientExtension() ?? 'jpg';
                $name      = sprintf('%s.%s', $hash, $extension);
                $path      = sprintf('%s/%s', $pathBase, $name);
                if (Storage::putFileAs($pathBase, $file, $name)) {
                    if (config('storage.AUTO_MAKE_THUMB') && in_array($file->getMimeType(), $this->imageMimes)) {
                        $thumb     = Image::read($file)
                            ->resize(config('admin.thumb_size'), config('admin.thumb_size'));
                        $thumbName = sprintf('%s/%s-thumb.%s', $pathBase, $hash, $extension);
                        $imageData = match ($file->getMimeType()) {
                            'image/png' => $thumb->toPng(),
                            'image/jpg' => $thumb->toJpeg(),
                            'image/jpeg' => $thumb->toJpeg(),
                            'image/gif' => $thumb->toGif(),
                            'image/bmp' => $thumb->toBitmap(),
                            'image/webp' => $thumb->toWebp(),
                        };
                        Storage::put($thumbName, $imageData);
                    }
                    Upload::create([
                        'user_id'  => Api::id(),
                        'hash'     => $hash,
                        'size'     => File::size($file),
                        'type'     => $file->getClientMimeType(),
                        'original' => $file->getClientOriginalName(),
                        'disk'     => config('filesystems.default'),
                        'path'     => $path,
                    ]);
                    $pictures[] = $path;
                }
            } else {
                $pictures[] = $existFile->path;
            }
        }
        $cms = Content::create([
            'reviewer_type' => $user->getMorphClass(),
            'reviewer_id'   => $user->getKey(),
            'category_id'   => $request->category_id ?: 0,
            'title'         => $request->title,
            'language'      => session('language_type', config('languageDefault.key')),
            'description'   => $request->blog_description ?: Str::limit(strip_tags($request->full_content), 100, ''),
            'sub_title'     => $tags,
            'content'       => $request->full_content,
            'cover'         => $pictures[0] ?? '',
            'pictures'      => $pictures,
            'apply_status'  => ApplyStatus::REVIEW->value,
        ]);
        if (env('AUTH_CONTENT', false)) {
            Sensitive::dispatch($cms);
        } else {
            $cms->sensitivePass();
        }

        return $this->success(lecho('Published successfully, please wait for the system\'s review'),
            route('MyPublishIndex'));
    }

    public function myPublish()
    {
        View::share('sub_title', lecho('My Publish'));
        $user   = Auth::user();
        $total  = [
            'init'   => $user->blogs()->where('apply_status', ApplyStatus::INIT->value)->count(),
            'review' => $user->blogs()->where('apply_status', ApplyStatus::REVIEW->value)->count(),
            'pass'   => $user->blogs()->where('apply_status', ApplyStatus::PASS->value)->count(),
            'reject' => $user->blogs()->where('apply_status', ApplyStatus::REJECT->value)->count(),
        ];
        $status = request('status', '');
        $blogs  = $user->blogs()
            ->withCount([
                'comments' => function ($query) {
                    $query->where('status', CommentStatus::NORMAL);
                },
                'likes',
                'favorites'
            ])
            ->when($status, function (Builder $builder, $status) {
                $builder->where('apply_status', $status);
            })
            ->orderByDesc('created_at')
            ->paginate(10);
        return view('publish/my_publish', [
            'total' => $total,
            'blogs' => $blogs,
        ]);
    }

    public function edit(Content $item)
    {
        if ($item->reviewer->id != Auth::id()) {
            return view('layout.404');
        }
        View::share('sub_title', lecho('My Publish'));

        $category = Category::where('parent_id', 1)
            ->ofEnabled()
            ->orderBy('order')
            ->get();
        return view('publish/edit', [
            'category' => $category,
            'item'     => $item,
        ]);
    }

    public function editDo(Content $item, PublishStoreRequest $request)
    {
        $tags    = $request->tags;
        $tags    = explode(",", $tags);
        $content = $request->full_content;
        if (Str::length(strip_tags($content)) < 30) {
            return $this->failed(lecho('The minimum length of content is 30 characters'));
        }
        if (count($tags) > 5) {
            return $this->failed(lecho('The number of tags cannot exceed').' 5');
        }

        $files = $request->file('files') ?: [];
        
        $pictures = [];
        $pics     = $request->pics ?: [];
        foreach ($pics as $pic) {
            $pictures[] = $pic;
        }
        $pathBase = date('Y/m/d');
        foreach ($files as $file) {
            $hash      = File::hash($file);
            $existFile = Upload::where('hash', $hash)->first();
            if (! $existFile) {
                $extension = $file->guessClientExtension() ?? 'jpg';
                $name      = sprintf('%s.%s', $hash, $extension);
                $path      = sprintf('%s/%s', $pathBase, $name);
                if (Storage::putFileAs($pathBase, $file, $name)) {
                    if (config('storage.AUTO_MAKE_THUMB') && in_array($file->getMimeType(), $this->imageMimes)) {
                        $thumb     = Image::read($file)
                            ->resize(config('admin.thumb_size'), config('admin.thumb_size'));
                        $thumbName = sprintf('%s/%s-thumb.%s', $pathBase, $hash, $extension);
                        $imageData = match ($file->getMimeType()) {
                            'image/png' => $thumb->toPng(),
                            'image/jpg' => $thumb->toJpeg(),
                            'image/jpeg' => $thumb->toJpeg(),
                            'image/gif' => $thumb->toGif(),
                            'image/bmp' => $thumb->toBitmap(),
                            'image/webp' => $thumb->toWebp(),
                        };
                        Storage::put($thumbName, $imageData);
                    }
                    Upload::create([
                        'user_id'  => Api::id(),
                        'hash'     => $hash,
                        'size'     => File::size($file),
                        'type'     => $file->getClientMimeType(),
                        'original' => $file->getClientOriginalName(),
                        'disk'     => config('filesystems.default'),
                        'path'     => $path,
                    ]);
                    $pictures[] = $path;
                }
            } else {
                $pictures[] = $existFile->path;
            }
        }

        $item->update([
            'category_id'  => $request->category_id ?: 0,
            'title'        => $request->title,
            'description'  => $request->blog_description ?: Str::limit(strip_tags($request->full_content), 100, ''),
            'sub_title'    => $tags,
            'content'      => $request->full_content,
            'cover'        => $pictures[0] ?? '',
            'pictures'     => $pictures,
            'status'       => 0,
            'apply_status' => ApplyStatus::REVIEW->value,
        ]);
        $item->sensitive()->delete();
        $item->fresh();
        if (env('AUTH_CONTENT')) {
            Sensitive::dispatch($item);
        } else {
            $item->sensitivePass();
        }

        return $this->success(lecho('Published successfully, please wait for the system\'s review'),
            route('MyPublishIndex'));
    }

    public function delete(Content $cms)
    {
        $user = Auth::user();
        if ($cms->reviewer != $user) {
            return $this->failed(lecho('illegal operation'));
        }
        $cms->delete();
        return $this->success(lecho('Operation successful'));
    }
}
