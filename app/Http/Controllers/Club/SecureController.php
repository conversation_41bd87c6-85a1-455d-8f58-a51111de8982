<?php

namespace App\Http\Controllers\Club;

use App\Http\Controllers\ApiController;
use App\Http\Requests\User\RepeatLoginPasswordRequest;
use App\Http\Requests\User\ResetPaymentSecureRequest;
use App\Http\Requests\User\SetPaymentSecureRequest;
use App\Jobs\Email\SendBladeMessageJob;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\View;

class SecureController extends ApiController
{
    public function index()
    {
        View::share('sub_title', lecho('secure  setting'));
        View::share('menu', 'secure');
        return view('secure.index', [
            'user' => Auth::user(),
        ]);
    }

    public function loginRepeat(RepeatLoginPasswordRequest $request)
    {
        $user = Auth::user();
        if ($this->checkPassWord($user->username, $request->old_password)) {
            $user->password = $request->new_password;
            $user->save();
            Auth::logout();
            return $this->success(lecho('Password changed successfully, please log in again'), route('login'));
        } else {
            return $this->failed(lecho('Password verification failed'));
        }
    }

    public function sendMail(Request $request)
    {
        $user = Auth::user();
        $code = sprintf("%06d", mt_rand(1, 999999));
        Cache::set('user_secure_set_'.$user->id, $code, 300);
        SendBladeMessageJob::dispatch($user->email, $user->info->nickname,
            lecho('Set payment password verification code'),
            view('email.secure', ['code' => $code, 'user' => $user]));
        return $this->success(lecho('Successfully sent'));
    }

    public function payment(SetPaymentSecureRequest $request)
    {
        $user      = Auth::user();
        $code      = Cache::get('user_secure_set_'.$user->id);
        $checkCode = $request->email_code;
        if (! $code) {
            return $this->failed(lecho('The verification code has expired'));
        }
        if ($code != $checkCode) {
            return $this->failed(lecho('The verification code is incorrect'));
        }
        $user->security()->updateOrCreate([
            'user_id' => $user->id,
        ], [
            'password' => $request->new_pay_password,
        ]);
        Cache::delete('user_secure_set_'.$user->id);

        return $this->success(lecho('Payment password successfully set'), route('club.secure'));
    }

    public function paymentReset(ResetPaymentSecureRequest $request)
    {
        $user      = Auth::user();
        $code      = Cache::get('user_secure_set_'.$user->id);
        $checkCode = $request->email_code;
        if (! $code) {
            return $this->failed(lecho('The verification code has expired'));
        }
        if ($code != $checkCode) {
            return $this->failed(lecho('The verification code is incorrect'));
        }
//        if (! $user->security->verify($request->old_pay_password)) {
//            return $this->failed(lecho('The original payment password is incorrect'));
//        }
        $user->security()->updateOrCreate([
            'user_id' => $user->id,
        ], [
            'password' => $request->new_pay_password,
        ]);
        Cache::delete('user_secure_set_'.$user->id);
        return $this->success(lecho('Payment password successfully changed'), route('club.secure'));
    }

}
