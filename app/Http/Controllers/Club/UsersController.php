<?php

namespace App\Http\Controllers\Club;

use App\Http\Controllers\ApiController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\View;
use Modules\Cms\Models\Content;
use Modules\Interaction\Enums\CommentStatus;

class UsersController extends ApiController
{

    public function index()
    {
        $language_type = session('language_type',config('languageDefault.key'));
        $user = Auth::user();
        View::share('sub_title', lecho('User Center MainPage'));
        $user->initManyAccount([
            'usd',
            'balance',
            'coin',
        ]);
        $usd             = $user->getCenterAccountAll('usd');
        $balance         = $user->getCenterAccountAll('balance');
        $coin            = $user->getCenterAccountAll('coin');
        $identity        = $user->identityFirst();
        $subscribe_count = $user->subscribes->count();
        $cms_count       = Content::where('reviewer_id', $user->id)->count();
        $cms_read_count  = Content::where('reviewer_id', $user->id)->sum('clicks');
        $cms             = Content::OfPassed()
            ->withCount([
                'comments' => function ($query) {
                    $query->where('status', CommentStatus::NORMAL);
                },
                'likes',
                'favorites',
            ])
            ->where('reviewer_id', $user->id)
            ->take(3)
            ->latest()
            ->get();

        $notices   = Content::ofEnabled()
            ->whereHas('category', function ($query) {
                $query->where('name', 'Notice_gonggao');
            })
            ->take(3)
            ->get();

        if($language_type == 'en'){
            $hotSearch = getWikipedia();
        }else{
            $hotSearch = $this->hotsearch();
            $hotSearch = collect($hotSearch)->random(10);
        }

        return view('users/index', [
            'user'            => $user,
            'usd'             => $usd,
            'balance'         => $balance,
            'coin'            => $coin,
            'identity'        => $identity,
            'subscribe_count' => $subscribe_count,
            'cms_count'       => $cms_count,
            'cms_read_count'  => $cms_read_count,
            'cms'             => $cms,
            'notices'         => $notices,
            'lang'            => $language_type,
            'hotSearch'       => $hotSearch,
        ]);
    }

    public function hotsearch()
    {
        $data = Cache::get('hotsearch2', '');
        if (blank($data)) {
            $data   = [];
            $result = file_get_contents('https://www.toutiao.com/hot-event/hot-board/?origin=toutiao_pc&_signature=_02B4Z6wo00901VO3PtwAAIDBkBRB2riwqjVTkzpAADKARIJSircloPQoAVuZSphe-kP.e1BxjPCexsaSJCBphXLGtLW8Yn3MasIToNG7egg496.SdJh2ZpYotlnl7rp-F4bBZzIcc-xeBKBU74');
            try {
                $json = json_decode($result, true);
                if (! empty($json['data'])) {
                    foreach ($json['data'] as $vo) {
                        $data[] = $vo['Title'];
                    }
                    Cache::set('hotsearch2', $data, 6 * 3600);
                }
            } catch (\Exception $e) {
            }
        }
        return $data;
    }
}
