<?php

namespace App\Http\Controllers;

use App\Models\ChatGroup;
use App\Packages\WateAiChat\WateAiChat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class DeepchatController extends ApiController
{
    public function index()
    {
        return view('deepchat/index');
    }

    public function chat(Request $request)
    {
        $message = $request->message;
        $groupId = $request->group_id;
        if (blank($message)) {
            echo json_encode([
                'type' => 'error',
                'msg'  => '请输入内容',
                'data' => '',
            ]);
        }

        $user    = Auth::user();
        $channel = 'deepseek';
        if (! $message) {
            return $request->kernel->error('请输入您的聊天内容');
        }
        $group = ChatGroup::ofUser($user->id ?? 0)->where('id', $groupId)->first();
        if (! $group) {
            $group = ChatGroup::create([
                'user_id' => $user->id ?? 0,
                'name'    => Str::limit($message, 20),
            ]);
        }
        $wateAi = WateAiChat::make($channel, $group, $user);
        $wateAi->send($message);
    }

}
