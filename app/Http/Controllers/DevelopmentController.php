<?php

namespace App\Http\Controllers;


use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;

use Modules\Cms\Models\Category;
use Modules\Interaction\Enums\CommentStatus;
class DevelopmentController extends ApiController
{
    public function index()
    {
        $category = Category::find(965899);

        $language_type = session('language_type', config('languageDefault.key'));
        View::share('sub_title', lecho($category->name));
        $lists = $category->contents()
            ->withCount([
                'comments' => function ($query) {
                    $query->where('status', CommentStatus::NORMAL);
                }
            ])
            ->where('language', $language_type)
            ->ofEnabled()
            ->ofPassed()
            ->orderByDesc('sort')
            ->orderByDesc('created_at')
            ->paginate(10);


        return view('dev/index', [
            'category' => $category,
            'lists'    => $lists,
        ]);
    }

}
