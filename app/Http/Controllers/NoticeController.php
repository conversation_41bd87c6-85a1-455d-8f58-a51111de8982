<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\View;
use Modules\Cms\Models\Content;

class NoticeController extends ApiController
{
    public function index()
    {
        View::share('sub_title', lecho('Notice_gonggao'));

        $notices = Content::ofEnabled()
            ->whereHas('category', function ($query) {
                $query->where('name', 'Notice_gonggao');
            })
            ->latest()
            ->paginate(10);

        return view('notice/index', [
            'notices' => $notices
        ]);
    }

    public function detail(Content $item)
    {
        View::share('sub_title', lecho('Notice_gonggao'));

        if ($item->isDisabled() || ! $item->isPassed()) {
            return view('layout.404');
        }

        $item->increment('clicks');
        return view('notice/detail', [
            'item' => $item
        ]);
    }
}
