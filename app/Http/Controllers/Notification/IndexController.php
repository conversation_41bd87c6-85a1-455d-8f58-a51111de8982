<?php

namespace App\Http\Controllers\Notification;

use App\Http\Controllers\ApiController;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;

class IndexController extends ApiController
{
    public function __construct()
    {
        parent::__construct();
        View::share('menu', 'notification');
    }

    public function index()
    {
        View::share('sub_title', lecho('Personal Message'));

        $user = Auth::user();
        $logs = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        return view('notification.index', [
            'logs' => $logs,
        ]);
    }

    public function detail(DatabaseNotification $notification)
    {
        View::share('sub_title', lecho('Personal Message'));

        $notification->markAsRead();
        return view('notification.detail', [
            'notification' => $notification,
        ]);
    }
}