<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Modules\Cms\Models\Page;

class PagesController extends ApiController
{
    public function detail(Request $request, $slug)
    {
        $type    = session('language_type', config('languageDefault.key'));
        $slugKey = $type.'_'.$slug;
        $page    = Page::where('slug', $slugKey)->first();
        if (! $page) {
            abort(404);
        }
        View::share('sub_title', lecho($page->title ?? ''));
        if($slug == 'about'){
            return view('club.about', compact('page'));
        }
        if($slug == 'contact'){
            return view('club.contact', compact('page'));
        }
        return view('club.pages', compact('page'));
    }
}
