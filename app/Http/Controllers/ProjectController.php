<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\View;
use Modules\Cms\Models\Category;
use Modules\Cms\Models\Content;
use Modules\Interaction\Enums\CommentStatus;

class ProjectController extends ApiController
{
    public function index()
    {
        $category = Category::find(965901);

        $language_type = session('language_type', config('languageDefault.key'));
        View::share('sub_title', lecho($category->name));
        $lists = $category->contents()
            ->withCount([
                'comments' => function ($query) {
                    $query->where('status', CommentStatus::NORMAL);
                }
            ])
            ->where('language', $language_type)
            ->ofEnabled()
            ->ofPassed()
            ->orderBy('created_at')
            ->paginate(10);

        return view('project/index', [
            'category' => $category,
            'lists'    => $lists,
        ]);
    }

    public function detail(Content $content)
    {
        View::share('sub_title', $content->title);

        $content->increment('clicks');

        return view('project/detail', [
            'content' => $content,
        ]);
    }
}
