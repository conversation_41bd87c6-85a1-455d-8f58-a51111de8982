<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Models\Account;
use Modules\Payment\Models\AccountLog;

class RankingController extends ApiController
{
    public function index()
    {
        View::share('sub_title', lecho('Ranking'));
        $total    = Account::sum('score');
        $totalDay = AccountLog::whereDate('created_at', now())->where('type', AccountType::SCORE->value)->sum('amount');

        $userJoin       = DB::table('payment_accounts')
            ->select(['user_id', DB::raw('any_value(score) as score')])
            ->groupBy('user_id');
        $likesJoin      = DB::table('interaction_likes')
            ->where('likeable_type', 'content')
            ->select('likeable_id', DB::raw('count(id) as like_count'))
            ->groupBy('likeable_id');
        $contentJoin    = DB::table('cms_contents')
            ->leftJoinSub($likesJoin, 'likesJoin', function ($join) {
                $join->on('likesJoin.likeable_id', '=', 'cms_contents.id');
            })
            ->select('reviewer_id', DB::raw('sum(likesJoin.like_count) as like_sum'))
            ->groupBy('reviewer_id');
        $subscribesJoin = DB::table('interaction_subscribes')
            ->where('subscribable_type', 'user')
            ->select('subscribable_id', DB::raw('count(id) as subscribe_count'))
            ->groupBy('subscribable_id');

        $lists = User::query()
            ->leftJoinSub($subscribesJoin, 'subscribesJoin', function ($join) {
                $join->on('users.id', '=', 'subscribesJoin.subscribable_id');
            })
            ->leftJoinSub($userJoin, 'userJoin', function ($join) {
                $join->on('users.id', '=', 'userJoin.user_id');
            })
            ->leftJoinSub($contentJoin, 'contentJoin', function ($join) {
                $join->on('users.id', '=', 'contentJoin.reviewer_id');
            })
            ->orderByDesc('score')
            ->select('*', 'score', 'like_sum', 'subscribe_count')
            ->paginate(50);
        return view('ranking/index', [
            'total'    => $total,
            'totalDay' => $totalDay,
            'lists'    => $lists,
        ]);
    }
}
