<?php

namespace App\Http\Controllers\Storage;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use App\Http\Traits\WebUploadTrait;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Storage\Models\Upload;

class UploadController extends ApiController
{
    use WebUploadTrait;

    protected string $path;

    protected array $imageMimes = [
        'image/png',
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/bmp',
        'image/webp',
    ];

    public function avatar(Request $request)
    {
        $img = $request->post('img');
        if (blank($img)) {
            return $this->failed(lecho('Image data does not exist'));
        }
        try {
            $img  = Str::replace('data:image/jpeg;base64,', '', $img);
            $data = $this->save($img);
            if ($data['path']) {
                Auth::user()->info()->update(['avatar' => $data['path']]);
                return $this->success(lecho('Modified successfully'), route('ProfileIndex'));
            } else {
                return $this->failed(lecho('Image upload failed'));
            }
        } catch (Exception $e) {
            return $this->failed($e->getMessage());
        }
    }

    public function editor(Request $request)
    {
        $file       = $request->file('wangeditor-uploaded-image');
        $hash       = File::hash($file);
        $this->path = date('Y/m/d');
        $extension  = $file->guessClientExtension() ?? 'jpg';
        $name       = sprintf('%s.%s', $hash, $extension);
        $path       = sprintf('%s/%s', $this->path, $name);
        $existFile  = Upload::where('hash', $hash)->first();
        if (! $existFile) {
            if (Storage::putFileAs($this->path, $file, $name) === false) {
                return [
                    'errno'   => 1,
                    'message' => '文件上传失败',
                ];
            }
            Upload::create([
                'user_id'  => Api::id(),
                'hash'     => $hash,
                'size'     => File::size($file),
                'type'     => $file->getClientMimeType(),
                'original' => $file->getClientOriginalName(),
                'disk'     => config('filesystems.default'),
                'path'     => $path,
            ]);

            return [
                'errno' => 0,
                'data'  => [
                    'url' => Storage::url($path),
                ],
            ];
        } else {
            return [
                'errno' => 0,
                'data'  => [
                    'url' => Storage::url($existFile->path),
                ],
            ];
        }
    }
}
