<?php

namespace App\Http\Controllers;

class TestController extends ApiController
{
    public function test()
    {
    }

    public function atest($url, $json = "")
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        $headers = [
            'Authorization: Bearer fastgpt-KQrIHclYLvbnYnHy5oeLxVYjmXkTsjy0o8qPmGMmHNLiDeYvJ2HyVGx0MU',
            'Content-Type: application/json'
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $response = curl_exec($ch);
        curl_close($ch);
        return ($response);
    }

    public function dtext($url, $json = "")
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        $headers = [
            'cookie: http_order=id%20desc; http_serverType=nginx; http_pro_end=-1; http_ltd_end=-1; http_file_recycle_status=true; http_rank=list; _fbp=fb.3.1731636136014.76631975229260973; _gcl_au=1.1.1470549605.1731636279; _rdt_uuid=1731636278577.9efeeee8-9eaa-48cb-afec-7b2a40ebd5bf; _ga=GA1.1.2102038958.1731636279; wikiwand.cookies.consent=true; wikiwand-omni-intro=true; ww-settings-upgrade=true; settings={"columnWidth":"53.75em","theme":"light","fontSize":"16px","fontBody":{"FALLBACK":"var(--base-font-body)","ja":"var(--font-m-plus-rounded-1c)","zh":"var(--font-noto-sans-sc)","ko":"var(--base-font-body-kr)","he":"var(--font-rubik)","ar":"var(--base-font-body-arabic)","th":"var(--base-font-body-thai)","hi":"var(--font-hind)"},"fontHeader":{"FALLBACK":"var(--base-font-header)","ja":"var(--font-zen-kaku-gothic-new)","zh":"var(--font-noto-serif-sc)","ko":"var(--font-nanum-gothic)","he":"var(--font-secular-one)","ar":"var(--font-tajawal)","th":"var(--font-kanit)","hi":"var(--font-mukta)"},"links":"color","ai":false,"toc":true,"textAlign":"auto","layout":"header","mediaWidth":"normal","aiTab":"topQs"}; _ga_RV409947JJ=GS1.1.1731636279.1.1.1731638496.6.0.883219851; 17117cc58dd88bc29fd327e438d06dd8=357e3e50-3a5e-4e0c-aa60-02c76219cde6.0g_lry7nFri3hj2a_RA7O75mdVw; JSESSIONID=BC8ECC798C08D3321732BFEB3FD8E2BE; http_Path=%2Fwww%2Fwwwroot%2FLaravelAiWikiClub; token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NmU3YzM2YmFlNzE4YTViYzA2OTFiOTkiLCJ0ZWFtSWQiOiI2NmU3YzM2YmFlNzE4YTViYzA2OTFiOWQiLCJ0bWJJZCI6IjY2ZTdjMzZiYWU3MThhNWJjMDY5MWJhMyIsImV4cCI6MTczMzM1OTc2MCwiaWF0IjoxNzMyNzU0OTYwfQ.YhbIWW0VlODbMySV-WuHtzr14I0qJHFbEElTc2ZcNNE',
            'Content-Type: application/json'
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $response = curl_exec($ch);
        curl_close($ch);
        return ($response);
    }

}
