<?php

namespace App\Http\Controllers;

use App\Facades\Api;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Modules\Cms\Models\Content;
use Modules\Interaction\Models\Subscribe;

class UserController extends ApiController
{
    public function index(User $people)
    {
        $user = Api::user();

        $articles     = Content::query()
            ->where('reviewer_type', $people->getMorphClass())
            ->where('reviewer_id', $people->getKey())
            ->OfPassed()
            ->OfEnabled()
            ->paginate(10);
        $isSubscribed = Subscribe::where('user_id', Api::id())
            ->where('subscribable_type', $people->getMorphClass())
            ->where('subscribable_id', $people->getKey())
            ->exists();

        View::share('sub_title', lecho($people->info->nickname));
        return view('users.profile', compact('people', 'articles', 'user', 'isSubscribed'));
    }

    /**
     * Notes: 关注
     *
     * @Author: 玄尘
     * @Date: 2024/11/26 13:53
     */
    public function follow(User $people, Request $request)
    {
        $user = Api::user();
        if (! $request->ajax()) {
            return view('layout.404');
        }
        if ($user->id == $people->id) {
            return $this->failed("Don't follow yourself");
        }

        $info = $user->follows()
            ->where('subscribable_type', $people->getMorphClass())
            ->where('subscribable_id', $people->getKey())
            ->first();

        if ($info) {
            $info->delete();
            return $this->success(lecho('Cancel'));
        } else {
            Subscribe::create([
                'subscribable_type' => $people->getMorphClass(),
                'subscribable_id'   => $people->getKey(),
                'user_id'           => $user->id,
            ]);
            return $this->success(lecho('Success'));
        }
    }
}
