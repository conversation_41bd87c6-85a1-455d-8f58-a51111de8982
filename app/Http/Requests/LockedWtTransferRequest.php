<?php

namespace App\Http\Requests;

use App\Models\LockedWt;
use Illuminate\Support\Facades\Auth;

class LockedWtTransferRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'to_username'  => 'required|string',
            'locked_wt_id' => 'required|integer|exists:user_locked_wt,id',
            'amount'       => [
                'required',
                'numeric',
                'min:0.********',
                function ($attribute, $value, $fail) {
                    $lockedWtId = $this->input('locked_wt_id');
                    $userId     = Auth::id();

                    $lockedWt = LockedWt::where('id', $lockedWtId)
                        ->where('user_id', $userId)
                        ->where('status', LockedWt::STATUS_LOCKED)
                        ->first();

                    if ($lockedWt && $value > $lockedWt->remaining_amount) {
                        $fail(lecho('transfer_amount_exceeds_remaining').' '.floatval($lockedWt->remaining_amount).' WT');
                    }
                }
            ],
            'remark'       => 'nullable|string|max:255'
        ];
    }

    public function messages(): array
    {
        return [
            'to_username.required'  => lecho('Target account or email must be filled in'),
            'locked_wt_id.required' => lecho('locked_wt_record_required'),
            'locked_wt_id.exists'   => lecho('locked_wt_record_not_exists'),
            'amount.required'       => lecho('Transfer amount must be filled in'),
            'amount.numeric'        => lecho('Transfer amount format error'),
            'amount.min'            => lecho('Minimum transfer amount is 0.********'),
            'amount.max'            => lecho('Maximum transfer amount is *********'),
            'remark.max'            => lecho('remark_max_255_chars')
        ];
    }
}
