<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\Password;
use Modules\User\Rules\MobileRule;

class BindMobileSmsRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'password' => [
                'required',
                'current_password:web',
                config('user.STRONG_PASSWORD') ? Password::min(8)->mixedCase()->numbers() : Password::min(6),
            ],
            'mobile'   => [
                'required',
                'bail',
                'unique:users,mobile',
                new MobileRule(),
            ]
        ];
    }

    public function messages()
    {
        return [
            'password.required' => lecho('Please enter your login password'),
            'mobile.required'   => lecho('Please enter your phone number'),
            'mobile.unique'     => lecho('This phone number has already been linked to an account'),
            'channel.required'  => lecho('Please enter the SMS channel'),
        ];
    }
}
