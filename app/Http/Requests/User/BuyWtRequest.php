<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;

class BuyWtRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'amount' => 'required|numeric|min:1|max:100000',
        ];
    }

    public function messages()
    {
        return [
            'amount.required' => lecho('quantity_cannot_be_empty'),
            'amount.numeric'  => lecho('invalid_quantity_type'),
            'amount.min'      => lecho('Minimum purchase quantity is :min'),
            'amount.max'      => lecho('Maximum purchase quantity is :max'),
        ];
    }
}
