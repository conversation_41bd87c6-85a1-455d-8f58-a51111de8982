<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;

class ForgotEmailSendRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'send_mail' => 'required|email|bail|exists:users,email',
        ];
    }

    public function messages()
    {
        return [
            'send_mail.required' => lecho('Please fill in your email address'),
            'send_mail.email'    => lecho('Email format error'),
            'send_mail.exists'   => lecho('Email does not exist'),
        ];
    }
}
