<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;

class PublishEditRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'title'            => 'required|max:100',
            'blog_description' => 'nullable|max:200',
            'category_id'      => 'required|exists:cms_categories,id',
            'tags'             => 'nullable|string|max:50',
            'full_content'     => 'nullable',
            'files'            => 'nullable|array',
            'pics'             => 'nullable|array',
        ];
    }

    public function messages()
    {
        return [
            'title.required'            => lecho('Title must be filled in'),
            'title.max'                 => lecho('Title maximum length :max characters'),
            'blog_description.required' => lecho('Introduction must be filled in'),
            'blog_description.max'      => lecho('Description maximum :max characters'),
            'category_id.max'           => lecho('Please choose a category'),
            'category_id.exists'        => lecho('The classification does not exist'),
            'tags.string'               => lecho('Label format error'),
            'tags.max'                  => lecho('Maximum tag length :max characters'),
            //            'content.max'               => lecho('The maximum length of content is 20000 characters'),
            'files.array'               => lecho('Image upload format error'),
            'pics.array'                => lecho('Image upload format error'),
        ];
    }
}