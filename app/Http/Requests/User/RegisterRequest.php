<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;
use App\Rules\UserNameUniqueRule;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'username'              => [
                'required',
                'min:5',
                'max:50',
//                'alpha',
                new UserNameUniqueRule()
            ],
            'password'              => [
                'required',
                'confirmed',
                config('user.STRONG_PASSWORD') ? Password::min(8)->mixedCase()->numbers() : Password::min(6),
            ],
            'password_confirmation' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'username.required'              => lecho('The username cannot be empty'),
            'username.min'                   => lecho('username min 5 chat'),
            'username.max'                   => lecho('The maximum username is :max character'),
//            'username.alpha'                 => lecho('The username must contain characters'),
            'username.unique'                => lecho('The user has been registered'),
            'password.required'              => lecho('User password must be filled in'),
            'password.confirmed'             => lecho('Confirm password inconsistency'),
            'password_confirmation.required' => lecho('Confirm that the password is incorrect'),
        ];
    }
}
