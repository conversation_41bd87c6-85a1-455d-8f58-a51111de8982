<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\Password;

class RepeatLoginPasswordRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'old_password' => [
                'required',
            ],
            'new_password' => [
                'required',
                'confirmed',
                config('user.STRONG_PASSWORD') ? Password::min(8)->mixedCase()->numbers() : Password::min(6),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'old_password.required'         => lecho('Please enter the original password'),
            'old_password.current_password' => lecho('Password verification failed'),
            'new_password.required'         => lecho('Please enter a new password'),
            'new_password.confirmed'        => lecho('Duplicate password error'),
        ];
    }
}
