<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;

class RewardRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'coin'      => 'required|numeric|min:1|max:500',
            'send_word' => 'nullable|string|max:100',
        ];
    }

    public function messages()
    {
        return [
            'coin.required'    => lecho('Coin field is required.'),
            'coin.numeric'     => lecho('Coin field must numeric.'),
            'coin.min'         => lecho('Coin field :min'),
            'coin.max'         => lecho('Coin field :max'),
            'send_word.string' => lecho('Send a word field must be a string.'),
            'send_word.max'    => lecho('Send a word field :max'),
        ];
    }
}