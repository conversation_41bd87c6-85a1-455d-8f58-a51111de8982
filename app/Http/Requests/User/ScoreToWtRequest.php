<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;

class ScoreToWtRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'type'     => 'required',
            'amount'   => 'required|numeric|min:1|max:500000',
            'password' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'amount.required'   => lecho('The transfer amount must be filled in'),
            'amount.numeric'    => lecho('Transfer amount format error'),
            'amount.min'        => lecho('The minimum transfer amount is :min'),
            'amount.max'        => lecho('The maximum transfer amount is :max'),
            'password.required' => lecho('Please enter the payment password'),
        ];
    }
}
