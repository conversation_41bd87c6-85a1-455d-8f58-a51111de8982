<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\Password;

class SetPaymentSecureRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'email_code'                    => 'required',
            'new_pay_password'              => [
                'required',
                'confirmed',
                config('user.STRONG_PASSWORD') ? Password::min(8)->mixedCase()->numbers() : Password::min(6),
            ],
            'new_pay_password_confirmation' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'email_code.required'        => lecho('Please enter the verification code'),
            'new_pay_password.required'  => lecho(lecho('Please enter the payment password')),
            'new_pay_password.confirmed' => lecho('Duplicate payment password error'),
        ];
    }
}
