<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;

class WtToUsdcRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'wallet_address' => 'required|string|regex:/^0x[a-fA-F0-9]{40}$/',
            'amount'         => 'required|numeric|min:1|max:500000',
            'password'       => 'required',
        ];
    }

    public function messages()
    {
        return [
            'wallet_address.required' => lecho('wallet_address_cannot_be_empty'),
            'wallet_address.regex'    => lecho('error_invalid_wallet_address_format'),
            'amount.required'         => lecho('quantity_cannot_be_empty'),
            'amount.numeric'          => lecho('invalid_quantity_type'),
            'amount.min'              => lecho('Minimum purchase quantity is :min'),
            'amount.max'              => lecho('Maximum purchase quantity is :max'),
        ];
    }
}
