<?php

use App\Http\Controllers\Account\IndexController;
use App\Http\Controllers\Account\LockedWtController;
use App\Http\Controllers\Account\ScoreController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'prefix'     => '/account',
    'namespace'  => 'Account',
    'as'         => 'account.',
    'middleware' => ['web', 'auth'],
], function (Router $router) {
    $router->get('balance', [IndexController::class, 'balance'])->name('balance');
    $router->post('balance/transfer', [IndexController::class, 'transfer'])->name('balance.transfer');
    $router->post('wt/transfer', [IndexController::class, 'wtTransfer'])->name('wt.transfer');
    $router->post('transfer/{transfer}/result', [IndexController::class, 'result'])->name('result');
    $router->post('transfer/{transfer}/wt_result', [IndexController::class, 'wtResult'])->name('wt_result');
    $router->get('coin', [IndexController::class, 'coin'])->name('coin');
    $router->get('wt', [IndexController::class, 'wt'])->name('wt');
    $router->get('score', [ScoreController::class, 'index'])->name('score');
    $router->post('score/convert', [ScoreController::class, 'scoreToWt'])->name('score.convert');
    //锁仓wt
    $router->get('locked-wt', [LockedWtController::class, 'index'])->name('locked_wt');
    $router->post('locked_wt/transfer', [LockedWtController::class, 'transfer'])->name('locked_wt.transfer');
    $router->get('locked_wt/{lockedWt}/operations', [LockedWtController::class, 'recordOperations'])
        ->name('locked_wt.record_operations');
});