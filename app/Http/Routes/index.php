<?php

use App\Http\Controllers\ArticleDetailController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\DeepchatController;
use App\Http\Controllers\DevelopmentController;
use App\Http\Controllers\FaqController;
use App\Http\Controllers\IndexController;
use App\Http\Controllers\MemberController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\NoticeController;
use App\Http\Controllers\OpensourceController;
use App\Http\Controllers\PagesController;
use App\Http\Controllers\PediaController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\RankingController;
use App\Http\Controllers\TeamController;
use App\Http\Controllers\TestController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'prefix'     => '',
    'middleware' => ['web'],
], function (Router $router) {
    $router->get('/', [IndexController::class, 'index'])->name('index');
    $router->get('/news', [NewsController::class, 'index'])->name('NewsIndex');
    $router->get('/opensource', [OpensourceController::class, 'index'])->name('OpenSource');
    $router->get('/deepchat', [DeepchatController::class, 'index'])->name('DeepChat');
    $router->post('/chat/deepseek', [DeepchatController::class, 'chat'])
        ->name('DeepChatDo');
    $router->get('/category/{category}', [CategoryController::class, 'index'])->name('categoryIndex');
    $router->get('/article/{content}/detail', [ArticleDetailController::class, 'index'])->name('ArticleDetailIndex');
    $router->get('/ranking', [RankingController::class, 'index'])->name('RankIndex');
    $router->get('/faq', [FaqController::class, 'index'])->name('FaqIndex');
    $router->get('/faq/{item}/detail', [FaqController::class, 'detail'])->name('FaqDetail');
    $router->get('/pedia', [PediaController::class, 'index'])->name('PediaIndex');
    $router->get('/pedia/search', [PediaController::class, 'search'])->name('PediaSearch');
    $router->get('/wiki/{title}', [PediaController::class, 'detail'])->name('PediaDetail');
    $router->get('/pages/{page}', [PagesController::class, 'detail'])->name('PageDetail');

    //关于我们
    $router->get('/abouts/development_history', [DevelopmentController::class, 'index'])->name('development_history');
    $router->get('/abouts/team_members', [TeamController::class, 'index'])->name('team_members');
    $router->get('/team/{content}/detail', [TeamController::class, 'detail'])->name('team_detail');
    $router->get('/abouts/project_introduction', [ProjectController::class, 'index'])->name('project_introduction');
    $router->get('/project/{content}/detail', [ProjectController::class, 'detail'])->name('project_detail');

    //公告
    $router->get('/notice', [NoticeController::class, 'index'])->name('notice.index');
    $router->get('/notice/{item}/detail', [NoticeController::class, 'detail'])->name('notice.detail');
    $router->get('/search', [IndexController::class, 'search'])->name('index.search');
    $router->get('/test', [TestController::class, 'test'])->name('index.test');
    //成员
    $router->get('/member/{content}/detail', [MemberController::class, 'detail'])->name('member_detail');
});
Route::group([
    'prefix'     => '/',
    'middleware' => ['web', 'auth'],
], function (Router $router) {
    $router->get('/detail/{item}/score',
        [ArticleDetailController::class, 'score'])->name('ArticleScore');
    $router->get('/detail/{item}/dscore',
        [ArticleDetailController::class, 'dscore'])->name('ArticleDScore');
    $router->get('/detail/{item}/like',
        [ArticleDetailController::class, 'like'])->name('ArticleLike');
    $router->post('/detail/{item}/comment',
        [ArticleDetailController::class, 'comment'])->name('ArticleComment');
    $router->get('/detail/{item}/favorites',
        [ArticleDetailController::class, 'favorite'])->name('ArticleFavorites');
    $router->post('/detail/{item}/reward',
        [ArticleDetailController::class, 'reward'])->name('ArticleReward');
    $router->get('/detail/{item}/share',
        [ArticleDetailController::class, 'share'])->name('ArticleShare');
    //用户中心
});


