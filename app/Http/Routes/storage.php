<?php

use App\Http\Controllers\Storage\UploadController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'prefix'     => '/storage',
    'middleware' => ['web', 'auth'],
], function (Router $router) {
    $router->post('avatar', [UploadController::class, 'avatar'])->name('storage.avatar');
    $router->post('editor', [UploadController::class, 'editor'])->name('editor.upload');
});
Route::group([
    'prefix'     => '/storage',
    'middleware' => ['web'],
], function (Router $router) {
});