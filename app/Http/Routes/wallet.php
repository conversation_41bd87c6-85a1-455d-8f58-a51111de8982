<?php

use App\Http\Controllers\Wallet\IndexController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'prefix'     => '/wallet',
    'as'         => 'wallet.',
    'middleware' => ['web', 'auth'],
], function (Router $router) {
    $router->get('buy', [IndexController::class, 'buy'])->name('buy');
    $router->post('create-order', [IndexController::class, 'createOrder'])->name('create-order');
    $router->get('orders', [IndexController::class, 'orders'])->name('orders');

    // WT转USDC相关路由
    $router->get('transfer', [IndexController::class, 'transfer'])->name('transfer');
    $router->post('transfer', [IndexController::class, 'processTransfer'])->name('process-transfer');
    $router->post('transfer/{transaction}/result', [IndexController::class, 'transferResult'])->name('transfer-result');
});
