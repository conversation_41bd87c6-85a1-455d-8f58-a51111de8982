<?php

namespace App\Http\Traits;

use App\Facades\Api;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Modules\Storage\Models\Upload;

trait WebUploadTrait
{
    protected function save(string $data)
    {
        $tempFileName = tempnam(sys_get_temp_dir(), 'image');
        file_put_contents($tempFileName, base64_decode($data));
        $path      = date('Y/m/d');
        $file      = new UploadedFile(
            $tempFileName,
            'temp.jpg',        // 可以自定义文件名，这里假设是JPEG格式
        );
        $hash      = File::hash($file);
        $extension = 'jpg';
        $existFile = Upload::where('hash', $hash)->first();
        if (! $existFile) {
            $name = sprintf('%s.%s', $hash, $extension);
            if (Storage::putFileAs($path, $file, $name) === false) {
                throw new Exception('文件上传失败');
            }
            $path = sprintf('%s/%s', $path, $name);
            Upload::create([
                'user_id'  => Api::id(),
                'hash'     => $hash,
                'size'     => File::size($file),
                'type'     => $file->getClientMimeType(),
                'original' => $file->getClientOriginalName(),
                'disk'     => config('filesystems.default'),
                'path'     => $path,
            ]);
            return [
                'hash'     => $hash,
                'type'     => $file->getMimeType(),
                'size'     => File::size($file),
                'original' => $file->getClientOriginalName(),
                'url'      => Storage::url($path),
                'path'     => $path,
            ];
        } else {
            return [
                'hash'     => $hash,
                'type'     => $existFile->type,
                'size'     => $existFile->size,
                'original' => $existFile->original,
                'url'      => Storage::url($existFile->path),
                'path'     => $existFile->path,
            ];
        }
    }
}