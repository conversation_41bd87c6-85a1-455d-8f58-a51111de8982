<?php

namespace App\Jobs;

use DateInterval;
use DateTimeInterface;
use Illuminate\Bus\Batchable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

abstract class Base<PERSON><PERSON><PERSON>Job implements ShouldQueue
{
    use Batchable,
        Dispatchable,
        InteractsWithQueue,
        SerializesModels;

    public string $connection = 'redis';

    public string $queue = 'shinami';

    public DateTimeInterface|DateInterval|array|int|null $delay = 0;

    public int $timeout = 30;

    public int $tries = 1;

    abstract public function handle(): void;

    /**
     * 设置延迟队列
     *
     * @param  \DateInterval|\DateTimeInterface|int|array|null  $delay
     * @return $this
     */
    public function delay(DateInterval|DateTimeInterface|int|array|null $delay): static
    {
        $this->delay = $delay;

        return $this;
    }
}