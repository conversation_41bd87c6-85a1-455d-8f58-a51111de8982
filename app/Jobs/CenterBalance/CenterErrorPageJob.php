<?php

namespace App\Jobs\CenterBalance;

use App\Jobs\BaseCenterBalance;
use App\Models\CenterAccountLogError;
use App\Models\User;

class CenterErrorPageJob extends BaseCenterBalance
{
    public function __construct(protected User $user)
    {
    }

    public function handle(): void
    {
        $logs = CenterAccountLogError::where('user_id', $this->user->id)->get();
        foreach ($logs as $log) {
            CenterErrorOneJob::dispatch($log);
        }
    }
}