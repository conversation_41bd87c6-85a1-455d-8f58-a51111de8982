<?php

namespace App\Jobs\Imports\Children;

use App\Jobs\BaseMany;
use Illuminate\Support\Facades\DB;
use Modules\Cms\Models\Content;

class ImportContentCommentJob extends BaseMany
{
    public function __construct(protected Content $content)
    {
    }

    public function handle(): void
    {
        $logs = DB::connection('old_user')
            ->table('p_user_comment_blog')
            ->where('blog_id', $this->content->id)
            ->get();
        foreach ($logs as $log) {
            $star = 0;
            if ($log->zan) {
                $star = 1;
            }
            if ($log->cai) {
                $star = -1;
            }

            DB::table('interaction_comments')
                ->updateOrInsert([
                    'id' => $log->id,
                ], [
                    'user_id'          => $log->uid,
                    'commentable_type' => 'content',
                    'commentable_id'   => $this->content->id,
                    'pictures'         => $log->images ? json_encode([$log->images]) : null,
                    'content'          => $log->comment,
                    'star'             => $star,
                    'created_at'       => date('Y-m-d H:i:s', $log->createtime),
                    'updated_at'       => date('Y-m-d H:i:s', $log->createtime),
                ]);
        }
    }
}