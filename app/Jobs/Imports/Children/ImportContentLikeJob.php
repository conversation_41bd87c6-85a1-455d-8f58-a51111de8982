<?php

namespace App\Jobs\Imports\Children;

use App\Jobs\BaseMany;
use Illuminate\Support\Facades\DB;
use Modules\Cms\Models\Content;

class ImportContentLikeJob extends BaseMany
{
    public function __construct(protected Content $content)
    {
    }

    public function handle(): void
    {
        $logs = DB::connection('old_user')
            ->table('p_user_like_blog')
            ->where('blog_id', $this->content->id)
            ->get();
        foreach ($logs as $log) {
            DB::table('interaction_likes')
                ->updateOrInsert([
                    'id' => $log->id,
                ], [
                    'user_id'       => $log->uid,
                    'likeable_type' => 'content',
                    'likeable_id'   => $this->content->id,
                    'created_at'    => date('Y-m-d H:i:s', $log->createtime),
                    'updated_at'    => date('Y-m-d H:i:s', $log->createtime),
                ]);
        }
    }
}