<?php

namespace App\Jobs\Imports;

use App\Enums\ApplyStatus;
use App\Jobs\BaseJob;
use App\Jobs\Imports\Children\ImportContentCommentJob;
use App\Jobs\Imports\Children\ImportContentFavoritesJob;
use App\Jobs\Imports\Children\ImportContentLikeJob;
use App\Models\Old\OldBlog;
use Modules\Cms\Models\Category;
use Modules\Cms\Models\Content;

class ImportWhoBlogJob extends BaseJob
{
    public function __construct(protected OldBlog $model)
    {
    }

    public function handle(): void
    {
        $category = Category::where('description', $this->model->cate_id)->first();
        if ($category) {
            $item = Content::updateOrCreate([
                'id' => $this->model->id,
            ], [
                'title'         => $this->model->title,
                'sub_title'     => $this->model->tips,
                'description'   => $this->model->description,
                'content'       => $this->model->content,
                'category_id'   => $category->id,
                'cover'         => $this->model->image,
                'reviewer_type' => 'user',
                'reviewer_id'   => $this->model->uid,
                'clicks'        => $this->model->click,
                'created_at'    => date('Y-m-d H:i:s', $this->model->createtime),
                'status'        => 1,
                'apply_status'  => ApplyStatus::PASS,
            ]);
            ImportContentLikeJob::dispatch($item);
            ImportContentFavoritesJob::dispatch($item);
            ImportContentCommentJob::dispatch($item);
        }
    }
}