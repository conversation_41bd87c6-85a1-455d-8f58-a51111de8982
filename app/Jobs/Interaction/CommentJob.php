<?php

namespace App\Jobs\Interaction;

use App\Models\SystemConfig;
use Modules\Interaction\Jobs\InteractionBaseJob;
use Modules\Interaction\Models\Comment;
use Modules\Payment\Models\AccountLog;
use Modules\Payment\Models\AccountRule;

class CommentJob extends InteractionBaseJob
{
    public function __construct(protected Comment $comment)
    {
    }

    public function handle(): void
    {
        $user = $this->comment->user;
        $rule = AccountRule::where('slug', 'comment')->first();

        if ($rule && AccountLog::where('source->comment_id',
                $this->comment->commentable_id)
                ->where('source->comment_type',
                    $this->comment->commentable_type)
                ->where('account_id', $user->account->id)
                ->doesntExist()) {
            $scoreMax = SystemConfig::getValue('score', 'day_comment_max');
            $score    = SystemConfig::getValue('score', 'comment');
            if ($user->account->logs()->where('rule_id', $rule->id)
                    ->whereDate('created_at', now())
                    ->sum('amount') < $scoreMax) {
                $user->account->exec($rule, $score, null, [
                    'comment_type' => $this->comment->commentable_type,
                    'comment_id'   => $this->comment->commentable_id,
                    'remark'       => lecho('Comment').$this->comment->commentable->getTitle(),
                ]);
            }
            $content = $this->comment->commentable;
            if ($content->reviewer) {
                $content->reviewer->account->exec($rule, $score, null, [
                    'comment_type' => $this->comment->commentable_type,
                    'comment_id'   => $this->comment->commentable_id,
                    'reviewer'     => 1,
                    'remark'       => 'Blog posts are commented on'
                ]);
            }
        }
    }
}