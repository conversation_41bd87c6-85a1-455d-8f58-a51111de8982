<?php

namespace App\Jobs\Interaction;

use App\Models\SystemConfig;
use Modules\Interaction\Jobs\InteractionBaseJob;
use Modules\Interaction\Models\Favorite;
use Modules\Payment\Models\AccountLog;
use Modules\Payment\Models\AccountRule;

class FavoriteJob extends InteractionBaseJob
{
    public function __construct(protected Favorite $favorite)
    {
    }

    public function handle(): void
    {
        $user = $this->favorite->user;
        $rule = AccountRule::where('slug', 'favorite')->first();

        if ($rule && AccountLog::where('source->favorite_type',
                $this->favorite->favoriteable_type)
                ->where('source->favorite_id', $this->favorite->favoriteable_id)
                ->where('account_id', $user->account->id)
                ->doesntExist()) {
            $scoreMax = SystemConfig::getValue('score', 'day_favorite_max');
            $score    = SystemConfig::getValue('score', 'favorite');
            if ($user->account->logs()->where('rule_id', $rule->id)
                    ->whereDate('created_at', now())
                    ->sum('amount') < $scoreMax) {
                $user->account->exec($rule, $score, null, [
                    'favorite_type' => $this->favorite->favoriteable_type,
                    'favorite_id'   => $this->favorite->favoriteable_id,
                    'remark'        => lecho('Favorites').$this->favorite->favoriteable->getTitle(),
                ]);
            }
            $content = $this->favorite->favoriteable;
            if ($content->reviewer) {
                $content->reviewer->account->exec($rule, $score, null, [
                    'favorite_type' => $this->favorite->favoriteable_type,
                    'favorite_id'   => $this->favorite->favoriteable_id,
                    'reviewer'      => 1,
                    'remark'        => 'Blog posts are bookmarked'
                ]);
            }
        }
    }
}
