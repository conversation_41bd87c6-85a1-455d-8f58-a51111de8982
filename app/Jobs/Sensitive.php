<?php

namespace App\Jobs;

use App\Models\Model;
use GuzzleHttp\Client;
use Illuminate\Support\Str;

class Sensitive extends BaseJob
{
    public int       $timeout = 120;
    protected Client $client;
    protected string $path;

    public function __construct(protected Model $model)
    {
    }

    public function handle(): void
    {
        $baseUrl      = env('HUYATEXT_URL');
        $this->path   = env('HUYATEXT_PATH');
        $this->client = new Client([
            'base_uri' => $baseUrl,
        ]);

        if (method_exists($this->model, 'getSensitive')
            && method_exists($this->model, 'sensitivePass')
            && method_exists($this->model, 'sensitiveReject')
            && method_exists($this->model, 'sensitive')
        ) {
            $sensitiveContent = $this->model->getSensitive();
            $index            = 1;
            $chunkNumber      = 5000;
            $message          = Str::substr($sensitiveContent, ($index - 1) * $chunkNumber, $chunkNumber);
            while (! blank($message)) {
                $res = $this->checkMessage($message);
                if ($res === true) {
                    $index++;
                    $message = Str::substr($sensitiveContent, ($index - 1) * $chunkNumber, $chunkNumber);
                    if (blank($message)) {
                        $this->model->sensitivePass();
                    }
                } elseif (is_array($res)) {
                    foreach ($res['resultItems'] as $item) {
                        $msg   = $item['msg'] ?? '';
                        $words = $item['hits'][0]['words'] ?? [];
                        $this->model->sensitive()->create([
                            'msg'   => $msg,
                            'words' => implode('|', $words),
                        ]);
                    }
                    $this->model->sensitiveReject();
                    break;
                } elseif (is_string($res)) {
                    $this->fail($res);
                    break;
                }
            }
        } else {
            $this->fail('对应模型没有getSensitive、sensitivePass、sensitiveReject、sensitive函数');
        }
    }

    protected function checkMessage(string $message)
    {
        $response = $this->client->post($this->path, [
            'headers'     => [
                'Content-Type'  => 'application/x-www-form-urlencoded; charset=UTF-8',
                'Authorization' => 'APPCODE '.env('HUYATEXT_CODE'),
            ],
            'form_params' => [
                'text' => $message,
            ]
        ]);
        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getBody()->getContents(), true);
            if ($data['success']) {
                $result = $data['data'];
                if ($result['result'] == '1' || $result['result'] == '3') {
                    return true;
                } elseif ($result['result'] == '2') {
                    return $result['resultItems'];
                } else {
                    return '未知的返回类型';
                }
            } else {
                return $data['msg'];
            }
        } else {
            return $response->getBody()->getContents();
        }
    }
}