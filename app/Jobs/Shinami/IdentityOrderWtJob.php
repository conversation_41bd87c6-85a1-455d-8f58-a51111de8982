<?php

namespace App\Jobs\Shinami;

use App\Jobs\BaseShinamiJob;
use App\Models\LockedWt;
use App\Models\SystemConfig;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IdentityOrderWtJob extends BaseShinamiJob
{

    /**
     * 创建一个新的任务实例
     */
    public function __construct(protected User $user, protected float $number = 10000)
    {
        Log::info('IdentityOrderWtJob __construct method started!'); // 添加这行
        info(__CLASS__.' '.__LINE__);
    }

    /**
     * 执行任务
     */
    public function handle(): void
    {
        try {
            $rewardAmount = $this->number;
            $user         = $this->user;

            // 获取配置
            $feeRate  = SystemConfig::getValue('wallet', 'vip_wt_fee_rate', 0.3);
            $lockDays = SystemConfig::getValue('wallet', 'vip_wt_lock_days', 730);

            // 计算服务费和实际到账金额
            $serviceFee   = $rewardAmount * ($feeRate / 100);
            $actualAmount = $rewardAmount - $serviceFee;

            Log::info('会员WT奖励计算', [
                'user_id'       => $user->id,
                'reward_amount' => $rewardAmount,
                'service_fee'   => $serviceFee,
                'actual_amount' => $actualAmount,
                'lock_days'     => $lockDays
            ]);

            DB::transaction(function () use ($user, $rewardAmount, $serviceFee, $actualAmount, $lockDays) {
                // 在coins账户中增加锁仓WT
                $user->account->exec('recharge_coins', $actualAmount, null, [
                    'type'            => 'vip_reward',
                    'original_amount' => $rewardAmount,
                    'service_fee'     => $serviceFee,
                    'lock_days'       => $lockDays,
                    'remark'          => '开通会员赠送WT（锁仓中）'
                ]);

                // 创建锁仓记录
                $expiredAt = now()->addDays($lockDays);
                LockedWt::create([
                    'user_id'          => $user->id,
                    'amount'           => $actualAmount,
                    'remaining_amount' => $actualAmount,
                    'expired_at'       => $expiredAt,
                    'status'           => LockedWt::STATUS_LOCKED,
                ]);

                Log::info('会员WT奖励发放成功', [
                    'user_id'       => $user->id,
                    'actual_amount' => $actualAmount,
                    'locked_until'  => $expiredAt->format('Y-m-d H:i:s')
                ]);
            });
        } catch (Exception $e) {
            Log::channel('shinami')->error('会员WT奖励发放失败', [
                'user_id' => $this->user->id,
                'error'   => $e->getMessage(),
                'trace'   => $e->getTraceAsString()
            ]);

            // 如果不是最后一次尝试，则重试
            if ($this->attempts() < $this->tries) {
                $this->release(30 * $this->attempts()); // 递增延迟
            } else {
                throw $e;
            }
        }
    }

}
