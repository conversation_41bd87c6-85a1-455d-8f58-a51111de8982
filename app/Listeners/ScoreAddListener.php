<?php

namespace App\Listeners;

use App\Events\ScoreAddEvent;
use Illuminate\Contracts\Queue\ShouldQueue;

class ScoreAddListener implements ShouldQueue
{
    public string $connection = 'redis';

    public int $delay = 0;

    public int $tries = 1;

    public function handle(ScoreAddEvent $event): void
    {
        $score = $event->data['amount'] ?: 0;
        if ($score > 0) {
            $event->user->account->exec($event->rule, $score, 0, $event->data);
        }
    }
}