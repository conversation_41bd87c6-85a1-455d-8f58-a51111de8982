<?php

namespace App\Models;

use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\DB;

class AiChatConfig extends Model
{
    use Cachable, HasEasyStatus, HasCovers;

    const WENXIN   = 'wenxin';
    const DOUBAO   = 'doubao';
    const TONGYI   = 'tongyi';
    const ZHIPU    = 'zhipu';
    const KIMI     = 'kimi';
    const HUNYUAN  = 'hunyuan';
    const DEEPSEEK = 'deepseek';
    const CHATGPT  = 'chatgpt4';
    const KEYS     = [
//        self::WENXIN   => '文心一言',
//        self::DOUBAO   => '豆包',
//        self::TONGYI   => '通义千问',
//        self::ZHIPU    => '智普AI',
//        self::KIMI     => 'Kimi',
//        self::HUNYUAN  => '腾讯混元',
        self::DEEPSEEK => 'Deepseek',
//        self::CHATGPT  => 'ChatGpt4',
    ];

    protected $casts = [
        'params' => 'json'
    ];

    protected static function boot()
    {
        parent::boot();
        self::saving(function ($model) {
            if ($model->is_default) {
                DB::table('ai_chat_configs')
                    ->where('id', '!=', $model->id)
                    ->where('is_default', 1)
                    ->update([
                        'is_default' => 0
                    ]);
            }
        });
    }

    public function engine(): HasOne
    {
        return $this->hasOne(AiChatEngine::class, 'name', 'engine_name');
    }

}
