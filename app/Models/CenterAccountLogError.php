<?php

namespace App\Models;

use App\Packages\AiWikiUserCenter\AiWikiUserCenter;
use App\Traits\BelongsToUser;
use Exception;
use Illuminate\Database\Eloquent\SoftDeletes;

class CenterAccountLogError extends Model
{
    use BelongsToUser,
        SoftDeletes;

    protected $casts = [
        'source' => 'json',
    ];

    public static function addLog(
        int $user_id = 0,
        string $type = '',
        float $amount = 0,
        string $remark = '',
        array $source = [],
        int $freezeDay = 0,
        string $message = '',
    ): self {
        return CenterAccountLogError::create([
            'user_id'   => $user_id,
            'type'      => $type,
            'amount'    => $amount,
            'remark'    => $remark,
            'source'    => $source,
            'freezeDay' => $freezeDay,
            'message'   => $message,
        ]);
    }

    public function reset(): void
    {
        $result = AiWikiUserCenter::user()->addBalance(
            userkey: $this->user->center_key,
            type: $this->type,
            amount: $this->amount,
            remark: $this->remark,
            freezeTime: $this->freezeDay ? now()->addDays($this->freezeDay) : '',
            source: $this->source,
        );
        if ($result->isSuccess()) {
            $this->delete();
        } else {
            $this->message = $result->toArray()['message'];
            $this->save();
            throw new Exception($result->toArray()['message']);
        }
    }
}
