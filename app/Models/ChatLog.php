<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatLog extends Model
{
    use BelongsToUser, SoftDeletes;

    protected $casts = [
        'images' => 'json'
    ];

    public function group(): BelongsTo
    {
        return $this->belongsTo(ChatGroup::class, 'group_id', 'id');
    }
}
