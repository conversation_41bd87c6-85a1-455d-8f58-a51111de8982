<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Cms\Models\Content;

class CmsContentReward extends Model
{
    public function fromUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    public function toUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    public function cmsContent(): BelongsTo
    {
        return $this->belongsTo(Content::class, 'cms_content_id');
    }
}
