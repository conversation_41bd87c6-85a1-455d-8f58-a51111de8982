<?php

namespace App\Models;

use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;

class LanguageType extends Model
{
    use HasEasyStatus, Cachable;

    protected static function boot()
    {
        parent::boot();
        self::saved(function ($model) {
            if ($model->is_default) {
                LanguageType::where('id', '!=', $model->id)->update(['is_default' => 0]);
            }
        });
    }
}
