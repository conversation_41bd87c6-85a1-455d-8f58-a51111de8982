<?php

namespace App\Models;

use Dcat\Admin\Grid;
use Dcat\Admin\Repositories\Repository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Nwidart\Modules\Facades\Module as ModuleManager;

class Module extends Repository
{
    protected string $current = '0.0.0';

    protected function checkVersion(\Nwidart\Modules\Laravel\Module $module): array
    {
        if (! $module->isEnabled() || ! config('custom.check_module_update')) {
            return ['tag_name' => 'none', 'name' => '', 'update' => 2];
        }
        $repository = $module->get('repository');

        if ($repository) {
            $res = Http::withToken('7cfd8c13d44caf1e63d4574f194ce5049c94c5f0')
                ->get('https://git.yuzhankeji.cn/api/v1/repos/'.$repository.'/releases?per_page=1&page=1&limit=1');
            $arr = json_decode($res->body(), true)[0] ?? ['tag_name' => 'none', 'name' => '', 'compare' => 2];

            return [
                'tag_name' => $arr['tag_name'],
                'name'     => $arr['name'],
                'update'   => version_compare($this->current, $module->get('version')),
            ];
        } else {
            return ['tag_name' => 'none', 'name' => '', 'update' => 2];
        }
    }

    /**
     * Notes   : 模块是否存在并已启用
     *
     * @Date   : 2023/7/17 16:17
     * <AUTHOR> <Jason.C>
     * @param  string  $moduleName
     * @return bool
     */
    public static function isEnabled(string $moduleName): bool
    {
        return ModuleManager::has($moduleName) && ModuleManager::isEnabled($moduleName);
    }

    public function get(Grid\Model $model): LengthAwarePaginator|array|Collection
    {
        $modules = ModuleManager::toCollection();
        $modules = $modules->sortBy(function ($module) {
            return $module->get('priority');
        });

        $currentPage = $model->getCurrentPage();
        $perPage     = $model->getPerPage();
        $chunk       = $modules->forPage($currentPage, $perPage);

        $data = $chunk->map(function ($module) {
            return [
                'id'          => $module->getName(),
                'name'        => $module->getName(),
                'low_name'    => strtolower($module->getName()),
                'alias'       => $module->get('alias'),
                'description' => $module->getDescription(),
                'priority'    => $module->getPriority(),
                'keywords'    => $module->get('keywords'),
                'enabled'     => $module->isEnabled(),
                'version'     => $module->get('version'),
                'author'      => $module->get('author'),
                'requires'    => $module->get('requires'),
                'config'      => file_exists(module_path($module->getName(), 'config.json')),
                'current'     => $this->getCurrentVersion($module),
                'newest'      => [
                    'tag_name' => $module->get('version'),
                    'update'   => '00'
                ],
            ];
        })->when(request()->get('_search_'), function (Collection $item) {
            return $item->where('low_name', strtolower(request()->get('_search_')));
        });

        return $model->makePaginator(
            $modules->count(), // 传入总记录数
            $data ?? []        // 传入数据二维数组
        );
    }

    /**
     * Notes   : 获取当前已安装版本
     *
     * @Date   : 2023/9/4 11:40
     * <AUTHOR> <Jason.C>
     * @param  \Nwidart\Modules\Laravel\Module  $module
     * @return string
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function getCurrentVersion(\Nwidart\Modules\Laravel\Module $module): string
    {
        $files  = app('files');
        $status = base_path('modules_versions.json');
        if (! $files->exists($status)) {
            $files->put($status, '[]');
        }

        $file = $files->get($status);
        $json = json_decode($file, true);

        return $this->current = $json[$module->getName()] ?? '0.0.0';
    }
}