<?php

namespace App\Models;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ModuleLog extends Model
{
    protected $casts = [
        'config' => 'json'
    ];

    public function user(): MorphTo
    {
        return $this->morphTo();
    }

    public function setUserAttribute(Authenticatable $user): void
    {
        $this->attributes['user_type'] = $user::class;
        $this->attributes['user_id']   = $user->getAuthIdentifier();
    }
}
