<?php

namespace App\Models;

use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WalletTransaction extends Model
{

    protected $table = 'user_wallet_transactions';

    protected $casts = [
        'amount'           => 'decimal:8',
        'fee'              => 'decimal:8',
        'actual_amount'    => 'decimal:8',
        'transaction_data' => 'array',
        'completed_at'     => 'datetime',
    ];

    const TOKEN_TYPE_WIKE_TOKEN = 'wt';
    const TOKEN_TYPE_USDC_TOKEN = 'usdc';
    const TOKEN_TYPES           = [
        self::TOKEN_TYPE_WIKE_TOKEN => 'WT币',
        self::TOKEN_TYPE_USDC_TOKEN => 'USDC币',

    ];

    const TOKEN_TYPES_VALUES = [
        self::TOKEN_TYPE_WIKE_TOKEN => '0x42f4b6a2af1d54f77207795bbab6247a1d1b4ef9529bb743304ea71406418a8::wike_education_token::WIKE_EDUCATION_TOKEN',
        self::TOKEN_TYPE_USDC_TOKEN => '0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC',
    ];

    const TRANSACTION_TYPE_WITHDRAW = 'withdraw';

    const TRANSACTION_TYPE_TRANSFER = 'transfer';
    //兑换
    const TRANSACTION_TYPE_EXCHANGE = 'exchange';
    //调账
    const TRANSACTION_TYPE_ADJUST = 'adjust';

    const TRANSACTION_TYPE_FEE = 'fee';

    const TRANSACTION_TYPE_PURCHASE = 'purchase';
    const TRANSACTION_TYPE_TOUSDC   = 'to_usdc';
    const TRANSACTION_TYPE_USDC     = 'usdc';
    //开通会员赠送
    const TRANSACTION_TYPE_VIP = 'vip';
    const TRANSACTION_TYPES    = [
        self::TRANSACTION_TYPE_WITHDRAW => '提现',
        self::TRANSACTION_TYPE_TRANSFER => '转账',
        self::TRANSACTION_TYPE_EXCHANGE => '兑换',
        self::TRANSACTION_TYPE_ADJUST   => '调账',
        self::TRANSACTION_TYPE_FEE      => '手续费',
        self::TRANSACTION_TYPE_PURCHASE => '购买',
        self::TRANSACTION_TYPE_TOUSDC   => '转换扣除WT',
        self::TRANSACTION_TYPE_USDC     => '转换USDC',
        self::TRANSACTION_TYPE_VIP      => '会员赠送'
    ];

    // 交易状态常量
    const STATUS_PENDING         = 'pending';
    const STATUS_PROCESSING      = 'processing';
    const STATUS_COMPLETED       = 'completed';
    const STATUS_FAILED          = 'failed';
    const STATUS_ROLLED_BACK     = 'rolled_back';
    const STATUS_ROLLBACK_FAILED = 'rollback_failed';
    const STATUS_FEE_PENDING     = 'fee_pending';
    const STATUS_FEE_FAILED      = 'fee_failed';
    const STATUS_FEE_COMPLETED   = 'fee_completed';
    const WALLET_MAX_AMOUNT      = 30;

    // 状态转换映射
    const STATUS_TRANSITIONS = [
        self::STATUS_PENDING         => [self::STATUS_PROCESSING, self::STATUS_COMPLETED, self::STATUS_FAILED],
        self::STATUS_PROCESSING      => [self::STATUS_COMPLETED, self::STATUS_FAILED],
        self::STATUS_FAILED          => [self::STATUS_ROLLED_BACK, self::STATUS_ROLLBACK_FAILED],
        self::STATUS_ROLLED_BACK     => [],
        self::STATUS_ROLLBACK_FAILED => [self::STATUS_ROLLED_BACK],
        self::STATUS_COMPLETED       => [],
    ];

    // 状态文本映射
    const STATUS_TEXTS      = [
        self::STATUS_PENDING         => '待处理',
        self::STATUS_PROCESSING      => '处理中',
        self::STATUS_COMPLETED       => '已完成',
        self::STATUS_FAILED          => '失败',
        self::STATUS_ROLLED_BACK     => '已回滚',
        self::STATUS_ROLLBACK_FAILED => '回滚失败',
        self::STATUS_FEE_PENDING     => '待处理',
        self::STATUS_FEE_FAILED      => '待处理',
    ];
    const STATUS_TEXT_LABEL = [
        self::STATUS_PENDING         => 'info',
        self::STATUS_PROCESSING      => 'primary',
        self::STATUS_COMPLETED       => 'success',
        self::STATUS_FAILED          => 'danger',
        self::STATUS_ROLLED_BACK     => 'warning',
        self::STATUS_ROLLBACK_FAILED => 'danger',
        self::STATUS_FEE_PENDING     => 'info',
        self::STATUS_FEE_FAILED      => 'danger',
        self::STATUS_FEE_COMPLETED   => 'success',
    ];

    protected static function boot()
    {
        parent::boot();
        self::creating(function ($model) {
            $to_wallet_id = $model->to_wallet_id;
            $userWallet   = UserWallet::where('wallet_id', $model->to_wallet_id)->first();
            if ($userWallet) {
                $allBalance = WalletTransaction::query()
                    ->where('to_wallet_id', $model->to_wallet_id)
                    ->where('status', self::STATUS_COMPLETED)
                    ->sum('amount');
                if ($allBalance > self::WALLET_MAX_AMOUNT) {
                    $message = lecho('wallet_mall_wt');
                    throw new Exception(str_replace('{amount}', self::WALLET_MAX_AMOUNT, $message));
                }
            }
        });
    }

    public function getTransactionTypeTextAttribute()
    {
        return self::TRANSACTION_TYPES[$this->transaction_type] ?? '';
    }

    public function getStatusTextAttribute()
    {
        return self::STATUS_TEXTS[$this->status] ?? lecho($this->status);
    }

    /**
     * 获取代币类型文本
     */
    public function getTokenTypeTextAttribute()
    {
        return self::TOKEN_TYPES[$this->token_type] ?? $this->token_type;
    }

    /**
     * 获取代币类型完整值
     */
    public function getTokenTypeValueAttribute()
    {
        return self::TOKEN_TYPES_VALUES[$this->token_type] ?? $this->token_type;
    }

    /**
     * 设置 token_type 属性，确保存储的是简写形式
     */
    public function setTokenTypeAttribute($value)
    {
        // 如果传入的是完整值，将其转换为简写
        if (in_array($value, self::TOKEN_TYPES_VALUES)) {
            $this->attributes['token_type'] = array_search($value, self::TOKEN_TYPES_VALUES);
        } else {
            $this->attributes['token_type'] = $value;
        }
    }

    /**
     * 检查状态转换是否有效
     *
     * @param  string  $newStatus  新状态
     * @return bool 是否有效
     */
    public function canTransitionTo(string $newStatus): bool
    {
        return in_array($newStatus, self::STATUS_TRANSITIONS[$this->status] ?? []);
    }

    /**
     * 尝试转换状态
     *
     * @param  string  $newStatus  新状态
     * @param  array  $transactionData  交易数据
     * @return bool 是否成功
     * @throws \Exception 如果状态转换无效
     */
    public function transitionTo(string $newStatus, array $transactionData = []): bool
    {
        if (! $this->canTransitionTo($newStatus)) {
            throw new \Exception("无效的状态转换: {$this->status} -> {$newStatus}");
        }

        $this->status = $newStatus;

        if (! empty($transactionData) && in_array($newStatus, [self::STATUS_COMPLETED, self::STATUS_FEE_COMPLETED])) {
            $this->transaction_data = array_merge($this->transaction_data ?? [], $transactionData);
        }

        // 如果状态是已完成，设置完成时间
        if ($newStatus === self::STATUS_COMPLETED) {
            $this->error_message = null;
            $this->completed_at  = now();
        }

        return $this->save();
    }

    /**
     * 关联发送方用户
     */
    public function fromUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    /**
     * 关联接收方用户
     */
    public function toUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    public function fromWallet(): BelongsTo
    {
        return $this->belongsTo(UserWallet::class, 'from_wallet_id');
    }

    public function toWallet(): BelongsTo
    {
        return $this->belongsTo(UserWallet::class, 'to_wallet_id');
    }

    /**
     * Notes: 获取转出地址
     *
     * @Author: 玄尘
     * @Date: 2025/4/25 14:55
     * @return mixed|null
     */
    public function getFromWalletAddress()
    {
        if ($this->fromWallet) {
            return $this->fromWallet->wallet_address;
        }
        $adminWalletId = SystemConfig::getValue('wallet', 'wallet_admin_id');
        if ($adminWalletId == $this->from_wallet_id) {
            return SystemConfig::getValue('wallet', 'wallet_admin_address');
        }
        return '';
    }

    /**
     * Notes: 获取转入地址
     *
     * @Author: 玄尘
     * @Date: 2025/4/25 14:55
     * @return \Illuminate\Database\Eloquent\HigherOrderBuilderProxy|mixed|string|null
     */
    public function getToWalletAddress()
    {
        if ($this->toWallet) {
            return $this->toWallet->wallet_address;
        }
        $adminWalletId = SystemConfig::getValue('wallet', 'wallet_admin_id');
        if ($adminWalletId == $this->to_wallet_id) {
            return SystemConfig::getValue('wallet', 'wallet_admin_address');
        }
        return '';
    }

    /**
     * 更新交易状态为完成
     */
    public function markAsCompleted(array $transactionData = []): bool
    {
        try {
            return $this->transitionTo(self::STATUS_COMPLETED, $transactionData);
        } catch (\Exception $e) {
            // 如果状态转换无效，尝试直接设置
            $this->status        = self::STATUS_COMPLETED;
            $this->error_message = null;
            $this->completed_at  = now();

            if (! empty($transactionData)) {
                $this->transaction_data = array_merge($this->transaction_data ?? [], $transactionData);
            }

            return $this->save();
        }
    }

    /**
     * 更新交易状态为失败
     */
    public function markAsFailed(string $errorMessage, array $transactionData = []): bool
    {
        $data = array_merge($transactionData, ['error_message' => $errorMessage]);

        try {
            $result              = $this->transitionTo(self::STATUS_FAILED, $data);
            $this->error_message = $errorMessage;
            $this->save();
            return $result;
        } catch (\Exception $e) {
            // 如果状态转换无效，尝试直接设置
            $this->status        = self::STATUS_FAILED;
            $this->error_message = $errorMessage;

            return $this->save();
        }
    }

    /**
     * 更新交易状态为处理中
     */
    public function markAsProcessing(array $transactionData = []): bool
    {
        try {
            return $this->transitionTo(self::STATUS_PROCESSING, $transactionData);
        } catch (\Exception $e) {
            // 如果状态转换无效，尝试直接设置
            $this->status = self::STATUS_PROCESSING;

            if (! empty($transactionData)) {
                $this->transaction_data = array_merge($this->transaction_data ?? [], $transactionData);
            }

            return $this->save();
        }
    }

    /**
     * 更新交易状态为已回滚
     */
    public function markAsRolledBack(array $transactionData = []): bool
    {
        try {
            return $this->transitionTo(self::STATUS_ROLLED_BACK, $transactionData);
        } catch (\Exception $e) {
            // 如果状态转换无效，尝试直接设置
            $this->status = self::STATUS_ROLLED_BACK;

            if (! empty($transactionData)) {
                $this->transaction_data = array_merge($this->transaction_data ?? [], $transactionData);
            }

            return $this->save();
        }
    }

    /**
     * 更新交易状态为回滚失败
     */
    public function markAsRollbackFailed(string $errorMessage, array $transactionData = []): bool
    {
        $data = array_merge($transactionData, ['rollback_error' => $errorMessage]);

        try {
            return $this->transitionTo(self::STATUS_ROLLBACK_FAILED, $data);
        } catch (\Exception $e) {
            // 如果状态转换无效，尝试直接设置
            $this->status = self::STATUS_ROLLBACK_FAILED;

            if (! empty($transactionData)) {
                $this->transaction_data = array_merge($this->transaction_data ?? [], $data);
            }

            return $this->save();
        }
    }

    /**
     * 获取关联的主交易
     */
    public function mainTransaction()
    {
        return $this->belongsTo(WalletTransaction::class, 'main_transaction_id');
    }

    /**
     * 获取关联的手续费交易
     */
    public function feeTransaction()
    {
        return $this->hasOne(WalletTransaction::class, 'main_transaction_id');
    }

    /**
     * Notes: 是否可以执行转账
     *
     * @Author: 玄尘
     * @Date: 2025/4/25 14:48
     */
    public function canDo(): bool
    {
        return in_array($this->status, [
            self::STATUS_PENDING,
            self::STATUS_PROCESSING,
            self::STATUS_FAILED,
            self::STATUS_FEE_PENDING,
            self::STATUS_FEE_FAILED,
        ]);
    }

    public static function getFullTokenType($tokenType)
    {
        if (in_array($tokenType, [self::TOKEN_TYPE_WIKE_TOKEN, self::TOKEN_TYPE_USDC_TOKEN])) {
            return WalletTransaction::TOKEN_TYPES_VALUES[$tokenType];
        }
        return $tokenType;
    }
}
