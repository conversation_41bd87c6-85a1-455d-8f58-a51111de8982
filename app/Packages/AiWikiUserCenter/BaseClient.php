<?php

namespace App\Packages\AiWikiUserCenter;

class BaseClient
{
    private string $baseUri;
    private string $appId;
    private Sign   $signTool;
    private string $path;
    private array  $headers = [];
    private array  $params  = [];

    public function __construct()
    {
        $this->baseUri  = env('USER_CENTER_URI');
        $this->appId    = env('USER_CENTER_APP_ID', '');
        $this->signTool = new Sign();
        $this->headers  = [
            'Accept' => 'application/json',
            'App-Id' => $this->appId,
        ];
        $this->params   = [
            'app_id' => $this->appId,
            'time'   => now()->toDateTimeString(),
        ];
    }

    protected function setPath(string $path): self
    {
        $this->path = $path;
        return $this;
    }

    protected function setParams(array $params): self
    {
        $this->params = array_merge($this->params, $params);
        return $this;
    }

    protected function setHeaders(array $headers): self
    {
        $this->headers = array_merge($this->headers, $headers);
        return $this;
    }

    protected function post()
    {
        $client                = new \GuzzleHttp\Client();
        $url                   = $this->baseUri.'/'.$this->path;
        $this->headers['Sign'] = $this->signTool->sign($this->params);
        $response              = $client->post($url, [
            'headers' => $this->headers,
            'json'    => $this->params,
        ]);
        return new UserCenterResponse($response, $this->params);
    }
}