<?php

namespace App\Packages\AiWikiUserCenter;

use App\Packages\AiWikiUserCenter\Exception\SslException;

class Sign
{
    private string $mainPublicKey;
    private string $selfPrivateKey;
    private string $appKey;

    public function __construct()
    {
        $this->appKey         = env('USER_CENTER_APP_KEY', '');
        $this->mainPublicKey  = env('USER_CENTER_MAIN_PUBLIC_KEY', '');
        $this->selfPrivateKey = env('USER_CENTER_SELF_PRIVATE_KEY', '');
    }

    public function sign(array $params): string
    {
        $privateKeyString = chunk_split($this->selfPrivateKey, 64, "\n");
        $privateKeyString = "-----BEGIN RSA PRIVATE KEY-----\n".$privateKeyString."-----END RSA PRIVATE KEY-----\n";
        $privateKey       = openssl_pkey_get_private($privateKeyString);
        if ($privateKey === false) {
            throw new SslException('私钥错误');
        }
        $params['app_key'] = $this->appKey;
        ksort($params);
        $enString = '';
        foreach ($params as $k => $v) {
            if (is_array($v)) {
                $v = json_encode($v, JSON_UNESCAPED_UNICODE);
            }
            if (is_null($v)) {
                $v = '';
            }
            $enString .= $k.'='.$v.'&';
        }
        $enString = rtrim($enString, '&');
        if (! openssl_private_encrypt(md5($enString), $encryptedData, $privateKey)) {
            throw new SslException('加密失败');
        }
        return base64_encode($encryptedData);
    }

    public function verify(array $params, string $verifyString): bool
    {
        $publicKeyString = chunk_split($this->mainPublicKey, 64, "\n");
        $publicKeyString = "-----BEGIN PUBLIC KEY-----\n".$publicKeyString."-----END PUBLIC KEY-----\n";
        $publicKey       = openssl_pkey_get_public($publicKeyString);
        if (! $publicKey) {
            throw new SslException('公钥错误');
        }

        if (! openssl_public_decrypt(base64_decode($verifyString), $encryptedData, $publicKey)) {
            throw new SslException('验证加密失败');
        }
        $params['app_key'] = $this->appKey;
        ksort($params);
        $signString = '';
        foreach ($params as $k => $v) {
            if (is_array($v)) {
                $v = json_encode($v, JSON_UNESCAPED_UNICODE);
            }
            if (is_null($v)) {
                $v = '';
            }
            $signString .= $k.'='.$v.'&';
        }
        $signString = rtrim($signString, '&');
        return $encryptedData == md5($signString);
    }

    protected function getAppKey(): string
    {
        return $this->appKey;
    }

    protected function getSelfPrivateKey(): string
    {
        return $this->selfPrivateKey;
    }

    protected function getMainPublicKey(): string
    {
        return $this->mainPublicKey;
    }
}