<?php

namespace App\Packages\AiWikiUserCenter\User;

use App\Packages\AiWikiUserCenter\BaseClient;

class Client extends BaseClient
{
    /**
     * @param  string  $email  电子邮箱
     * @param  string  $nickname  昵称
     * @return \App\Packages\AiWikiUserCenter\UserCenterResponse
     */
    public function emailRegister(string $email, string $callback = '')
    {
        return $this->setPath('auth/register')
            ->setParams([
                'email'    => $email,
                'callback' => $callback,
            ])
            ->post();
    }

    /**
     * @param  string  $userkey  用户key
     * @param  string  $type  账户类型
     * @return \App\Packages\AiWikiUserCenter\UserCenterResponse
     */
    public function getBalance(string $userkey, string $type)
    {
        return $this->setPath('accounts/get_balance')
            ->setParams([
                'user_key' => $userkey,
                'type'     => $type,
            ])
            ->post();
    }

    /**
     * @param  string  $userkey  用户key
     * @param  string  $type  账户类型
     * @return \App\Packages\AiWikiUserCenter\UserCenterResponse
     */
    public function getManyBalance(string $userkey, array $types)
    {
        return $this->setPath('accounts/get_many_balance')
            ->setParams([
                'user_key' => $userkey,
                'types'    => $types,
            ])
            ->post();
    }

    /**
     * 账户变动
     *
     * @param  string  $userkey  用户key
     * @param  string  $type  账户类型
     * @param  float  $amount  变动金额
     * @param  string  $remark  描述
     * @param  string  $freezeTime  冻结结束时间
     * @param  array  $source  数据源
     * @return \App\Packages\AiWikiUserCenter\UserCenterResponse
     */
    public function addBalance(
        string $userkey,
        string $type,
        float $amount,
        string $remark = '',
        string $freezeTime = '',
        array $source = []
    ) {
        return $this->setPath('accounts/add_balance')
            ->setParams([
                'user_key'   => $userkey,
                'type'       => $type,
                'amount'     => $amount,
                'remark'     => $remark,
                'freezed_at' => $freezeTime,
                'source'     => $source,
            ])
            ->post();
    }

    /**
     * @param  string  $userkey
     * @param  string  $type
     * @param  int  $page
     * @param  int  $limit
     * @param  array  $source  搜索数据源 例如 账变数据源传入了 order_id=1, 查询是可传入['order_id'=>1],查询
     * @return \App\Packages\AiWikiUserCenter\UserCenterResponse
     */
    public function getList(
        string $userkey,
        string $type,
        int $page = 1,
        int $limit = 10,
        array $where = [],
        array $source = [],
    ) {
        return $this->setPath('accounts/lists')
            ->setParams([
                'user_key' => $userkey,
                'type'     => $type,
                'page'     => $page,
                'limit'    => $limit,
                'where'    => $where,
                'source'   => $source,
            ])
            ->post();
    }

    /**
     * 获取用户中心所有账户
     *
     * @param  string  $userkey
     * @return \App\Packages\AiWikiUserCenter\UserCenterResponse
     */
    public function getAllBalance(string $userkey)
    {
        return $this->setPath('accounts/all_balance')
            ->setParams([
                'user_key' => $userkey,
            ])
            ->post();
    }

    public function trading(
        string $type,
        string $formKey,
        string $toKey,
        float $amount,
        array $remark = [],
        array $source = []
    ) {
        return $this->setPath('accounts/trading')
            ->setParams([
                'type'     => $type,
                'from_key' => $formKey,
                'to_key'   => $toKey,
                'amount'   => $amount,
                'remark'   => $remark,
                'source'   => $source,
            ])
            ->post();
    }

    /**
     * Notes: 获取所有用户的coin
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 13:33
     * @return \App\Packages\AiWikiUserCenter\UserCenterResponse
     */
    public function getAllCoin()
    {
        return $this->setPath('accounts/all_coins')
            ->post();
    }

}