<?php

namespace App\Packages\AiWikiUserCenter;

use App\Packages\AiWikiUserCenter\Exception\SslException;
use Psr\Http\Message\ResponseInterface;

class UserCenterResponse
{
    protected int          $code    = 200;
    protected string       $message = 'OK';
    protected string       $sign;
    protected array        $source  = [];
    protected bool         $success = false;
    protected array|string $data;
    private Sign           $signTool;

    public function __construct(ResponseInterface $response, protected array $params = [])
    {
        $this->code     = $response->getStatusCode();
        $this->signTool = new Sign();
        if ($this->code == 200) {
            $this->success = true;
            $this->source  = json_decode($response->getBody()->getContents(), true);
            if ($this->source['code'] != 200) {
                $this->success = false;
                $this->message = $this->source['message'];
            } else {
                $this->data = $this->source['data'] ?? [];
                try {
                    $this->sign = $response->hasHeader('Sign') ? $response->getHeader('Sign')[0] : '';
                    if (! $this->signTool->verify($this->data, $this->sign)) {
                        $this->success = false;
                        $this->message = '返回内容验签失败';
                    }
                } catch (SslException $e) {
                    $this->success = false;
                    $this->message = $e->getMessage();
                } catch (\Exception $e) {
                    $this->success = false;
                    $this->message = '解密后内容不是JSON格式';
                }
            }
        } else {
            $this->success = false;
            $this->message = $response->getBody()->getContents();
        }
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function isError(): bool
    {
        return ! $this->isSuccess();
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function toArray(): array
    {
        return $this->data;
    }

    public function __get(string $name)
    {
        return $this->data[$name] ?? null;
    }
}