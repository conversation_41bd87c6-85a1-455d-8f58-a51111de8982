<?php

namespace App\Packages\Shinami\Key;

use App\Models\SystemConfig;
use App\Models\User;
use App\Packages\Shinami\ShinamiClient;
use App\Packages\Shinami\ShinamiException;
use App\Packages\Shinami\ShinamiResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class Client extends ShinamiClient
{
    /**
     * 缓存前缀
     */
    protected const CACHE_PREFIX = 'wallet_session_token:';

    /**
     * 缓存过期时间（分钟）
     */
    protected const CACHE_TTL = 9;

    /**
     * 管理员钱包缓存键
     */
    protected const ADMIN_WALLET_KEY = 'admin_wallet_token';

    /**
     * 创建会话令牌
     *
     * 在创建钱包、签名或执行交易之前，必须生成会话令牌
     * 会话令牌有效期为10分钟，可以在此期间重复使用
     *
     * @param  string  $secret  用于加密和解密钱包私钥的密钥
     * @param  string|null  $endpoint  手动指定的端点URL，默认为/sui/key/v1
     * @return ShinamiResponse 包含会话令牌的响应
     */
    public function createSession(string $secret): ShinamiResponse
    {
        // 参数验证
        if (empty($secret)) {
            return $this->createErrorResponse(ShinamiException::INVALID_WALLET_SECRET);
        }

        // 记录日志
        $this->logInfo('创建会话令牌', [
            'secret_length' => strlen($secret)
        ]);

        $this->params = [$secret];
        $response     = $this->post('shinami_key_createSession');

        // 记录响应信息
        if ($response->isError()) {
            $this->logError('创建会话令牌失败', [
                'error'     => $response->getMessage(),
                'errorCode' => $response->getErrorCode()
            ]);
        } else {
            $this->logInfo('创建会话令牌成功');
        }

        return $response;
    }

    /**
     * 获取用户会话令牌
     *
     * @param  int|string  $userId  用户ID
     * @return string 会话令牌
     * @throws ShinamiException
     */
    public function getSessionToken($userId): string
    {
        $cacheKey = self::CACHE_PREFIX.$userId;

        // 尝试从缓存获取令牌
        $token = Cache::get($cacheKey);
        if ($token) {
            return $token;
        }

        // 缓存中没有，创建新令牌
        $user = User::find($userId);
        if (! $user) {
            throw new ShinamiException('用户不存在');
        }

        if (! $user->wallet || empty($user->wallet->wallet_id)) {
            throw new ShinamiException('用户钱包尚未创建成功，请稍后再试');
        }

        // 解密钱包密钥
        $walletSecret = Crypt::decryptString($user->wallet->wallet_secret);

        // 创建会话令牌
        $sessionToken = $this->createSession($walletSecret);
        if ($sessionToken->isError()) {
            throw new ShinamiException($sessionToken->getMessage());
        }

        // 缓存令牌
        $token = $sessionToken->result;
        Cache::put($cacheKey, $token, now()->addMinutes(self::CACHE_TTL));

        return $token;
    }

    /**
     * 获取管理员钱包会话令牌
     *
     * @return string 会话令牌
     * @throws ShinamiException
     */
    public function getAdminSessionToken(): string
    {
        $cacheKey = self::CACHE_PREFIX.self::ADMIN_WALLET_KEY;

        // 尝试从缓存获取令牌
        $token = Cache::get($cacheKey);
        if ($token) {
            return $token;
        }

        // 缓存中没有，创建新令牌
        $adminWalletSecret = SystemConfig::getValue('wallet', 'wallet_admin_secret');
        if (empty($adminWalletSecret)) {
            throw new ShinamiException('管理员钱包密钥未配置，请联系管理员！');
        }

        // 解密管理员钱包密钥
        try {
            $adminWalletSecret = Crypt::decryptString($adminWalletSecret);
        } catch (\Exception $e) {
            $this->logError('解密管理员钱包密钥失败', [
                'error' => $e->getMessage()
            ]);
            throw new ShinamiException('管理员钱包密钥解密失败，请联系管理员！');
        }

        // 创建会话令牌
        $sessionToken = $this->createSession($adminWalletSecret);
        if ($sessionToken->isError()) {
            throw new ShinamiException($sessionToken->getMessage());
        }

        // 缓存令牌
        $token = $sessionToken->result;
        Cache::put($cacheKey, $token, now()->addMinutes(self::CACHE_TTL));

        return $token;
    }

    /**
     * 刷新用户会话令牌
     *
     * @param  int|string  $userId  用户ID
     * @return string 新的会话令牌
     * @throws ShinamiException
     */
    public function refreshSessionToken($userId): string
    {
        $cacheKey = self::CACHE_PREFIX.$userId;

        // 删除缓存中的令牌
        Cache::forget($cacheKey);

        // 创建新令牌
        return $this->getSessionToken($userId);
    }

    /**
     * 刷新管理员会话令牌
     *
     * @return string 新的会话令牌
     * @throws ShinamiException
     */
    public function refreshAdminSessionToken(): string
    {
        $cacheKey = self::CACHE_PREFIX.self::ADMIN_WALLET_KEY;

        // 删除缓存中的令牌
        Cache::forget($cacheKey);

        // 创建新令牌
        return $this->getAdminSessionToken();
    }

    /**
     * 清除所有会话令牌缓存
     */
    public function clearAllTokens(): void
    {
        $keys = Cache::get('cache:'.self::CACHE_PREFIX.'*');
        foreach ($keys as $key) {
            Cache::forget($key);
        }

        $this->logInfo('已清除所有会话令牌缓存');
    }
}
