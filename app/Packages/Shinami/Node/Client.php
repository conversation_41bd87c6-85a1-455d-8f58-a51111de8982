<?php

namespace App\Packages\Shinami\Node;

use App\Models\WalletTransaction;
use App\Packages\Shinami\ShinamiClient;
use App\Packages\Shinami\ShinamiResponse;

class Client extends ShinamiClient
{
    /**
     * 获取钱包余额
     *
     * @param  string  $address  钱包地址
     * @param  string|null  $coinType  代币类型，默认为SUI
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 包含余额信息的响应
     */
    public function getBalance(string $address, ?string $coinType = null, ?string $endpoint = null): ShinamiResponse
    {
        // 参数验证

        $params = [
            'owner' => $address
        ];

        if ($coinType) {
            $params['coin_type'] = WalletTransaction::getFullTokenType($coinType);
        }

        $this->params = $params;

        // 记录日志
//        $this->logInfo('获取钱包余额', [
//            'address'  => $address,
//            'coinType' => $coinType
//        ]);

        $response = $this->request('suix_getBalance', 'POST', $endpoint);

        // 记录响应信息
        if ($response->isError()) {
            $this->logError('获取钱包余额失败', [
                'address'   => $address,
                'coinType'  => $coinType,
                'error'     => $response->getMessage(),
                'errorCode' => $response->getErrorCode()
            ]);
        }

        return $response;
    }

    /**
     * 获取所有代币余额
     *
     * @param  string  $address  钱包地址
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 包含所有代币余额的响应
     */
    public function getAllBalances(string $address, ?string $endpoint = null): ShinamiResponse
    {
        $this->params = [
            $address
        ];

        // 记录日志
//        $this->logInfo('获取所有代币余额', [
//            'address' => $address
//        ]);

        $response = $this->request('suix_getAllBalances', 'POST', $endpoint);

        // 记录响应信息
        if ($response->isError()) {
            $this->logError('获取所有代币余额失败', [
                'address'   => $address,
                'error'     => $response->getMessage(),
                'errorCode' => $response->getErrorCode()
            ]);
        }

        return $response;
    }

    /**
     * 获取交易详情
     *
     * @param  string  $digest  交易哈希
     * @param  array  $options  选项
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 包含交易详情的响应
     */
    public function getTransaction(string $digest, array $options = [], ?string $endpoint = null): ShinamiResponse
    {
        if (empty($options)) {
            $options = [
                'showBalanceChanges' => true,
                'showContent'        => true,
                'showEffects'        => true,
                'showEvents'         => true,
                'showObjectChanges'  => true,
                'showRaw'            => true
            ];
        }
        $this->params = [
            'digest'  => $digest,
            'options' => $options
        ];

        return $this->request('sui_getTransactionBlock', 'POST', $endpoint);
    }

    /**
     * 获取代币总供应量
     *
     * @param  string  $coinType  代币类型
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 包含代币总供应量的响应
     */
    public function getTotalSupply(string $coinType, ?string $endpoint = null): ShinamiResponse
    {
        $this->params = [
            $coinType
        ];

        return $this->request('suix_getTotalSupply', 'POST', $endpoint);
    }

    /**
     * 获取代币列表
     *
     * @param  string  $address  钱包地址
     * @param  string|null  $coinType  代币类型
     * @param  int  $limit  限制数量
     * @param  string|null  $cursor  游标
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 包含代币列表的响应
     */
    public function getCoins(
        string $address,
        ?string $coinType = null,
        int $limit = 100,
        ?string $cursor = null,
        ?string $endpoint = null
    ): ShinamiResponse {
        $params        = [
            'owner' => $address,
            'limit' => $limit
        ];
        $fullTokenType = $coinType;
        $fullTokenType = WalletTransaction::getFullTokenType($fullTokenType);
        if ($fullTokenType) {
            $params['coinType'] = $fullTokenType;
        }

        if ($cursor) {
            $params['cursor'] = $cursor;
        }

        $this->params = $params;

        return $this->request('suix_getCoins', 'POST', $endpoint);
    }

    /**
     * 获取所有代币列表
     *
     * @param  string  $address  钱包地址
     * @param  int  $limit  限制数量
     * @param  string|null  $cursor  游标
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 包含所有代币列表的响应
     */
    public function getAllCoins(
        string $address,
        int $limit = 100,
        ?string $cursor = null,
        ?string $endpoint = null
    ): ShinamiResponse {
        $params = [
            'owner' => $address,
            'limit' => $limit
        ];

        if ($cursor) {
            $params['cursor'] = $cursor;
        }

        $this->params = $params;

        return $this->request('suix_getAllCoins', 'POST', $endpoint);
    }

    /**
     * 获取Gas价格
     *
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 包含Gas价格的响应
     */
    public function getReferenceGasPrice(?string $endpoint = null): ShinamiResponse
    {
        $this->params = [];

        return $this->request('suix_getReferenceGasPrice', 'POST', $endpoint);
    }
}
