<?php

namespace App\Packages\Shinami;

use App\Packages\Shinami\Key\Client as KeyClient;
use App\Packages\Shinami\Node\Client as NodeClient;
use App\Packages\Shinami\Transaction\Client as TransactionClient;
use App\Packages\Shinami\Wallet\Client as WalletClient;

class Shinami
{
    /**
     * Notes: 获取钱包操作客户端
     *
     * @Author: 玄尘
     * @Date: 2025/4/22 16:13
     * @return \App\Packages\Shinami\Wallet\Client
     */
    public static function wallet(): WalletClient
    {
        return new WalletClient();
    }

    /**
     * Notes: 获取交易操作客户端
     *
     * @Author: 玄尘
     * @Date: 2025/4/22 16:13
     * @return \App\Packages\Shinami\Transaction\Client
     */
    public static function transaction(): TransactionClient
    {
        return new TransactionClient();
    }

    /**
     * Notes: 获取密钥操作客户端
     *
     * @Author: 玄尘
     * @Date: 2025/4/22 16:13
     * @return \App\Packages\Shinami\Key\Client
     */
    public static function key(): KeyClient
    {
        return new KeyClient();
    }

    /**
     * Notes: 获取节点操作客户端
     *
     * @Author: 玄尘
     * @Date: 2025/4/22 16:13
     * @return \App\Packages\Shinami\Node\Client
     */
    public static function node(): NodeClient
    {
        return new NodeClient();
    }
}