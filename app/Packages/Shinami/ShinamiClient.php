<?php

namespace App\Packages\Shinami;

use App\Models\SystemConfig;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class ShinamiClient
{
    protected string  $baseUrl;
    protected Client  $httpClient;
    protected string  $apiKey;
    protected array   $result;
    protected array   $params   = [];
    protected ?string $endpoint = '';

    public function __construct()
    {
        $this->baseUrl = SystemConfig::getValue('wallet', 'shinami_url');
        $this->apiKey  = SystemConfig::getValue('wallet', 'wallet_access_key');
        $this->initializeClient();
    }

    protected function initializeClient(): void
    {
        $this->checkBaseData();
        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'timeout'  => 30,
            'headers'  => [
                'X-API-Key'    => $this->apiKey,
                'Accept'       => 'application/json',
                'Content-Type' => 'application/json',
            ]
        ]);
    }

    public function setBaseUrl($url)
    {
        $this->baseUrl = $url;
        $this->initializeClient();
    }

    public function checkBaseData(): void
    {
        if (empty($this->baseUrl)) {
            throw new ShinamiException('请先配置Shinami接口地址');
        }
        if (empty($this->apiKey)) {
            throw new ShinamiException('请先配置Shinami API密钥');
        }
    }

    /**
     * 发送JSON-RPC请求
     *
     * @param  string  $method  JSON-RPC方法名
     * @param  array  $params  参数
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 响应对象
     */
    protected function sendJsonRpcRequest(string $method, array $params = [], ?string $endpoint = null): ShinamiResponse
    {
        // 确定端点
        if ($endpoint === null) {
            if (strpos($method, 'shinami_key_') === 0) {
                $endpoint = '/sui/key/v1';
            } elseif (strpos($method, 'shinami_wal_') === 0 || strpos($method, 'shinami_walx_') === 0) {
                $endpoint = '/sui/wallet/v1';
            } elseif (strpos($method, 'suix_') === 0 || strpos($method, 'sui_') === 0) {
                $endpoint = '/sui/node/v1';
            } else {
                $endpoint = '/sui/v1';
            }
        }
        $this->endpoint = $endpoint;

        // 构建请求
        $requestData = [
            'jsonrpc' => '2.0',
            'method'  => $method,
            'params'  => $params ?: $this->params,
            'id'      => 1
        ];

        try {
            $response = $this->httpClient->request('POST', $endpoint, [
                'json' => $requestData
            ]);
            return new ShinamiResponse($response, $requestData, true, $endpoint, 'POST');
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * 发送REST请求
     *
     * @param  string  $path  请求路径
     * @param  string  $method  HTTP方法（GET、POST等）
     * @param  array  $params  参数
     * @return ShinamiResponse 响应对象
     */
    protected function sendRestRequest(string $path, string $method, array $params = []): ShinamiResponse
    {
        $this->endpoint = $path;
        $paramsName     = $method === 'GET' || $method === 'DELETE' ? 'query' : 'json';
        $requestParams  = $params ?: $this->params;

        try {
            $response = $this->httpClient->request($method, $path, [
                $paramsName => $requestParams
            ]);
            return new ShinamiResponse($response, $requestParams, false, $this->endpoint, 'POST');
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * 发送请求
     *
     * @param  string  $path  请求路径或JSON-RPC方法名
     * @param  string  $method  HTTP方法（GET、POST等）
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 响应对象
     */
    public function request(string $path, string $method, ?string $endpoint = null): ShinamiResponse
    {
        // 检查是否是JSON-RPC请求
        $isJsonRpc = strpos($path, 'shinami_') === 0 || strpos($path, 'sui_') === 0 || strpos($path, 'suix_') === 0;

        if ($isJsonRpc) {
            return $this->sendJsonRpcRequest($path, $this->params, $endpoint);
        } else {
            return $this->sendRestRequest($path, $method, $this->params);
        }
    }

    protected function handleException(Exception $e): ShinamiResponse
    {
        // 记录异常日志
        Log::channel('shinami')
            ->error('Shinami API 请求异常', [
                'message'  => $e->getMessage(),
                'endpoint' => $this->endpoint,
                'params'   => $this->params,
                'trace'    => $e->getTraceAsString()
            ]);

        if (method_exists($e, 'getResponse')) {
            $response = $e->getResponse();
            if ($response instanceof ResponseInterface) {
                $resultCode = $response->getStatusCode();
                // 尝试解析响应体
                try {
                    $result    = json_decode($response->getBody()->getContents(), true);
                    $errorCode = $result['error']['code'] ?? 'UNKNOWN_ERROR';

                    // 根据HTTP状态码和错误码返回适当的错误响应
                    if ($resultCode == 404) {
                        return $this->createErrorResponse(ShinamiException::HTTP_NOT_FOUND);
                    } elseif ($resultCode == 401) {
                        return $this->createErrorResponse(ShinamiException::INVALID_API_KEY);
                    } elseif ($resultCode == 400) {
                        return $this->createErrorResponse(ShinamiException::INVALID_PARAMS);
                    } else {
                        return $this->createErrorResponse($errorCode);
                    }
                } catch (\Exception $jsonException) {
                    // 如果无法解析JSON，则使用HTTP状态码
                    return $this->createErrorResponse((string) $resultCode, ['result' => $jsonException->getMessage()]);
                }
            }
        }

        // 如果是ShinamiException，直接使用其错误码
        if ($e instanceof ShinamiException) {
            return $this->createErrorResponse($e->getCode() ?: ShinamiException::UNKNOWN_ERROR,
                ['result' => $e->getMessage()]);
        }

        // 默认返回未知错误
        return $this->createErrorResponse($e->getCode(), ['result' => $e->getMessage()]);
    }

    /**
     * 创建统一的错误响应
     *
     * @param  string  $errorCode  错误码
     * @param  array  $params  参数
     * @return ShinamiResponse 错误响应对象
     */
    protected function createErrorResponse(string $errorCode, array $params = []): ShinamiResponse
    {
        $errorMessage = ShinamiException::getErrorMessage($errorCode, $params);

        // 记录错误日志
        Log::channel('shinami')->error('Shinami API 错误', [
            'errorCode'    => $errorCode,
            'errorMessage' => $errorMessage,
            'endpoint'     => $this->endpoint,
            'params'       => $this->params
        ]);

        return ShinamiResponse::error($errorMessage, $this->params, false, $this->endpoint, 'ERROR', $params);
    }

    /**
     * 发送GET请求
     *
     * @param  string  $path  请求路径或JSON-RPC方法名
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 响应对象
     */
    protected function get(string $path, ?string $endpoint = null): ShinamiResponse
    {
        return $this->request($path, 'GET', $endpoint);
    }

    /**
     * 发送POST请求
     *
     * @param  string  $path  请求路径或JSON-RPC方法名
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 响应对象
     */
    protected function post(string $path, ?string $endpoint = null): ShinamiResponse
    {
        return $this->request($path, 'POST', $endpoint);
    }

    /**
     * 发送PUT请求
     *
     * @param  string  $path  请求路径或JSON-RPC方法名
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 响应对象
     */
    protected function put(string $path, ?string $endpoint = null): ShinamiResponse
    {
        return $this->request($path, 'PUT', $endpoint);
    }

    /**
     * 发送DELETE请求
     *
     * @param  string  $path  请求路径或JSON-RPC方法名
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 响应对象
     */
    protected function delete(string $path, ?string $endpoint = null): ShinamiResponse
    {
        return $this->request($path, 'DELETE', $endpoint);
    }

    /**
     * 记录错误日志
     *
     * @param  string  $message  日志消息
     * @param  array  $context  上下文数据
     */
    protected function logError(string $message, array $context = []): void
    {
        Log::channel('shinami')->error($message, $context);
    }

    /**
     * 记录警告日志
     *
     * @param  string  $message  日志消息
     * @param  array  $context  上下文数据
     */
    protected function logWarning(string $message, array $context = []): void
    {
        Log::channel('shinami')->warning($message, $context);
    }

    /**
     * 记录信息日志
     *
     * @param  string  $message  日志消息
     * @param  array  $context  上下文数据
     */
    protected function logInfo(string $message, array $context = []): void
    {
        Log::channel('shinami')->info($message, $context);
    }

    /**
     * 记录调试日志
     *
     * @param  string  $message  日志消息
     * @param  array  $context  上下文数据
     */
    protected function logDebug(string $message, array $context = []): void
    {
        Log::channel('shinami')->debug($message, $context);
    }
}