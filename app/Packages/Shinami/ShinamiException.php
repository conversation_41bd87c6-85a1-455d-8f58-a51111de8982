<?php

namespace App\Packages\Shinami;

class ShinamiException extends \Exception
{
    // 常用HTTP状态码
    public const HTTP_BAD_REQUEST           = 400;
    public const HTTP_UNAUTHORIZED          = 401;
    public const HTTP_NOT_FOUND             = 404;
    public const HTTP_INTERNAL_SERVER_ERROR = 500;

    // 常用错误码
    public const INVALID_API_KEY       = 'INVALID_API_KEY';
    public const INVALID_WALLET_ID     = 'INVALID_WALLET_ID';
    public const INVALID_WALLET_SECRET = 'INVALID_WALLET_SECRET';
    public const WALLET_NOT_FOUND      = 'WALLET_NOT_FOUND';
    public const INSUFFICIENT_BALANCE  = 'INSUFFICIENT_BALANCE';
    public const TRANSACTION_FAILED    = 'TRANSACTION_FAILED';
    public const RATE_LIMIT_EXCEEDED   = 'RATE_LIMIT_EXCEEDED';
    public const INTERNAL_ERROR        = 'INTERNAL_ERROR';
    public const UNKNOWN_ERROR         = 'UNKNOWN_ERROR';
    public const INVALID_PARAMS        = '-32602';
    public const PARSE_ERROR           = 'PARSE_ERROR';
    public const CUSTOM_ERROR          = 'CUSTOM_ERROR';
    public const NETWORK_ERROR         = 'NETWORK_ERROR';
    public const TIMEOUT_ERROR         = 'TIMEOUT_ERROR';
    public const CONFIG_ERROR          = 'CONFIG_ERROR';

    // 错误消息映射
    private static array $errorMessages = [
        self::INVALID_API_KEY            => '无效的API密钥',
        self::INVALID_WALLET_ID          => '无效的钱包ID',
        self::INVALID_WALLET_SECRET      => '无效的钱包密钥',
        self::WALLET_NOT_FOUND           => '钱包不存在',
        self::INSUFFICIENT_BALANCE       => '余额不足',
        self::TRANSACTION_FAILED         => '交易失败',
        self::RATE_LIMIT_EXCEEDED        => '请求频率超限',
        self::INTERNAL_ERROR             => '服务器内部错误',
        self::UNKNOWN_ERROR              => '未知错误',
        self::HTTP_NOT_FOUND             => '接口不存在',
        self::INVALID_PARAMS             => '参数错误',
        self::PARSE_ERROR                => '数据解析错误',
        self::CUSTOM_ERROR               => '自定义错误',
        self::NETWORK_ERROR              => '网络连接错误',
        self::TIMEOUT_ERROR              => '请求超时',
        self::CONFIG_ERROR               => '配置错误',
        // 通用错误处理
        self::HTTP_BAD_REQUEST           => '请求参数错误',
        self::HTTP_UNAUTHORIZED          => '未授权或认证失败',
        self::HTTP_INTERNAL_SERVER_ERROR => '服务器内部错误'
    ];

    public function __construct(string $message = '', int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * 获取错误消息
     *
     * @param  string  $code  错误码
     * @param  array  $params  格式化参数
     * @return string 错误消息
     */
    public static function getErrorMessage(string $code, array $params = []): string
    {
        if (! isset(self::$errorMessages[$code])) {
            return self::$errorMessages[self::UNKNOWN_ERROR];
        }

        $message = self::$errorMessages[$code];
        if (! empty($params)) {
            try {
                $message = vsprintf($message, $params);
            } catch (\ValueError $e) {
                return $message;
            }
        }

        return $message;
    }

    /**
     * 验证错误码是否有效
     *
     * @param  string  $code  错误码
     * @return bool 是否有效
     */
    public static function isValidErrorCode(string $code): bool
    {
        return isset(self::$errorMessages[$code]);
    }

    /**
     * 获取所有错误码
     *
     * @return array 错误码数组
     */
    public static function getAllErrorCodes(): array
    {
        return array_keys(self::$errorMessages);
    }

    /**
     * 添加自定义错误码和错误消息
     *
     * @param  string  $code  错误码
     * @param  string  $message  错误消息
     * @return void
     */
    public static function addErrorMessage(string $code, string $message): void
    {
        self::$errorMessages[$code] = $message;
    }
}