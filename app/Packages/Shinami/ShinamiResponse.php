<?php

namespace App\Packages\Shinami;

use App\Models\SystemConfig;
use App\Models\WalletOperation;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class ShinamiResponse
{
    protected bool   $success = false;
    protected string $message = '';
    protected array  $data    = [];
    protected array  $params  = [];

    protected bool   $isJsonRpc = false;
    protected mixed  $result    = null;
    protected string $endpoint  = '';
    protected string $method    = '';
    protected string $errorCode = '';

    public function __construct(
        ResponseInterface|string $response,
        array $params = [],
        bool $isJsonRpc = false,
        string $endpoint = '',
        string $method = '',
        array $data = []
    ) {
        $this->params    = $params;
        $this->isJsonRpc = $isJsonRpc;
        $this->endpoint  = $endpoint;
        $this->method    = $method;

        if ($response instanceof ResponseInterface) {
            $this->handleResponse($response);
        } else {
            $this->data = $data;
            $this->handleError($response);
        }
        // 记录API调用日志
        $this->logApiCall();
    }

    protected function handleResponse(ResponseInterface $response): void
    {
        try {
            $content    = $response->getBody()->getContents();
            $result     = json_decode($content, true);
            $this->data = $this->result = $result;
            if ($response->getStatusCode() === 200) {
                if ($this->isJsonRpc) {
                    // 处理JSON-RPC响应
                    if (isset($result['error'])) {
                        $errorCode       = $result['error']['code'] ?? 'UNKNOWN_ERROR';
                        $this->errorCode = $errorCode;
                        $errorMessage    = ShinamiException::getErrorMessage($errorCode) ?? $result['error']['message'] ?? '未知错误';
                        $this->handleError($errorMessage);
                    } else {
                        $this->success = true;
                        $this->result  = $result['result'] ?? null;
                        $this->data    = ['result' => $this->result];
                    }
                } else {
                    // 处理普通REST响应
                    if ($result['success'] != true) {
                        $errorCode       = $result['code'] ?? 'UNKNOWN_ERROR';
                        $this->errorCode = $errorCode;
                        $errorMessage    = $result['code'] ?? '未知错误';
                        $this->handleError($errorMessage);
                    } else {
                        $this->success = true;
                        $this->data    = ['result' => $result['data']];
                    }
                }
            } else {
                $errorCode       = (string) $response->getStatusCode();
                $this->errorCode = $errorCode;
                $errorMessage    = ShinamiException::getErrorMessage($errorCode) ?? 'HTTP错误: '.$response->getStatusCode();
                $this->handleError($errorMessage);
            }
        } catch (\Exception $e) {
            $this->errorCode = 'PARSE_ERROR';
            $this->handleError('响应数据解析失败: '.$e->getMessage());
        }
    }

    protected function handleError(string $message): void
    {
        $this->success = false;
        $this->message = $message;
    }

    public static function error(
        string $message,
        array $params = [],
        bool $isJsonRpc = false,
        string $endpoint = '',
        string $method = '',
        array $data = [],
    ): self {
        $response            = new static($message, $params, $isJsonRpc, $endpoint, $method, $data);
        $response->errorCode = 'CUSTOM_ERROR';
        return $response;
    }

    public function isError(): bool
    {
        return ! $this->isSuccess();
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    public function toArray(): array
    {
        return [
            'success'   => $this->success,
            'message'   => $this->message,
            'data'      => $this->data,
            'params'    => $this->params,
            'errorCode' => $this->errorCode
        ];
    }

    public function __get(string $name): mixed
    {
        if ($name == 'result') {
            return $this->data['result'] ?? null;
        }

        return $this->data['result'][$name] ?? null;
    }

    public function __isset(string $name): bool
    {
        return isset($this->data[$name]);
    }

    /**
     * 记录API调用日志
     *
     * 将API调用信息记录到数据库中
     */
    protected function logApiCall(): void
    {
        try {
            // 检查是否需要记录日志
            $shouldLog = SystemConfig::getValue('wallet', 'log_api_calls', true);
            if (! $shouldLog) {
                return;
            }

            // 过滤不需要记录的端点
            $skipEndpoints = SystemConfig::getValue('wallet', 'skip_log_endpoints') ?? ['getBalance', 'getAllBalances'];
            foreach ($skipEndpoints as $endpoint) {
                if (strpos($this->endpoint, $endpoint) !== false) {
                    return;
                }
            }

            // 记录到数据库
            WalletOperation::create([
                'base_url'      => SystemConfig::getValue('wallet', 'shinami_url'),
                'endpoint'      => $this->endpoint,
                'method'        => $this->method,
                'input_data'    => $this->params,
                'response_data' => $this->getData(),
                'is_success'    => $this->success,
                'error_message' => $this->success ? null : $this->message,
            ]);
        } catch (\Exception $e) {
            // 记录日志失败不应影响主要功能
            Log::channel('shinami')
                ->error('Failed to log API call: '.$e->getMessage(), [
                    'endpoint' => $this->endpoint,
                    'method'   => $this->method
                ]);
        }
    }
}