<?php

namespace App\Packages\Shinami\Wallet;

use App\Models\SystemConfig;
use App\Models\UserWallet;
use App\Packages\Shinami\ShinamiClient;
use App\Packages\Shinami\ShinamiException;
use App\Packages\Shinami\ShinamiResponse;
use Illuminate\Support\Facades\Log;

class Client extends ShinamiClient
{
    /**
     * Notes: 创建钱包
     *
     * @Author: 玄尘
     * @Date: 2025/4/22 15:45
     * @param  string  $walletId
     * @param  string  $sessionToken
     * @return \App\Packages\Shinami\ShinamiResponse
     */
    public function createWallet(string $walletId, string $sessionToken): ShinamiResponse
    {
        // 参数验证
        if (empty($walletId)) {
            return $this->createErrorResponse(ShinamiException::INVALID_WALLET_ID);
        }
        if (empty($sessionToken)) {
            return $this->createErrorResponse(ShinamiException::INVALID_WALLET_SECRET);
        }

        $this->params = [$walletId, $sessionToken];
        return $this->post('shinami_wal_createWallet');
    }

    /**
     * Notes: 获取钱包地址
     *
     * @Author: 玄尘
     * @Date: 2025/4/22 15:45
     * @param  string  $walletId
     * @return \App\Packages\Shinami\ShinamiResponse
     */
    public function getWalletAddress(string $walletId): ShinamiResponse
    {
        // 参数验证
        if (empty($walletId)) {
            return $this->createErrorResponse(ShinamiException::INVALID_WALLET_ID);
        }

        $this->params = [$walletId];
        return $this->post('shinami_wal_getWallet');
    }

    /**
     * 执行无Gas交易
     *
     * @param  string  $walletId  钱包唯一ID
     * @param  string  $sessionToken  会话令牌
     * @param  string  $txBytes  交易字节
     * @param  array  $options  交易选项
     * @param  string|null  $endpoint  手动指定的端点URL
     * @return ShinamiResponse 包含交易结果的响应
     */
    public function executeGaslessTransaction(
        string $walletId,
        string $sessionToken,
        string $txBytes,
        array $options = [],
    ): ShinamiResponse {
        // 参数验证
        if (empty($walletId)) {
            return $this->createErrorResponse(ShinamiException::INVALID_WALLET_ID);
        }
        if (empty($sessionToken)) {
            return $this->createErrorResponse(ShinamiException::INVALID_WALLET_SECRET);
        }
        if (empty($txBytes)) {
            return $this->createErrorResponse(ShinamiException::INVALID_PARAMS, ['参数' => '交易字节']);
        }

        // 记录交易字节信息，便于调试
        $this->logInfo('执行无Gas交易', [
            'walletId'       => $walletId,
            'txBytes'        => $txBytes,
            'decodedTxBytes' => json_decode(base64_decode($txBytes), true),
            'options'        => $options
        ]);

        $this->params = [
            $walletId,
            $sessionToken,
            $txBytes,
            null, // gasBudget - 使用自动预算
            $options
        ];

        $response = $this->post('shinami_wal_executeGaslessTransactionBlock');

        // 记录响应信息，便于调试
        if ($response->isError()) {
            $this->logError('执行无Gas交易失败', [
                'walletId'  => $walletId,
                'error'     => $response->getMessage(),
                'errorCode' => $response->getErrorCode(),
                'data'      => $response->data
            ]);
        }

        return $response;
    }

    /**
     * Notes: 获取钱包地址
     *
     * @Author: 玄尘
     * @Date: 2025/4/23 10:39
     * @param $wallet
     * @return mixed|null
     */
    public function getUserWalletAddress($wallet)
    {
        $walletResponse = '';
        if ($wallet instanceof UserWallet) {
            $walletId       = $wallet->wallet_id;
            $walletResponse = $wallet->wallet_address;
        } else {
            $adminWalletId = SystemConfig::getValue('wallet', 'wallet_admin_id');
            $walletId      = $wallet;
            if ($walletId == $adminWalletId) {
                $walletAddress = SystemConfig::getValue('wallet', 'wallet_admin_address');
                if (empty($walletAddress)) {
                    $this->logError('管理员钱包地址未配置');
                    return null;
                }
                return $walletAddress;
            }
        }
        if ($walletResponse) {
            return $walletResponse;
        }

        $walletResponse = $this->getWalletAddress($walletId);
        if ($walletResponse->isError()) {
            $this->logError('获取钱包地址失败', [
                'walletId'  => $walletId,
                'error'     => $walletResponse->getMessage(),
                'errorCode' => $walletResponse->getErrorCode()
            ]);
            return null;
        }

        return $walletResponse->result;
    }

    public function getAdminWalletAddress(): string
    {
        $adminWalletAddress = SystemConfig::getValue('wallet', 'wallet_admin_address');
        if (empty($adminWalletAddress)) {
            $this->logError('管理员钱包地址未配置');
            return '';
        }

        return $adminWalletAddress;
    }
}