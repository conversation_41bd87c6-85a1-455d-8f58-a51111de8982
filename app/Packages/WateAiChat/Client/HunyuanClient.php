<?php

namespace App\Packages\WateAiChat\Client;

use App\Models\AiChatConfig;
use App\Models\ChatGroup;
use App\Models\User;
use App\Packages\WateAiChat\BaseClient;
use App\Packages\WateAiChat\Interfaces\ChatInterface;
use App\Packages\WateAiChat\Traits\OpenAiTrait;

class HunyuanClient extends BaseClient implements ChatInterface
{
    use OpenAiTrait;

    public function __construct(AiChatConfig $config, ChatGroup $group, User $user)
    {
        $this->config = $config;
        $this->engine = $config->engine;
        $this->group  = $group;
        $this->user   = $user;
    }

}