<?php

namespace App\Packages\WateAiChat\Interfaces;

use App\Models\AiChatConfig;
use App\Models\ChatGroup;
use App\Models\User;
use App\Packages\WateAiChat\StreamResponse;

interface ChatInterface
{
    public function __construct(AiChatConfig $config, ChatGroup $group, User $user);

    public function getParams(string $message, string $system = '', array $images = []): array;

    public function checkText(StreamResponse $response);

}