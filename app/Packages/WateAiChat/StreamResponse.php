<?php

namespace App\Packages\WateAiChat;

use App\Models\ChatGroup;
use App\Models\SystemConfig;
use App\Models\User;
use App\Packages\Suno\Exceptions\StreamException;
use OpenAI\Exceptions\ErrorException;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\StreamInterface;

class StreamResponse
{
    protected string $responseMessage   = '';
    protected int    $responseTokens    = 0;
    protected int    $inputToken        = 0;
    protected int    $outputToken       = 0;
    protected string $responseReasoning = '';

    public function __construct(
        public ResponseInterface $response,
        protected array $params,
        protected string $message,
        protected array $imageUrl,
        protected string $audio,
        protected string $video,
    ) {
    }

    public function pushMessage(string $message)
    {
        $this->responseMessage .= $message;
    }

    public function setTokens(int $tokens, int $input, int $output)
    {
        $this->responseTokens = $tokens;
        $this->inputToken     = $input;
        $this->outputToken    = $output;
    }

    public function pushReasoning(string $reasoning)
    {
        $this->responseReasoning .= $reasoning;
    }

    public function getIterator()
    {
        while (! $this->response->getBody()->eof()) {
            $line  = $this->readLine($this->response->getBody());
            $event = null;
            if (str_starts_with($line, 'event:')) {
                $event = trim(substr($line, strlen('event:')));
                $line  = $this->readLine($this->response->getBody());
            }
            if (! str_starts_with($line, 'data:')) {
                continue;
            }
            $data = trim(substr($line, strlen('data:')));

            if ($data === '[DONE]') {
                break;
            }
            $response = json_decode($data, true, flags: JSON_THROW_ON_ERROR);
            if (isset($response['error'])) {
                throw new ErrorException($response['error'], $this->response->getStatusCode());
            }
            if ($event !== null) {
                $response['__event'] = $event;
                //                $response['__meta']  = $this->meta();
            }

            yield $response;
        }
    }

    private function readLine(StreamInterface $stream): string
    {
        $buffer = '';

        while (! $stream->eof()) {
            if ('' === ($byte = $stream->read(1))) {
                return $buffer;
            }
            $buffer .= $byte;
            if ($byte === "\n") {
                break;
            }
        }

        return $buffer;
    }

    public function finish(ChatGroup $group, ?User $user = null, string $channel)
    {
        if ($user instanceof User) {
            $score = SystemConfig::getValue('score', 'ai_ask', 0);
            if ($score > 0) {
                $user->account->exec('ai_ask', $score, null, [
                    'remark' => "DeepChat:[{$this->message}]",
                ]);
            }
        }
        return $group->logs()->create([
            'user_id'     => $user->id ?? 0,
            'channel'     => $channel,
            'message'     => $this->message,
            'images'      => $this->imageUrl,
            'audio'       => $this->audio,
            'video'       => $this->video,
            'response'    => $this->responseMessage,
            'reasoning'   => $this->responseReasoning,
            'input_token' => $this->inputToken,
            'out_token'   => $this->outputToken,
            'tokens'      => $this->responseTokens,
        ]);
    }
}