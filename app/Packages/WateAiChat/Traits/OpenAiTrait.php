<?php

namespace App\Packages\WateAiChat\Traits;

use App\Packages\WateAiChat\StreamResponse;

trait OpenAiTrait
{
    public function getParams(
        string $message,
        string $system = '',
        array $images = [],
        string $audio = '',
        string $video = ''
    ): array {
        return [
            'messages'       => $this->getGroupMessage(
                message: $message,
                images: $images,
                audio: $audio,
                video: $video,
            ),
            'model'          => $this->engine->name,
            'max_tokens'     => (int) $this->engine->maxout,
            'stream'         => true,
            'stream_options' => [
                'include_usage' => true,
            ],
        ];
    }

    public function getGroupMessage(
        string $message,
        array $images = [],
        string $audio = '',
        string $video = '',
    ): array {
        $data = [
            [
                'role'    => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $message,
                    ],
                ],
            ]
        ];

        if (count($images) > 0 && $this->engine->image) {
            foreach ($images as $image) {
                $data[0]['content'][] = [
                    'type'      => 'image_url',
                    'image_url' => [
                        'url' => method_exists(self::class, 'getImgUrl') ? $this->getImgUrl($image) : $image,
                    ],
                ];
            }
        }
        if ($audio && $this->engine->audio) {
            $data[0]['content'][] = [
                'type'         => 'input_audio',
                'input_audio ' => [
                    'data'   => $audio,
                    'format' => 'mp3',
                ],
            ];
        }
        if ($video && $this->engine->video) {
            $data[0]['content'][] = [
                'type'       => 'video_url',
                'video_url ' => [
                    'url' => $video
                ],
            ];
        }

        $logs = $this->group->logs()
            ->orderBy('created_at', 'desc')
            ->select('message', 'response', 'images', 'audio', 'video')
            ->get();

        foreach ($logs as $log) {
            array_unshift($data, [
                'role'    => 'assistant',
                'content' => $log->response,
            ]);

            if (count($log->images ?: []) > 0) {
                array_unshift($data, [
                    'role'    => 'user',
                    'content' => array_merge([
                        [
                            'type' => 'text',
                            'text' => $message,
                        ],
                    ], collect($log->images)->map(function ($image) {
                        return [
                            'type'      => 'image_url',
                            'image_url' => [
                                'url' => method_exists(self::class, 'getImgUrl') ? $this->getImgUrl($image) : $image,
                            ],
                        ];
                    })->toArray()),
                ]);
            } else {
                array_unshift($data, [
                    'role'    => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $log->message,
                        ]
                    ],
                ]);
            }
        }
        return $data;
    }

    public function checkText(StreamResponse $response)
    {
        foreach ($response->getIterator() as $chunk) {
            $reasoning = $chunk['choices'][0]['delta']['reasoning_content'] ?? null;
            if ($reasoning) {
                $response->pushReasoning($reasoning);
                $this->sendReasoning($reasoning);
            }
            $message = $chunk['choices'][0]['delta']['content'] ?? null;
            if ($message) {
                $response->pushMessage($message);
                $this->sendText($message);
            }
            $tokens = $chunk['usage']['total_tokens'] ?? 0;
            if ($tokens > 0) {
                $completion_tokens = $chunk['usage']['completion_tokens'] ?? 0;
                $prompt_tokens     = $chunk['usage']['prompt_tokens'] ?? 0;
                $response->setTokens($tokens, $prompt_tokens, $completion_tokens);
            }
        }
    }

}