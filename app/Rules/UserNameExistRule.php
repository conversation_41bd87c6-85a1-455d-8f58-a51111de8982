<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class UserNameExistRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (DB::table('users')
            ->whereRaw("LOWER(username) = LOWER('{$value}')")
            ->doesntExist()) {
            $fail(lecho('User does not exist or password is incorrect'));
        }
    }
}