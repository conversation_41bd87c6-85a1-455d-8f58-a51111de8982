<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class UserNameUniqueRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (DB::table('users')
            ->whereRaw("LOWER(username) = LOWER('{$value}')")
            ->exists()) {
            $fail(lecho('The user has been registered'));
        }
    }
}