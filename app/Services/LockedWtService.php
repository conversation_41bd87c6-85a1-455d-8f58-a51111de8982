<?php

namespace App\Services;

use App\Models\LockedWt;
use App\Models\LockedWtOperation;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class LockedWtService
{
    /**
     * 创建操作记录
     */
    public function createOperationRecord(
        int $userId,
        int $lockedWtId,
        string $operationType,
        float $amount,
        float $beforeAmount,
        float $afterAmount,
        ?int $targetUserId = null,
        ?string $remark = null,
        ?array $extraData = null
    ): LockedWtOperation {
        return LockedWtOperation::create([
            'user_id'        => $userId,
            'locked_wt_id'   => $lockedWtId,
            'operation_type' => $operationType,
            'amount'         => $amount,
            'target_user_id' => $targetUserId,
            'before_amount'  => $beforeAmount,
            'after_amount'   => $afterAmount,
            'remark'         => $remark,
            'extra_data'     => $extraData,
        ]);
    }

    /**
     * 执行锁仓WT转账（支持部分转出）
     */
    public function transferLockedWt(
        User $fromUser,
        User $toUser,
        int $lockedWtId,
        float $amount,
        string $remark = ''
    ): bool {
        try {
            // 检查接收方是否存在
            if (! $toUser || ! $toUser->exists) {
                throw new \Exception(lecho('The target user does not exist or has not activated their account yet'));
            }

            // 不能转给自己
            if ($fromUser->id == $toUser->id) {
                throw new \Exception(lecho('Cannot transfer money to oneself'));
            }

            // 查找LockedWt记录（只能转账锁仓中的记录）
            $lockedWt = LockedWt::where('id', $lockedWtId)
                ->where('status', LockedWt::STATUS_LOCKED)
                ->first();
            if (! $lockedWt) {
                throw new \Exception(lecho('locked_wt_record_not_exists'));
            }
            if ($lockedWt->user_id != $fromUser->id) {
                throw new \Exception(lecho('no_permission'));
            }

            // 检查转出数量是否有效
            if (! $lockedWt->canTransferAmount($amount)) {
                throw new \Exception(lecho('invalid_or_exceeds_available_amount'));
            }

            $originalRemainingAmount = $lockedWt->remaining_amount;

            DB::transaction(function () use (
                $fromUser,
                $toUser,
                $lockedWt,
                $amount,
                $originalRemainingAmount,
                $remark
            ) {
                // 从发送方coins账户扣除
                $fromUser->account->exec('consume_coins', $amount, null, [
                    'type'         => 'locked_wt_transfer_out',
                    'to_user_id'   => $toUser->id,
                    'locked_wt_id' => $lockedWt->id,
                    'remark'       => $remark ?: '转出锁仓WT记录'
                ]);

                // 给接收方coins账户增加
                $toUser->account->exec('user_recharge_coins', $amount, null, [
                    'type'         => 'locked_wt_transfer_in',
                    'from_user_id' => $fromUser->id,
                    'locked_wt_id' => $lockedWt->id,
                    'remark'       => $remark ?: '接收锁仓WT记录'
                ]);

                // 减少原记录剩余数量
                $newRemainingAmount = $originalRemainingAmount - $amount;

                if ($newRemainingAmount <= 0) {
                    // 如果剩余数量为0，标记为已转出
                    $lockedWt->update([
                        'remaining_amount' => 0,
                        'status'           => LockedWt::STATUS_TRANSFERRED_OUT
                    ]);
                } else {
                    // 否则只更新剩余数量
                    $lockedWt->update(['remaining_amount' => $newRemainingAmount]);
                }

                // 为接收方创建新的LockedWt记录
                $newLockedWt = LockedWt::create([
                    'user_id'               => $toUser->id,
                    'amount'                => $amount, // 原始数量
                    'remaining_amount'      => $amount, // 剩余数量
                    'expired_at'            => $lockedWt->expired_at,
                    'status'                => LockedWt::STATUS_LOCKED,
                    'original_locked_wt_id' => $lockedWt->id,
                    'transfer_from_user_id' => $fromUser->id,
                    'transfer_remark'       => $remark,
                ]);

                // 记录发送方操作历史
                $this->createOperationRecord(
                    $fromUser->id,
                    $lockedWt->id,
                    $amount == $originalRemainingAmount ? LockedWtOperation::TYPE_TRANSFER_OUT : LockedWtOperation::TYPE_PARTIAL_OUT,
                    $amount,
                    $originalRemainingAmount,
                    $newRemainingAmount,
                    $toUser->id,
                    $remark ?: '转出锁仓WT记录'
                );

                // 记录接收方操作历史
                $this->createOperationRecord(
                    $toUser->id,
                    $newLockedWt->id,
                    LockedWtOperation::TYPE_TRANSFER_IN,
                    $amount,
                    0,
                    $amount,
                    $fromUser->id,
                    $remark ?: '接收锁仓WT记录'
                );
            });

            return true;
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
