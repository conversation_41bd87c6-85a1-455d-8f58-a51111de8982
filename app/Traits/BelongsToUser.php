<?php

namespace App\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait BelongsToUser
{
    /**
     * Notes   : 所属用户
     *
     * @Date   : 2022/12/14 13:16
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)
            ->withDefault([
                'username' => '00000000000',
            ])
            ->withTrashed();
    }

    /**
     * Notes   : 插入数据库的时候，可以直接指定 user = User
     *
     * @Date   : 2023/3/30 11:38
     * <AUTHOR> <Jason.C>
     * @param  \App\Models\User  $user
     */
    public function setUserAttribute(User $user): void
    {
        $this->attributes['user_id'] = $user->getKey();
    }

    /**
     * Notes   : 用户作用域
     *
     * @Date   : 2023/3/30 11:38
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  \App\Models\User|int  $user
     */
    public function scopeOfUser(Builder $query, User|int $user): void
    {
        if ($user instanceof User) {
            $query->where('user_id', $user->getKey());
        } else {
            $query->where('user_id', $user);
        }
    }
}