<?php

use App\Facades\Sigma;
use Illuminate\Support\Facades\Cache;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

/**
 * Notes   : 隐藏字符串中间的N位
 *
 * @Date   : 2023/7/19 15:12
 * <AUTHOR> <Jason.C>
 * @param  string  $mobile  手机号
 * @param  int  $len  隐藏位数
 * @param  string  $char  填充符号
 * @return string
 */
function hideMobilePhoneNo(string $mobile, int $len = 4, string $char = '*'): string
{
    if (! $mobile) {
        return '';
    }
    if ($len >= strlen($mobile)) {
        $len = ceil(strlen($mobile) / 2);
    }
    $fill = str_repeat($char, $len);

    $residue = strlen($mobile) - $len;

    return substr($mobile, 0, intval($residue / 2)).$fill.substr($mobile, -ceil($residue / 2));
}

function notificationType(string $type): string
{
    if (method_exists($type, 'getTitle')) {
        return $type::getTitle();
    }
    return $type;
}

/**
 * 数组转换为树型结构，用于无限极分类
 *
 * @param  array  $list
 * @param  int  $parentId
 * @param  string  $parentNodeName
 * @param  string  $primaryKey
 * @param  string  $childNodeName
 * @return array
 */
function array2tree(
    array $list,
    int $parentId = 0,
    string $primaryKey = 'id',
    string $parentNodeName = 'parent_id',
    string $childNodeName = 'children'
): array {
    $resultArr = [];
    if (empty($list)) {
        return [];
    }
    foreach ($list as $key => $item) {
        if ($item[$parentNodeName] == $parentId) {
            unset($list[$key]);
            $item[$childNodeName] = array2tree(
                $list,
                $item[$primaryKey],
                $primaryKey,
                $parentNodeName,
                $childNodeName
            );
            $resultArr[$key]      = $item;
        }
    }
    return $resultArr;
}

/**
 * Notes   : 无限级分类2
 *           这个速度要比递归稳定一些
 *
 * @Date   : 2023/7/5 10:48
 * <AUTHOR> <Jason.C>
 * @param  array  $list
 * @param  int  $parentId
 * @param  string  $primaryKey
 * @param  string  $parentNodeName
 * @param  string  $childNodeName
 * @return array
 */
function list2tree(
    array $list,
    string $primaryKey = 'id',
    string $parentNodeName = 'parent_id',
    string $childNodeName = 'children',
    int $parentId = 0
): array {
    $tree  = [];
    $refer = [];
    foreach ($list as $key => $data) {
        $refer[$data[$primaryKey]] = &$list[$key];
    }
    foreach ($list as $key => $data) {
        $pid = $data[$parentNodeName];
        if ($parentId == $pid) {
            $tree[] = &$list[$key];
        } elseif (isset($refer[$pid])) {
            $parent                   = &$refer[$pid];
            $parent[$childNodeName][] = &$list[$key];
        }
    }
    return $tree;
}

/**
 * Notes   : 创建订单编号
 *
 * @Date   : 2023/7/25 09:30
 * <AUTHOR> <Jason.C>
 * @param  string  $prefix  编号前缀
 * @param  int  $length
 * @return string
 * @throws \Exception
 */
function createOrderNo(string $prefix = '', int $length = 18): string
{
    if ($length <= 13 || $length > 24) {
        throw new RuntimeException('数字长度必须大于13位且小于24位');
    }
    $rand = $length - 13;

    $time = explode(' ', microtime());
    $no   = date('ymdHis').sprintf("%0{$rand}d", $time[0] * (10 ** $rand));

    return $prefix.Sigma::orderNo($no);
}

/**
 * Notes   : 格式化字节大小
 *
 * @Date   : 2023/8/1 17:37
 * <AUTHOR> <Jason.C>
 * @param  int  $size
 * @return string
 */
function formatBytes(int $size): string
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

    for ($i = 0; $size >= 1024 && $i < 5; $i++) {
        $size /= 1024;
    }
    return round($size, 2).$units[$i];
}

/**
 * Notes   : 获取两个经纬度之间的距离
 *
 * @Date   : 2023/12/26 21:53
 * <AUTHOR> <Jason.C>
 * @param  float  $lat1  A点经度
 * @param  float  $lng1  A点纬度
 * @param  float  $lat2  B点经度
 * @param  float  $lng2  B点纬度
 * @param  float  $earthRadius  [6371]地球平均曲率半径；[6378.137]经度系数，1米所对应的经度为0.000011；[6370.996]地球半径系数
 * @return float  两点之间的距离，单位m
 */
function calculateDistance1(float $lat1, float $lng1, float $lat2, float $lng2, float $earthRadius = 6371): float
{
    $radLat1 = deg2rad($lat1);
    $radLat2 = deg2rad($lat2);
    $a       = $radLat1 - $radLat2;
    $b       = deg2rad($lng1) - deg2rad($lng2);
    $s       = 2 * asin(sqrt(pow(sin($a / 2), 2) + cos($radLat1) * cos($radLat2) * pow(sin($b / 2), 2)));
    $s       = $s * $earthRadius * 1000;
    return round($s, 2);
}

function lecho($key, $type = null)
{
    if (! $type) {
        $type = session('language_type', config('languageDefault.key'));
    }
    $string = config('language')[$key][$type] ?? $key;
    return str_replace("_", " ", $string);
}

function getQrCodeToBlade(string $content, int $width = 400, int $height = 400)
{
    $qrcode = QrCode::format('png')
        ->size(400)
        ->generate($content);
    return base64_encode($qrcode);
}

function getWikipedia()
{
    $language_type = session('language_type', config('languageDefault.key'));
    $lang          = 'zh';
    $url           = "https://zh.wikipedia.org/wiki/Wikipedia:%E9%A6%96%E9%A1%B5";
    if ($language_type == 'en') {
        $lang = 'en';
        $url  = 'https://en.wikipedia.org/wiki/Main_Page';
    }
    if ($lang == 'zh') {
        $news = Cache::get('zh_news');
        if (empty($news)) {
            $content = file_get_contents($url);
            $pattern = "/mp-2012-column-itn-block(.*?)<\/ul>/si";
            preg_match($pattern, $content, $matches);
            $pattern = "/<li>(.*?)<\/li>/si";
            preg_match_all($pattern, $matches[0], $matche);
            $news = $matche[0];
            Cache::set('zh_news', $news, 3600);
        }
    }
    if ($lang == 'en') {
        $news = Cache::get('en_news_20250119');
        if (empty($news)) {
            $content = file_get_contents($url);
            $pattern = "/In the news<\/h2>(.*?)<\/ul>/si";
            preg_match($pattern, $content, $matches);
            $pattern = "/<li>(.*?)<\/li>/si";
            preg_match_all($pattern, $matches[0], $matche);
            $news = $matche[0];
            Cache::set('en_news_20250119', $news, 3600);
        }
    }
    return $news;
}

/**
 * Notes: 币转成mist格式
 *
 * @Author: 玄尘
 * @Date: 2025/4/25 08:42
 * @param  float|string  $number  输入数字
 * @return string 返回mist格式的数字,保留8位小数
 */
function num2mist($number)
{
    // 检查输入是否为 null 或空
    if ($number === null || $number === '') {
        throw new InvalidArgumentException('输入参数不能为空');
    }
    // 确保输入是数字
    if (! is_numeric($number)) {
        throw new InvalidArgumentException('输入参数必须是数字');
    }
    // 将输入转换为字符串，并确保它是一个有效的数字格式
    $number = (string) $number;
    // 使用 bcmul 进行高精度乘法
    // 显式地将数字转换为字符串，并使用 number_format 移除科学计数法
    $number = number_format((float) $number, 8, '.', ''); // 8 是你的精度
    return bcmul($number, '1000000000', 0);
}

/**
 * Notes: mist转成币格式
 *
 * @Author: 玄尘
 * @Date: 2025/4/25 08:42
 * @param  float|string  $number  输入mist格式数字
 * @return string 返回普通数字格式,保留8位小数
 */
function mist2num($number)
{
    // 检查输入是否为 null 或空
    if ($number === null || $number === '') {
        throw new InvalidArgumentException('输入参数不能为空');
    }

    // 确保输入是数字
    if (! is_numeric($number)) {
        throw new InvalidArgumentException('输入参数必须是数字');
    }

    // 将输入转换为字符串，并确保它是一个有效的数字格式
    $number = (string) $number;

    // 使用 bcdiv 进行高精度除法，并指定小数位数为 8
    return formatNumber(bcdiv($number, '1000000000', 8));
}

/**
 * 格式化数字，去除末尾的0
 *
 * @param  float|string  $number  要格式化的数字
 * @param  int  $decimals  小数位数，默认为8位
 * @return string  格式化后的数字字符串
 */
function formatNumber($number, $decimals = 8)
{
    if ($number == 0) {
        return '0';
    }

    // 如果传入的是已经格式化的字符串（向后兼容）
    if (is_string($number) && strpos($number, '.') !== false) {
        // 使用原有的逻辑处理
        $numberString = rtrim($number, '0');
        $numberString = rtrim($numberString, '.');
        return $numberString;
    }

    // 新的逻辑：先格式化为指定小数位数，再去除末尾的0
    return rtrim(rtrim(number_format($number, $decimals, '.', ''), '0'), '.');
}
