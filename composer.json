{"name": "laravel/laravel", "type": "project", "description": "模块化，laravel框架，集成dcat-admin，模块配置管理", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "alibabacloud/dm-20151123": "1.2.2", "dcat/laravel-admin": "3.2.0", "genealabs/laravel-model-caching": "^0.13", "guzzlehttp/guzzle": "^7.9", "jenssegers/mongodb": "^4.8", "laravel/framework": "^10.40", "laravel/horizon": "^5.27", "laravel/pennant": "^1.10", "laravel/sanctum": "^3.3", "livewire/livewire": "^3.5", "nwidart/laravel-modules": "^10.0", "openai-php/laravel": "^0.11.0", "sendgrid/sendgrid": "^8.1", "wikimedia/composer-merge-plugin": "^2.1", "ext-bcmath": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.9", "enlightn/enlightn": "^2.6", "fakerphp/faker": "^1.23", "laravel/sail": "^1.31", "mockery/mockery": "^1.6", "nunomaduro/collision": "^7.10", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.3"}, "autoload": {"files": ["app/helpers.php"], "psr-4": {"App\\": "app/", "Modules\\": "modules/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}, "merge-plugin": {"include": ["modules/*/composer.json"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "git", "url": "https://gitee.com/cjango/dcat-admin"}]}