<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 多节点服务器名称配置
    |--------------------------------------------------------------------------
    */
    'server_id'           => env('SERVER_ID', 'CLOUD-0001'),

    /*
    |--------------------------------------------------------------------------
    | api请求速率限制
    |--------------------------------------------------------------------------
    */
    'api'                 => [
        'exclude_ip'    => env('RATE_LIMITER_EXCLUDE_IP', ''),
        'rate_login'    => env('RATE_LIMITER_LOGIN', 120),
        'rate_un_login' => env('RATE_LIMITER_UN_LOGIN', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | 模型默认分页数
    |--------------------------------------------------------------------------
    */
    'model_per_page'      => env('MODEL_PER_PAGE', 10),

    /*
    |--------------------------------------------------------------------------
    | 是否需要完整的API请求头信息
    |--------------------------------------------------------------------------
    */
    'full_api_header'     => env('FULL_API_HEADER', false),

    /*
    |--------------------------------------------------------------------------
    | UserInfo 默认头像
    |--------------------------------------------------------------------------
    */
    'default_avatar'      => env('DEFAULT_AVATAR', ''),

    /*
    |--------------------------------------------------------------------------
    | 检查模块版本更新
    |--------------------------------------------------------------------------
    */
    'check_module_update' => env('CHECK_MODULE_UPDATE', false),
];