<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_wallet_operations', function (Blueprint $table) {
            $table->id();
            $table->string('base_url')->nullable()->comment('主地址');
            $table->string('endpoint')->nullable()->comment('请求地址');
            $table->string('method')->nullable()->comment('请求方式');
            $table->json('input_data')->nullable()->comment('请求数据');
            $table->json('response_data')->nullable()->comment('响应数据');
            $table->boolean('is_success')->default(true)->comment('是否成功');
            $table->string('error_message')->nullable()->comment('错误信息');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_wallet_operations');
    }
};
