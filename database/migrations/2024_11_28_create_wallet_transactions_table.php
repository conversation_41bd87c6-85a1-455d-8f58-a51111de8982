<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_wallet_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('from_user_id')->index();
            $table->unsignedBigInteger('from_wallet_id')->index()->comment('发送方钱包ID');
            $table->unsignedBigInteger('to_user_id')->index();
            $table->unsignedBigInteger('to_wallet_id')->index()->comment('接收方钱包ID');
            $table->string('token_type')->comment('代币类型');
            $table->decimal('amount', 18, 8)->comment('交易金额');
            $table->string('transaction_type')->comment('交易类型：points_to_wt-积分兑换, wt_to_usdc-WT兑换USDC等');
            $table->string('status')->default('pending')
                ->comment('交易状态：pending-处理中, completed-已完成, failed-失败');
            $table->json('transaction_data')->nullable()->comment('交易详细数据');
            $table->string('error_message')->nullable()->comment('错误信息');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->timestamps();
            $table->index('transaction_type');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_wallet_transactions');
    }
};
