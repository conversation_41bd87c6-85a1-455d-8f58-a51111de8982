<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_wallet_transactions', function (Blueprint $table) {
            $table->string('transaction_id')->nullable()->change();
            $table->string('from_wallet_id')->change()->after('from_user_id')->comment('发送方钱包id')->index();
            $table->string('to_wallet_id')->change()->after('to_user_id')->comment('接收方钱包id')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
