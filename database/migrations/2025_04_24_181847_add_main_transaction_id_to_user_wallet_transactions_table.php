<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_wallet_transactions', function (Blueprint $table) {
            $table->unsignedBigInteger('main_transaction_id')->nullable()->after('transaction_id')->comment('主交易ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_wallet_transactions', function (Blueprint $table) {
            $table->dropColumn('main_transaction_id');
        });
    }
};
