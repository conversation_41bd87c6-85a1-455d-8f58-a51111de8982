<?php

use App\Enums\WtStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_wallet_orders', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index()->comment('用户ID');
            $table->string('no')->unique()->comment('订单号');
            $table->decimal('amount', 10, 2)->comment('支付金额');
            $table->decimal('wt_amount', 16, 8)->comment('WT数量');
            $table->decimal('price', 10, 2)->comment('单价');
            $table->enum('status', WtStatus::values())
                ->default(WtStatus::UNPAY->value)
                ->comment('支付状态');
            $table->timestamp('paid_at')->nullable()->comment('支付时间');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_wallet_orders');
    }
};
