<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_wallet_orders', function (Blueprint $table) {
            $table->decimal('usd', 10, 2)->nullable()->comment('美元金额')->after('no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_wallet_orders', function (Blueprint $table) {
            $table->dropColumn('usd');
        });
    }
};
