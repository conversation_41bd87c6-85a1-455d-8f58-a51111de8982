<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_locked_wt', function (Blueprint $table) {
            $table->unsignedBigInteger('original_locked_wt_id')->nullable()->after('status')
                ->comment('原始锁仓记录ID（转账时记录）');
            $table->unsignedBigInteger('transfer_from_user_id')->nullable()->after('original_locked_wt_id')
                ->comment('转账来源用户ID');
            $table->string('transfer_remark')->nullable()->after('transfer_from_user_id')->comment('转账备注');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_locked_wt', function (Blueprint $table) {
            $table->dropForeign(['original_locked_wt_id']);
            $table->dropForeign(['transfer_from_user_id']);
            $table->dropColumn(['original_locked_wt_id', 'transfer_from_user_id', 'transfer_remark']);
        });
    }
};
