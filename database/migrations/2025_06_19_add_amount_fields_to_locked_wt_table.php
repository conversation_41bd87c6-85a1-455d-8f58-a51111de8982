<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_locked_wt', function (Blueprint $table) {
            $table->decimal('remaining_amount', 18, 8)->nullable()->after('amount')->comment('当前剩余数量');
        });

        // 更新现有数据：将 amount 复制到 remaining_amount
        DB::statement('UPDATE user_locked_wt SET remaining_amount = amount WHERE remaining_amount IS NULL');

        // 设置字段为非空
        Schema::table('user_locked_wt', function (Blueprint $table) {
            $table->decimal('remaining_amount', 18, 8)->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_locked_wt', function (Blueprint $table) {
            $table->dropColumn('remaining_amount');
        });
    }
};
