<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_bitoken_conversion_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index()->comment('用户ID');
            $table->string('center_key')->index()->comment('用户中心KEY');
            $table->decimal('bitoken_amount', 18, 8)->comment('BiToken数量');
            $table->decimal('locked_wt_amount', 25, 8)->comment('转换的锁仓WT数量');
            $table->decimal('conversion_rate', 8, 2)->default(20)->comment('转换比例');
            $table->unsignedBigInteger('locked_wt_id')->nullable()->index()->comment('关联的锁仓记录ID');
            $table->enum('status', ['pending', 'completed', 'failed'])->default('pending')->comment('转换状态');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_bitoken_conversion_records');
    }
};
