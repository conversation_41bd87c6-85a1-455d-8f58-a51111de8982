<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_bitoken_conversion_records', function (Blueprint $table) {
            $table->json('center_deduct_result')->nullable()->after('error_message')->comment('用户中心扣除BiToken的结果');
            $table->boolean('center_deducted')->default(false)->after('center_deduct_result')->comment('是否已从用户中心扣除BiToken');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_bitoken_conversion_records', function (Blueprint $table) {
            $table->dropColumn(['center_deduct_result', 'center_deducted']);
        });
    }
};
