# BiToken转锁仓WT功能说明

## 功能概述
将用户的BiToken按1:20比例转换为锁仓WT，支持批量处理和详细记录追踪。

## 核心特性
- ✅ 1:20转换比例（1 BiToken = 20 WT）
- ✅ 完整的转换记录追踪
- ✅ 支持预览模式和测试模式
- ✅ 事务安全，确保数据一致性
- ✅ 详细的日志记录和错误处理
- ✅ 灵活的参数配置

## 数据库表结构

### 1. BiToken转换记录表 (`user_bitoken_conversion_records`)
- `user_id`: 用户ID
- `center_key`: 用户中心KEY
- `bitoken_amount`: BiToken数量
- `locked_wt_amount`: 转换的锁仓WT数量
- `conversion_rate`: 转换比例（默认20）
- `locked_wt_id`: 关联的锁仓记录ID
- `status`: 转换状态（pending/completed/failed）
- `center_deducted`: 是否已从用户中心扣除BiToken
- `center_deduct_result`: 用户中心扣除操作的详细结果
- `error_message`: 错误信息

### 2. 锁仓WT操作记录新增类型
- `TYPE_BITOKEN_CONVERSION`: BiToken转换操作类型

## 命令使用方法

### 基本语法
```bash
php artisan bitoken:convert-to-locked-wt [选项]
```

### 可用选项
- `--dry-run`: 预览模式，显示转换信息但不实际执行
- `--test-mode`: 测试模式，使用模拟数据（用于开发测试）
- `--user-id=123`: 指定特定用户ID进行转换
- `--min-bitoken=1`: 设置最小BiToken数量阈值

### 使用示例

#### 1. 预览所有用户的转换情况
```bash
php artisan bitoken:convert-to-locked-wt --dry-run
```

#### 2. 使用测试数据预览
```bash
php artisan bitoken:convert-to-locked-wt --test-mode --dry-run
```

#### 3. 实际执行转换（所有用户）
```bash
php artisan bitoken:convert-to-locked-wt
```

#### 4. 只转换特定用户
```bash
php artisan bitoken:convert-to-locked-wt --user-id=123
```

#### 5. 设置最小BiToken阈值
```bash
php artisan bitoken:convert-to-locked-wt --min-bitoken=1
```

#### 6. 测试模式实际执行
```bash
php artisan bitoken:convert-to-locked-wt --test-mode --user-id=1
```

## 转换流程

1. **获取BiToken数据**
   - 正常模式：调用AiWikiUserCenter API获取真实数据
   - 测试模式：生成模拟数据用于测试

2. **用户过滤**
   - 只处理有center_key的用户
   - BiToken数量大于设定阈值
   - 可指定特定用户ID

3. **转换执行**（在数据库事务中）
   - 创建转换记录（pending状态）
   - 在coins账户增加锁仓WT数量
   - 创建LockedWt锁仓记录
   - 创建操作记录
   - **从用户中心扣除BiToken**（重要！）
   - 标记转换记录为完成状态并记录扣除结果

4. **错误处理**
   - 任何步骤失败时标记记录为failed状态
   - 记录详细错误信息（包括用户中心扣除失败）
   - 事务回滚确保数据一致性
   - **如果用户中心扣除失败，本地锁仓WT也会被回滚**

## 配置说明

### 锁仓天数配置
```php
$lockDays = SystemConfig::getValue('wallet', 'vip_wt_lock_days', 730);
```
默认锁仓730天，可通过系统配置调整。

### 转换比例
```php
const CONVERSION_RATE = 20; // 1 BiToken = 20 WT
```
当前固定为1:20，如需调整可修改常量。

## 安全特性

1. **数据库事务**：确保所有操作要么全部成功，要么全部回滚
2. **详细日志**：记录所有转换操作和错误信息
3. **状态追踪**：完整的转换状态管理
4. **预览模式**：支持预览功能，避免误操作

## 监控和统计

命令执行后会显示详细的统计信息：
- 总用户数
- 处理用户数
- 成功/失败数量
- 总BiToken和总锁仓WT数量

## 注意事项

1. **用户中心连接**：确保用户中心API可正常访问
2. **测试模式**：开发环境可使用测试模式避免API依赖
3. **备份数据**：重要操作前建议备份数据库
4. **分批执行**：大量用户时建议分批执行

## 故障排除

### 常见问题

1. **用户中心API连接失败**
   - 检查USER_CENTER_URI配置
   - 使用--test-mode进行测试

2. **转换失败**
   - 查看error_message字段
   - 检查用户账户状态
   - 查看应用日志

3. **数据不一致**
   - 检查转换记录状态
   - 验证锁仓记录是否正确创建
   - 查看操作记录

### 日志位置
- 应用日志：`storage/logs/laravel.log`
- 转换日志：搜索关键词"BiToken转换"

## 开发说明

### 相关文件
- 命令文件：`app/Console/Commands/ConvertBiTokenToLockedWt.php`
- 模型文件：`app/Models/BiTokenConversionRecord.php`
- 迁移文件：`database/migrations/2025_06_24_create_bitoken_conversion_records_table.php`

### 扩展建议
- 支持自定义转换比例
- 添加转换限制（如每日限额）
- 支持批量撤销转换
- 添加Web界面管理
