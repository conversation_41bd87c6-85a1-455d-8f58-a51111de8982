<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | following language lines contain default error messages used by
    | validator class Some of these rules have multiple versions such
    | as size rules Feel free to tweak each of these messages here
    |
    */

    'accepted'             => '您必須接受。',
    'accepted_if'          => '當 :other 爲 :value 時，必須接受。',
    'active_url'           => '不是壹個有效的網址。',
    'after'                => '必須要晚于 :date。',
    'after_or_equal'       => '必須要等于 :date 或更晚。',
    'alpha'                => '只能由字母組成。',
    'alpha_dash'           => '只能由字母、數字、短劃線(-)和下劃線(_)組成。',
    'alpha_num'            => '只能由字母和數字組成。',
    'array'                => ':attribute 必須是壹個數組。',
    'before'               => ':attribute 必須要早于 :date。',
    'before_or_equal'      => ':attribute 必須要等于 :date 或更早。',
    'between'              => [
        'array'   => ':attribute 必須只有 :min - :max 個單元。',
        'file'    => ':attribute 必須介于 :min - :max KB 之間。',
        'numeric' => ':attribute 必須介于 :min - :max 之間。',
        'string'  => ':attribute 必須介于 :min - :max 個字符之間。',
    ],
    'boolean'              => ':attribute 必須爲布爾值。',
    'confirmed'            => ':attribute 兩次輸入不壹致。',
    'current_password'     => '密碼錯誤。',
    'date'                 => ':attribute 不是壹個有效的日期。',
    'date_equals'          => ':attribute 必須要等于 :date。',
    'date_format'          => ':attribute 的格式必須爲 :format。',
    'declined'             => ':attribute 必須是拒絕的。',
    'declined_if'          => '當 :other 爲 :value 時字段 :attribute 必須是拒絕的。',
    'different'            => ':attribute 和 :other 必須不同。',
    'digits'               => ':attribute 必須是 :digits 位數字。',
    'digits_between'       => ':attribute 必須是介于 :min 和 :max 位的數字。',
    'dimensions'           => ':attribute 圖片尺寸不正確。',
    'distinct'             => ':attribute 已經存在。',
    'doesnt_end_with'      => ':attribute 不能以以下之壹結尾: :values。',
    'doesnt_start_with'    => ':attribute 不能以下列之壹開頭: :values。',
    'email'                => ':attribute 不是壹個合法的郵箱。',
    'ends_with'            => ':attribute 必須以 :values 爲結尾。',
    'enum'                 => ':attribute 值不正確。',
    'exists'               => ':attribute 不存在。',
    'file'                 => ':attribute 必須是文件。',
    'filled'               => ':attribute 不能爲空。',
    'gt'                   => [
        'array'   => ':attribute 必須多于 :value 個元素。',
        'file'    => ':attribute 必須大于 :value KB',
        'numeric' => ':attribute 必須大于 :value。',
        'string'  => ':attribute 必須多于 :value 個字符。',
    ],
    'gte'                  => [
        'array'   => ':attribute 必須多于或等于 :value 個元素。',
        'file'    => ':attribute 必須大于或等于 :value KB。',
        'numeric' => ':attribute 必須大于或等于 :value。',
        'string'  => ':attribute 必須多于或等于 :value 個字符。',
    ],
    'image'                => ':attribute 必須是圖片。',
    'in'                   => '已選的屬性 :attribute 無效。',
    'in_array'             => ':attribute 必須在 :other 中。',
    'integer'              => ':attribute 必須是整數。',
    'ip'                   => ':attribute 必須是有效的 IP 地址。',
    'ipv4'                 => ':attribute 必須是有效的 IPv4 地址。',
    'ipv6'                 => ':attribute 必須是有效的 IPv6 地址。',
    'json'                 => ':attribute 必須是正確的 JSON 格式。',
    'lowercase'            => ':attribute 必須小寫。',
    'lt'                   => [
        'array'   => ':attribute 必須少于 :value 個元素。',
        'file'    => ':attribute 必須小于 :value KB。',
        'numeric' => ':attribute 必須小于 :value。',
        'string'  => ':attribute 必須少于 :value 個字符。',
    ],
    'lte'                  => [
        'array'   => ':attribute 必須少于或等于 :value 個元素。',
        'file'    => ':attribute 必須小于或等于 :value KB。',
        'numeric' => ':attribute 必須小于或等于 :value。',
        'string'  => ':attribute 必須少于或等于 :value 個字符。',
    ],
    'mac_address'          => ':attribute 必須是壹個有效的 MAC 地址。',
    'max'                  => [
        'array'   => ':attribute 最多只有 :max 個單元。',
        'file'    => ':attribute 不能大于 :max KB。',
        'numeric' => ':attribute 不能大于 :max。',
        'string'  => ':attribute 不能大于 :max 個字符。',
    ],
    'max_digits'           => ':attribute 不能超過 :max 位數。',
    'mimes'                => ':attribute 必須是壹個 :values 類型的文件。',
    'mimetypes'            => ':attribute 必須是壹個 :values 類型的文件。',
    'min'                  => [
        'array'   => ':attribute 至少有 :min 個單元。',
        'file'    => ':attribute 大小不能小于 :min KB。',
        'numeric' => ':attribute 必須大于等于 :min。',
        'string'  => ':attribute 至少爲 :min 個字符。',
    ],
    'min_digits'           => ':attribute 必須至少有 :min 位數。',
    'multiple_of'          => ':attribute 必須是 :value 中的多個值。',
    'not_in'               => '已選的屬性 :attribute 非法。',
    'not_regex'            => ':attribute 的格式錯誤。',
    'numeric'              => ':attribute 必須是壹個數字。',
    'password'             => [
        'letters'       => ':attribute 必須至少包含壹個字母。',
        'mixed'         => ':attribute 必須至少包含壹個大寫字母和壹個小寫字母。',
        'numbers'       => ':attribute 必須至少包含壹個數字。',
        'symbols'       => ':attribute 必須至少包含壹個符號。',
        'uncompromised' => '給定的 :attribute 出現在數據泄漏中。請選擇不同的 :attribute。',
    ],
    'present'              => ':attribute 必須存在。',
    'prohibited'           => ':attribute 字段被禁止。',
    'prohibited_if'        => '當 :other 爲 :value 時，禁止 :attribute 字段。',
    'prohibited_unless'    => ':attribute 字段被禁止，除非 :other 位于 :values 中。',
    'prohibits'            => ':attribute 字段禁止出現 :other。',
    'regex'                => ':attribute 格式不正確。',
    'required'             => ':attribute 不能爲空。',
    'required_array_keys'  => ':attribute 至少包含指定的鍵：:values。',
    'required_if'          => '當 :other 爲 :value 時 :attribute 不能爲空。',
    'required_if_accepted' => '當 :other 存在時，:attribute 不能爲空。',
    'required_unless'      => '當 :other 不爲 :values 時 :attribute 不能爲空。',
    'required_with'        => '當 :values 存在時 :attribute 不能爲空。',
    'required_with_all'    => '當 :values 存在時 :attribute 不能爲空。',
    'required_without'     => '當 :values 都不存在時 :attribute 不能爲空。',
    'required_without_all' => ':attribute 當 :values 都不存在時 :attribute 不能爲空。',
    'same'                 => ':attribute 和 :other 必須相同。',
    'size'                 => [
        'array'   => ':attribute 必須爲 :size 個單元。',
        'file'    => ':attribute 大小必須爲 :size KB。',
        'numeric' => ':attribute 大小必須爲 :size。',
        'string'  => ':attribute 必須是 :size 個字符。',
    ],
    'starts_with'          => ':attribute 必須以 :values 爲開頭。',
    'string'               => ':attribute 必須是壹個字符串。',
    'timezone'             => ':attribute 必須是壹個合法的時區值。',
    'unique'               => ':attribute 已經存在。',
    'uploaded'             => ':attribute 上傳失敗。',
    'uppercase'            => ':attribute 必須大寫。',
    'url'                  => ':attribute 格式不正確。',
    'uuid'                 => ':attribute 必須是有效的 UUID。',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attributerule" to name lines This makes it quick to
    | specify a specific custom language line for a given attribute rule
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email" This simply helps us make our message more expressive
    |
    */

    'attributes' => [],
];
