<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cms_categories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('parent_id')
                ->default(0)
                ->index();
            $table->string('name');
            $table->string('description')
                ->nullable();
            $table->integer('order')
                ->default(0);
            $table->cover();
            $table->boolean('status')
                ->default(0);
            $table->timestamps();
            $table->softDeletes()
                ->index();

            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cms_categories');
    }
};
