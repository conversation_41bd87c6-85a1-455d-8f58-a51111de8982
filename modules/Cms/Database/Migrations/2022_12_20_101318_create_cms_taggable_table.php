<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cms_taggable', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tag_id')
                ->index();
            $table->unsignedBigInteger('content_id')
                ->index();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cms_taggable');
    }
};
