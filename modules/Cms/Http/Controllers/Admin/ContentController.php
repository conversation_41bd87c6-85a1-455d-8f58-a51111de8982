<?php

namespace Modules\Cms\Http\Controllers\Admin;

use App\Admin\Actions\Batches\BatchDisable;
use App\Admin\Actions\Batches\BatchEnable;
use App\Admin\Actions\Restore;
use App\Admin\Actions\Review\ApplyReview;
use App\Admin\Actions\Review\BatchApplyReview;
use App\Admin\Traits\WithUploads;
use App\Enums\ApplyStatus;
use App\Models\LanguageType;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Cms\Models\Category;
use Modules\Cms\Models\Content;
use Modules\Cms\Models\Tag;

class ContentController extends AdminController
{
    use WithUploads;

    protected string $title = '内容管理';

    public function grid(): Grid
    {
        $content = Content::with(['category', 'tags', 'reviewer'])
            ->orderByDesc('sort')
            ->latest();
        return Grid::make($content, function (Grid $grid) {
//            $grid->selector(function (Grid\Tools\Selector $selector) {
//                $selector->select('apply_status', '审核状态', ApplyStatus::STATUS_MAP);
//            });
            $grid->showBatchDelete();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(Content::class));
                }

//                if (in_array($actions->row->apply_status, [ApplyStatus::INIT, ApplyStatus::REVIEW])) {
//                    $actions->append(new ApplyReview(Content::class));
//                }
            });

            $grid->batchActions([
                new BatchEnable(Content::class),
                new BatchDisable(Content::class),
                new BatchApplyReview(Content::class),
            ]);

            $grid->quickSearch(['title'])
                ->placeholder('内容标题');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->onlyTrashed();

                $filter->like('title', '内容标题');
                $filter->like('category.name', '分类名称');
                $filter->equal('category_id', '标签名称')
                    ->select()
                    ->ajax(route('admin.cms.tags.ajax'));
                $filter->like('language', '语言')
                    ->select(LanguageType::pluck('name', 'key')->toArray());
                $filter->equal('reviewer_id', '作者')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
                $filter->between('created_at', '创建时间')
                    ->datetime();

                if (env('APP_NAME') == 'AiWikiClub') {
                    $filter->equal('position', '定位')
                        ->select([
                            Content::POSITION_TOP => Content::POSITION_MAP[Content::POSITION_TOP],
                        ]);
                } else {
                    $filter->equal('position', '定位')
                        ->select(Content::POSITION_MAP);
                }
            });

            $grid->column('id', '#ID#');
            $grid->column('category.name', '分类名称');
            $grid->column('cover', '封面图')
                ->image('', 50);
            $grid->column('title', '标题');
            $grid->column('language', '语言');
            $grid->column('reviewer', '作者')
                ->display(fn($reviewer) => $reviewer ? $reviewer->showName : "");
            $grid->column('tags', '标签')
                ->pluck('name')
                ->label();
            $grid->column('position', '定位')
                ->using(Content::POSITION_MAP)
                ->label();
            $grid->column('sort', '排序')
                ->help('数字越大越靠前')
                ->editable();
            $grid->column('clicks', '浏览量');
            $grid->column('versions_count', '版本');
            $grid->column('status', '状态')
                ->bool();
//            $grid->column('apply_status', '申请状态')
//                ->using(ApplyStatus::STATUS_MAP)
//                ->label(ApplyStatus::LABEL_MAP);
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(Content::with('tags'), function (Form $form) {
            $form->block(7, function (Form\BlockForm $form) {
                $form->title('基础信息');
                $form->showFooter();
                $form->text('title', '标题')
                    ->required();
                $form->list('sub_title', '二级标题');
                $form->textarea('description', '内容简介');
                $form->editor('content', '内容详情')
                    ->required();
            });
            $form->block(5, function (Form\BlockForm $form) {
                $form->title('扩展信息');
                $form->select('category_id', '上级分类')
                    ->options(Category::selectOptions())
                    ->default(0);
                $form->select('language', '语言')
                    ->options(function () {
                        return LanguageType::pluck('name', 'key')->toArray();
                    })
                    ->required();
                $form->select('reviewer_id', '作者')
                    ->options(function ($userId) {
                        if ($userId) {
                            return [$userId => User::find($userId)->showName];
                        } else {
                            return [0 => '无'];
                        }
                    })
                    ->required()
                    ->ajax(route('admin.user.users.ajax'));
                $form->multipleSelect('tags', '标签')
                    ->options(function ($ids) {
                        if ($ids) {
                            return Tag::whereIn('id', $ids)->pluck('name', 'id');
                        } else {
                            return [];
                        }
                    })
                    ->customFormat(function ($v) {
                        return array_column($v, 'id');
                    })
                    ->ajax(route('admin.cms.tags.ajax'));

                if (env('APP_NAME') == 'AiWikiClub') {
                    $form->select('position', '定位')
                        ->options([
                            Content::POSITION_TOP => Content::POSITION_MAP[Content::POSITION_TOP],
                        ])
                        ->default(0);
                } else {
                    $form->select('position', '定位')
                        ->options(Content::POSITION_MAP)
                        ->default(0);
                }

                $form->number('sort', '排序')
                    ->help('数字越大越靠前');

                $form->switch('status')->default(1);

                $form->next(function (Form\BlockForm $form) {
                    $form->title('图片信息');
                    $this->cover($form);
                    $this->pictures($form, 'pictures', '轮播图', false);
                    $this->file($form, 'video', '视频');
                });

                $form->hidden('apply_status', '审核')->default(ApplyStatus::PASS);
            });

            $form->hidden('reviewer_type', '作者类型')->default('');
            $form->saving(function (Form $form) {
                if (! $form->cover) {
                    $pictures    = $form->pictures;
                    $form->cover = explode(',', $pictures)[0];
                }
                if ($form->reviewer_id) {
                    $form->reviewer_type = (new User())->getMorphClass();
                } else {
                    $form->reviewer_type = null;
                }
            });
        });
    }
}
