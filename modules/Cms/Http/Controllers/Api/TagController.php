<?php

namespace Modules\Cms\Http\Controllers\Api;

use App\Http\Controllers\ApiController;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Modules\Cms\Http\Requests\TagRequest;
use Modules\Cms\Http\Resources\TagCollection;
use Modules\Cms\Models\Tag;

class TagController extends ApiController
{
    public function index(TagRequest $request): JsonResponse
    {
        $resource = Tag::select(['id', 'name'])->latest()
            ->when($request->name, function (Builder $builder, $name) {
                $builder->where('name', 'like', "%$name%");
            })
            ->paginate();

        return $this->success(new TagCollection($resource));
    }
}