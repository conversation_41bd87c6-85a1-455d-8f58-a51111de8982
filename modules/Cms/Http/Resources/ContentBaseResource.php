<?php

namespace Modules\Cms\Http\Resources;

use App\Models\Module;
use Illuminate\Http\Resources\Json\JsonResource;

class ContentBaseResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'content_id'        => $this->id,
            'title'             => $this->title,
            'sub_title'         => $this->sub_title,
            'category'          => $this->category ? new CategoryBaseResource($this->category) : [],
            'tags'              => TagResource::collection($this->tags),
            'description'       => $this->description,
            'cover'             => $this->cover_url,
            'pictures'          => $this->picture_urls,
            'video'             => $this->parseImageUrl($this->video),
            'clicks'            => $this->clicks,
            'created_at'        => (string) $this->created_at,
            'interaction_alias' => $this->when(Module::isEnabled('Interaction'), $this->getMorphClass()),
        ];
    }
}
