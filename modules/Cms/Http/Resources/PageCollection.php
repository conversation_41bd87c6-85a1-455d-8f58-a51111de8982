<?php

namespace Modules\Cms\Http\Resources;

use App\Http\Resources\BaseCollection;

class PageCollection extends BaseCollection
{
    public function toArray($request): array
    {
        return [
            'data' => $this->collection->map(function ($page) {
                return [
                    'page_id'     => $page->id,
                    'title'       => $page->title,
                    'sub_title'   => $page->sub_title,
                    'description' => $page->description,
                    'slug'        => $page->slug,
                    'cover'       => $page->cover_url,
                    'pictures'    => $page->pictures_url,
                    'clicks'      => $page->clicks,
                    'created_at'  => (string) $page->created_at,
                ];
            }),
            'page' => $this->page(),
        ];
    }
}
