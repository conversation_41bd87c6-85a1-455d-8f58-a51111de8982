<?php

namespace Modules\Cms\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PageResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'page_id'     => $this->id,
            'title'       => $this->title,
            'sub_title'   => $this->sub_title,
            'description' => $this->description,
            'slug'        => $this->slug,
            'cover'       => $this->cover_url,
            'pictures'    => $this->pictures_url,
            'clicks'      => $this->clicks,
            'content'     => $this->content,
            'attachments' => $this->attachments_url,
            'created_at'  => (string) $this->created_at,
        ];
    }
}
