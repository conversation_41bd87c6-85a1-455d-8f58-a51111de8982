<?php

namespace Modules\Cms\Models;

use App\Models\Model;
use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Overtrue\LaravelVersionable\Versionable;

class Page extends Model
{
    use Cachable,
        Versionable,
        HasCovers,
        HasEasyStatus,
        SoftDeletes;

    protected $table = 'cms_pages';

    protected $casts = [
        'attachments' => 'json',
    ];

    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Notes   : 获取附件的下载地址
     *
     * @Date   : 2021/4/16 12:01 下午
     * <AUTHOR> < Jason.C >
     * @return \Illuminate\Support\Collection
     */
    public function getAttachmentsUrlAttribute(): Collection
    {
        return collect($this->attachments)->map(function ($item) {
            return Storage::url($item);
        });
    }
}
