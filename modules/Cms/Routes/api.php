<?php

use Illuminate\Support\Facades\Route;
use Modules\Cms\Http\Controllers\Api\CategoryController;
use Modules\Cms\Http\Controllers\Api\ContentController;
use Modules\Cms\Http\Controllers\Api\MaterialController;
use Modules\Cms\Http\Controllers\Api\PageController;
use Modules\Cms\Http\Controllers\Api\TagController;

Route::get('contents', [ContentController::class, 'index']);
Route::get('contents/{content}', [ContentController::class, 'show']);
Route::get('categories', [CategoryController::class, 'index']);
Route::get('tags', [TagController::class, 'index']);
Route::get('materials/{material}', [MaterialController::class, 'show']);
Route::get('pages', [PageController::class, 'index']);
Route::get('pages/{page}', [PageController::class, 'show']);
