<?php

namespace Modules\Interaction;

use App\Contracts\ModuleBootstrap;
use Illuminate\Support\Facades\Artisan;

class Bootstrap implements ModuleBootstrap
{
    protected static string $menuKey = 'interaction-module';

    public static function install(): void
    {
        Artisan::call('migrate', [
            '--path' => 'modules/Interaction/Database/Migrations',
        ]);
        self::createAdminMenu();
    }

    public static function uninstall(): void
    {
        Artisan::call('migrate:reset', [
            '--path' => 'modules/Interaction/Database/Migrations',
        ]);
        self::deleteAdminMenu();
    }

    public static function upgrade(): void
    {
    }

    public static function createAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');

        $main = $menuModel::create([
            'parent_id' => 0,
            'order'     => 35,
            'title'     => '互动模块',
            'icon'      => 'fa-chain '.self::$menuKey,
        ]);

        $main->children()->createMany([
            [
                'order' => 1,
                'title' => '评论互动',
                'icon'  => 'fa-dashboard '.self::$menuKey,
                'uri'   => 'interaction/comments',
            ],
        ]);
    }

    public static function deleteAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');
        $menuModel::where('icon', 'like', '%'.self::$menuKey)->delete();
    }
}