<?php

namespace Modules\Interaction\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use App\Models\ZoneGoods;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Interaction\Http\Requests\BatchDeleteRequest;
use Modules\Interaction\Http\Requests\FavoriteRequest;
use Modules\Interaction\Http\Requests\InteractionRequest;
use Modules\Interaction\Http\Resources\FavoriteCollection;
use Modules\Interaction\Models\Favorite;
use Modules\Mall\Models\Category;
use Modules\Mall\Models\Goods;

class FavoriteController extends ApiController
{
    public function index(Request $request): JsonResponse
    {
        $list = Favorite::ofUser(Api::user())
            ->when($request->type, function (Builder $builder, $type) {
                match ($type) {
                    'book' => $builder->whereHasMorph('favoriteable', [Goods::class], function (Builder $query) {
                        $query->whereHas('firstCategory', function ($q) {
                            $q->where('type', Category::TYPE_BOOK);
                        });
                    })
                        ->orWhereHasMorph('favoriteable', [ZoneGoods::class], function (Builder $query) {
                            $query->whereHas('goods.firstCategory', function ($q) {
                                $q->where('type', Category::TYPE_BOOK);
                            });
                        }),
                    'goods' => $builder->whereHasMorph('favoriteable', [Goods::class], function (Builder $query) {
                        $query->whereHas('firstCategory', function ($q) {
                            $q->where('type', Category::TYPE_GOODS);
                        });
                    })
                        ->orWhereHasMorph('favoriteable', [ZoneGoods::class], function (Builder $query) {
                            $query->whereHas('goods.firstCategory', function ($q) {
                                $q->where('type', Category::TYPE_GOODS);
                            });
                        }),
                    'video' => $builder->where('favoriteable_type', 'video'),
                };
            })
            ->latest()
            ->paginate($request->limit ?: 20);

        return $this->success(new FavoriteCollection($list, true, false));
    }

    public function store(FavoriteRequest $request): JsonResponse
    {
        $data     = $request->safe()->merge(['user_id' => Api::id()]);
        $favorite = Favorite::where($data->all())->first();

        if ($favorite) {
            $favorite->delete();
            return $this->success('DELETED');
        } else {
            Favorite::create($data->all());
            return $this->success('CREATED');
        }
    }

    public function show(InteractionRequest $request): JsonResponse
    {
        $result = Favorite::withTarget($request->safe()->only(['target_type', 'target_id']))
            ->ofUser(Api::user())
            ->exists();

        return $this->success([
            'is_favorited' => $result,
        ]);
    }

    public function destroy(BatchDeleteRequest $request): JsonResponse
    {
        $ids = $request->safe()->ids;
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }
        foreach ($ids as $id) {
            $favorite = Favorite::find($id);
            $this->checkPermission($favorite);
            $favorite->delete();
        }
        return $this->success();
    }

}