<?php

namespace Modules\Interaction\Http\Controllers\Api;

use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\Interaction\Enums\CommentStatus;
use Modules\Interaction\Http\Requests\InteractionRequest;
use Modules\Interaction\Http\Resources\CommentCollection;
use Modules\Interaction\Http\Resources\FavoriteCollection;
use Modules\Interaction\Http\Resources\FollowCollection;
use Modules\Interaction\Http\Resources\LikeCollection;
use Modules\Interaction\Http\Resources\SubscribeCollection;
use Modules\Interaction\Models\Comment;
use Modules\Interaction\Models\Favorite;
use Modules\Interaction\Models\Follow;
use Modules\Interaction\Models\Like;
use Modules\Interaction\Models\Subscribe;

class InteractionController extends ApiController
{
    public function comments(InteractionRequest $request): JsonResponse
    {
        $list = Comment::where('status', CommentStatus::NORMAL)
            ->withTarget($request->safe()->only(['target_type', 'target_id']))
            ->latest()
            ->paginate();
        return $this->success(new CommentCollection($list, false));
    }

    public function favorites(InteractionRequest $request): JsonResponse
    {
        $list = Favorite::withTarget($request->safe()->only(['target_type', 'target_id']))
            ->latest()
            ->paginate();
        return $this->success(new FavoriteCollection($list, false));
    }

    public function follows(InteractionRequest $request): JsonResponse
    {
        $list = Follow::withTarget($request->safe()->only(['target_type', 'target_id']))
            ->latest()
            ->paginate();
        return $this->success(new FollowCollection($list, false));
    }

    public function likes(InteractionRequest $request): JsonResponse
    {
        $list = Like::withTarget($request->safe()->only(['target_type', 'target_id']))
            ->latest()
            ->paginate();
        return $this->success(new LikeCollection($list, false));
    }

    public function subscribes(InteractionRequest $request): JsonResponse
    {
        $list = Subscribe::withTarget($request->safe()->only(['target_type', 'target_id']))
            ->latest()
            ->paginate();
        return $this->success(new SubscribeCollection($list, false));
    }
}