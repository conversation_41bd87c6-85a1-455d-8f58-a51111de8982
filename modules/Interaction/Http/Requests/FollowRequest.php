<?php

namespace Modules\Interaction\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Modules\Interaction\Rules\FollowRule;

class FollowRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'followable_type' => 'required|bail|alpha',
            'followable_id'   => [
                'required',
                'bail',
                new FollowRule,
            ],
        ];
    }
}
