<?php

namespace Modules\Interaction\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Modules\Interaction\Rules\TargetRule;

class InteractionRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'target_type' => [
                'required',
                'bail',
                'alpha',
            ],
            'target_id'   => [
                'required',
                'bail',
                new TargetRule,
            ],
        ];
    }
}
