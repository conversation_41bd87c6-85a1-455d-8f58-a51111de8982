<?php

namespace Modules\Interaction\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FollowResource extends JsonResource
{
    public function __construct($resource, protected bool $showTarget = true, protected bool $showUser = true)
    {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        return [
            'follow_id'  => $this->id,
            'target'     => $this->when($this->showTarget, [
                'type'  => $this->followable_type,
                'id'    => $this->followable_id,
                'title' => method_exists($this->followable, 'getTitle') ? $this->followable->getTitle() : '',
            ]),
            'user'       => $this->when($this->showUser, new InteractionUserResource($this->user)),
            'created_at' => (string) $this->created_at,
        ];
    }
}