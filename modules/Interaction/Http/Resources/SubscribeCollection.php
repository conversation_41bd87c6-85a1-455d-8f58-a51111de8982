<?php

namespace Modules\Interaction\Http\Resources;

use App\Http\Resources\BaseCollection;
use Illuminate\Http\Request;

class SubscribeCollection extends BaseCollection
{
    public function __construct($resource, protected bool $showTarget = true, protected bool $showUser = true)
    {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        $collection = $this->collection->groupBy('look_at');
        return [
            'data' => $collection->map(function ($items, $key) {
                return [
                    'key'   => $key,
                    'lists' => collect($items)->map(function ($item) {
                        return new SubscribeResource($item, $this->showTarget, $this->showUser);
                    }),
                ];
            })->values(),
            'page' => $this->page(),
        ];
    }
}