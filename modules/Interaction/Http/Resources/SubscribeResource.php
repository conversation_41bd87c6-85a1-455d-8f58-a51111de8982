<?php

namespace Modules\Interaction\Http\Resources;

use App\Models\ZoneGoods;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscribeResource extends JsonResource
{
    public function __construct($resource, protected bool $showTarget = true, protected bool $showUser = true)
    {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        $zoneId  = 0;
        $goodsId = $this->subscribable_id;
        if ($this->subscribable_type == 'zonegoods') {
            $goodsId = $this->subscribable->goods_id;
            $zoneId  = $this->subscribable->zone_id;
        }
        return [
            'subscribe_id' => $this->id,
            'target'       => $this->when($this->showTarget, [
                'type'    => $this->subscribable_type,
                'id'      => $goodsId,
                'zone_id' => $zoneId,
                'title'   => method_exists($this->subscribable, 'getTitle') ? $this->subscribable->getTitle() : '',
                'cover'   => method_exists($this->subscribable, 'getCover') ? $this->subscribable->getCover() : '',
                'price'   => method_exists($this->subscribable, 'getPrice') ? $this->subscribable->getPrice() : '0.00',
            ]),
            'user'         => $this->when($this->showUser, new InteractionUserResource($this->user)),
            'created_at'   => (string) $this->created_at,
        ];
    }
}