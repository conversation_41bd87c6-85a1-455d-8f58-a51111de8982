<?php

namespace Modules\Interaction\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Follow extends Model
{
    use BelongsToUser;

    protected $table = 'interaction_follows';

    protected array $dates = [
        'accepted_at',
    ];

    public function followable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeWithType(Builder $query, string $type): Builder
    {
        return $query->where('followable_type', app($type)->getMorphClass());
    }
    
    public function scopeWithTarget(Builder $query, array $target): Builder
    {
        return $query->where('followable_type', $target['target_type'])
            ->where('followable_id', $target['target_id']);
    }
}
