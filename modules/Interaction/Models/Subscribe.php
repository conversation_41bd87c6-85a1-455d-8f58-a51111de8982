<?php

namespace Modules\Interaction\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Subscribe extends Model
{
    use BelongsToUser;

    protected $table = 'interaction_subscribes';

    public function subscribable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeWithType(Builder $query, string $type): Builder
    {
        return $query->where('subscribable_type', app($type)->getMorphClass());
    }

    public function scopeWithTarget(Builder $query, array $target): Builder
    {
        return $query->where('subscribable_type', $target['target_type'])
            ->where('subscribable_id', $target['target_id']);
    }
}
