<?php

namespace Modules\Interaction\Providers;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;

class InteractionServiceProvider extends ServiceProvider
{
    protected string $moduleName = 'Interaction';

    protected string $moduleNameLower = 'interaction';

    public function boot(): void
    {
    }

    public function register(): void
    {
        $this->registerConfig();
    }

    protected function registerConfig(): void
    {
        Config::set('horizon.defaults.interaction', [
            'connection'          => 'redis',
            'queue'               => ['interaction'],
            'balance'             => 'auto',
            'autoScalingStrategy' => 'time',
            'maxProcesses'        => 5,
            'tries'               => 1,
        ]);
    }

    public function provides(): array
    {
        return [];
    }
}
