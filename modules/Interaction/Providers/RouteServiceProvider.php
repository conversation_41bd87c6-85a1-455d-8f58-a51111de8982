<?php

namespace Modules\Interaction\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    protected string $moduleNamespace = 'Modules\Interaction\Http\Controllers';

    public function map(): void
    {
        $this->mapAdminRoutes();
        $this->mapApiRoutes();
    }

    protected function mapAdminRoutes(): void
    {
        Route::as(config('admin.route.as').'interaction.')
            ->domain(config('admin.route.domain'))
            ->middleware(config('admin.route.middleware'))
            ->namespace($this->moduleNamespace.'\Admin')
            ->prefix(config('admin.route.prefix').'/interaction')
            ->group(__DIR__.'/../Routes/admin.php');
    }

    protected function mapApiRoutes(): void
    {
        Route::as(config('api.route.as').'interaction.')
            ->domain(config('api.route.domain'))
            ->middleware(config('api.route.middleware'))
            ->namespace($this->moduleNamespace.'\Api')
            ->prefix(config('api.route.prefix').'/interaction')
            ->group(__DIR__.'/../Routes/api.php');
    }
}
