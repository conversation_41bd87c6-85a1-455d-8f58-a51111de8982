<?php

namespace Modules\Interaction\Rules;

use Closure;
use Exception;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Database\Eloquent\Relations\Relation;
use ReflectionClass;

class CommentRule implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $alias = $this->data['commentable_type'];
        $class = Relation::getMorphedModel($alias);

        if (! class_exists($class)) {
            $fail('评论对象模型不存在');
            return;
        }

        if (! $class::where('id', $value)->exists()) {
            $fail('评论对象数据不存在');
            return;
        }

        try {
            $obj         = new ReflectionClass($class);
            $property    = $obj->getProperty('interaction');
            $interaction = $property->getValue(new $class);

            if (! isset($interaction['comment']) || ! $interaction['comment']) {
                $fail('当前对象不允许评论');
                return;
            }

            if (isset($interaction['comment_star'])) {
                if (! $interaction['comment_star'] && isset($this->data['star'])) {
                    $fail('当前评论对象不允许评分');
                    return;
                }
                if ($interaction['comment_star'] && ! isset($this->data['star'])) {
                    $fail('当前评论对象必须评分');
                    return;
                }
            }

            if (isset($interaction['comment_pictures'])) {
                if (! $interaction['comment_pictures'] && isset($this->data['pictures'])) {
                    $fail('当前评论对象不可上传图片');
                    return;
                }
                if ($interaction['comment_pictures'] && ! isset($this->data['pictures'])) {
                    $fail('当前评论对象必须上传图片');
                    return;
                }
            }
        } catch (Exception) {
        }
    }
}