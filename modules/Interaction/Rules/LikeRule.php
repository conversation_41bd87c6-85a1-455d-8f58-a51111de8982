<?php

namespace Modules\Interaction\Rules;

use Closure;
use Exception;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Database\Eloquent\Relations\Relation;
use ReflectionClass;

class LikeRule implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $alias = $this->data['likeable_type'];
        $class = Relation::getMorphedModel($alias);

        if (! class_exists($class)) {
            $fail('点赞对象模型不存在');
            return;
        }

        if (! $class::where('id', $value)->exists()) {
            $fail('点赞对象数据不存在');
            return;
        }

        try {
            $obj         = new ReflectionClass($class);
            $property    = $obj->getProperty('interaction');
            $interaction = $property->getValue(new $class);

            if (! isset($interaction['like']) || ! $interaction['like']) {
                $fail('当前对象不允许点赞');
                return;
            }
        } catch (Exception) {
        }
    }
}