<?php

namespace Modules\Payment\Contracts;

interface WithdrawAdapter
{
    /**
     * Notes   : 支付
     *
     * @Date   : 2023/10/30 11:31
     * <AUTHOR> <Jason.C>
     * @param  string  $no  付款单号
     * @param  float  $amount
     * @param  array  $options  付款参数
     * @return bool
     */
    public function disburse(string $no, float $amount, array $options): bool;

    /**
     * Notes   : 提交申请时需要的参数
     *
     * @Date   : 2023/10/27 16:08
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public static function fields(): array;

    /**
     * Notes   : 付款参数
     *
     * @Date   : 2023/10/27 16:08
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public static function params(): array;
}