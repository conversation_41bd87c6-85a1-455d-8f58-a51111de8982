<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_bank_cards', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->unsignedBigInteger('bank_id')
                ->index();
            $table->string('name');
            $table->string('card_no');
            $table->boolean('can_withdraw')
                ->default(0)
                ->comment('是否支持提现');
            $table->boolean('can_quick')
                ->default(0)
                ->comment('是否支持快捷支付');
            $table->timestamps();
            $table->softDeletes()
                ->index();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_bank_cards');
    }
};
