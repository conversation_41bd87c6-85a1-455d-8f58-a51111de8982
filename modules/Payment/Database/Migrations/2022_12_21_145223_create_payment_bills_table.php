<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\BillType;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_bills', function (Blueprint $table) {
            $table->id();
            $table->enum('type', BillType::values());
            $table->date('date')
                ->index()
                ->comment('账单日期');
            $table->decimal('received', 20)
                ->comment('累计收款');
            $table->decimal('refund', 20)
                ->comment('累计退款');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_bills');
    }
};
