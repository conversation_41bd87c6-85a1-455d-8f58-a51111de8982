<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\WithdrawStatus;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('payment_withdraws', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->no();
            $table->unsignedBigInteger('channel_id')
                ->index();
            $table->unsignedDecimal('amount', 20)
                ->comment('提现金额');
            $table->unsignedDecimal('tax')
                ->default(0)
                ->comment('手续费');
            $table->unsignedDecimal('take')
                ->nullable()
                ->comment('实际到账金额，未到账的时候，给null');
            $table->enum('status', WithdrawStatus::values())
                ->default(WithdrawStatus::INIT);
            $table->json('source')
                ->nullable()
                ->comment('提现来源');
            $table->timestamp('paid_at')
                ->nullable()
                ->comment('付款时间');
            $table->string('certificate')
                ->comment('打款凭证')
                ->nullable();
            $table->string('reason')
                ->comment('拒绝原因')
                ->nullable();
            $table->approver();
            $table->timestamps();
            $table->softDeletes()
                ->index();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_withdraws');
    }
};
