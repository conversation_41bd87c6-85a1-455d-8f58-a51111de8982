<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\Gateway;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_virtuals', function (Blueprint $table) {
            $table->id();
            $table->string('no', 32)
                ->unique();
            $table->morphs('user');
            $table->unsignedBigInteger('payment_id')
                ->index();
            $table->enum('gateway', Gateway::values())
                ->index()
                ->comment('支付渠道');
            $table->enum('channel', Channel::values())
                ->index()
                ->comment('支付通道,属于支付渠道下的支付方式');
            $table->decimal('amount');
            $table->string('remark')
                ->nullable();
            $table->dateTime('paid_at')
                ->nullable();
            $table->timestamps();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_virtuals');
    }
};
