<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\CorporateStatus;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_corporates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('payment_id')
                ->index();
            $table->morphs('orderable');
            $table->string('name')
                ->nullable()
                ->comment('打款人');
            $table->decimal('amount', 20)
                ->nullable()
                ->comment('打款金额');
            $table->string('bank_no')
                ->nullable()
                ->comment('打款账户');
            $table->string('cover')
                ->comment('打款凭证');
            $table->string('remark')
                ->nullable()
                ->comment('备注');
            $table->enum('status', CorporateStatus::values())
                ->default(CorporateStatus::INIT->value)
                ->comment('状态');
            $table->json('source')
                ->nullable();
            $table->dateTime('audit_at')
                ->nullable()
                ->comment('审核时间');
            $table->approver();
            $table->timestamps();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_corporates');
    }
};
