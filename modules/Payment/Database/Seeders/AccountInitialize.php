<?php

namespace Modules\Payment\Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Modules\Payment\Models\Account;

class AccountInitialize extends Seeder
{
    public function run(): void
    {
        foreach (User::all() as $user) {
            if (! Account::where('user_id', $user->id)->exists()) {
                Account::create([
                    'user' => $user,
                ]);
            }
        }
    }
}
