<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Cache;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Drivers\PaymentAdapters\Encrypter\Sm4;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;
use Rtgm\sm\RtSm2;
use Rtgm\sm\RtSm3;

class TzBankAdapter implements PaymentAdapter
{
    protected string $baseUri = 'https://ebanktest.tzcb.com:8111/';

    protected array $config;

    public function __construct(?Payment $payment)
    {
        $this->config = $this->getConfig();
    }

    public function getConfig(): array
    {
        return [
            'app_key'         => config('payment.TZ_BANK_APP_KEY'),
            'app_secret'      => config('payment.TZ_BANK_APP_SECRET'),
            'private_key'     => config('payment.TZ_BANK_PRIVATE_KEY'),
            'public_key'      => config('payment.TZ_BANK_PUBLIC_KEY'),
            'bank_public_key' => config('payment.TZ_BANK_BANK_PUBLIC_KEY'),
        ];
    }

    public function wechat(): array
    {
        return [];
    }

    public function alipay(): array
    {
        return [];
    }

    public function callback(): array
    {
        return [];
    }

    public function success(): ResponseInterface
    {
        return new Response(
            200,
            ['Content-Type' => 'application/json'],
            json_encode(['code' => 'SUCCESS', 'message' => lecho("Success")]),
        );
    }

    public function query(): array
    {
        return [];
    }

    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        return [];
    }

    public function token()
    {
        return Cache::remember('TZB_TOKEN_CACHE', 25 * 60, function () {
            return $this->getToken();
        });
    }

    protected function request(string $requestUrl, array $reqBody)
    {
        $sm2       = new RtSm2();
        $sm3       = new RtSm3();
        $sm4       = new Sm4();
        $token     = $this->token()['token'];
        $randomSec = $this->token()['randomSec'];

        $inputDic['reqHeader'] = [
            'terminalNo' => '5116466147',
            'cutType'    => 'WEB',
            'tranDate'   => date('Ymd'),
            'tranSeq'    => date('YmdHis').rand(1000, 9999),
            'tranTime'   => date('His'),
        ];
        $inputDic['reqBody']   = $reqBody;
        $inputDic['token']     = $token;

        $inputAndToken = json_encode($inputDic, JSON_UNESCAPED_UNICODE);
        $inputAndToken = str_replace("\\", "", $inputAndToken);

        $sm3Sign            = $sm3->digest($inputAndToken);
        $sign               = $sm2->doSign(hex2bin($sm3Sign), $this->config['private_key'],
            hex2bin('****************'));
        $inputDic['x-sign'] = $sign;
        $headerMap          = ['x-appKey' => $this->config['app_key'], 'token' => $token];

        $sm4inputS = $sm4->setKey($randomSec)->encryptData(json_encode($inputDic, JSON_UNESCAPED_UNICODE));
        $resultDic = $this->post($requestUrl, $sm4inputS, $headerMap);

        $outputDic = json_decode($sm4->setKey($randomSec)->decryptData($resultDic), true);

        $sign = $outputDic['x-sign'];
        unset($outputDic['x-sign']);

        $message  = json_encode($outputDic, JSON_UNESCAPED_UNICODE);
        $message3 = $sm3->digest($message);
        $signFlag = $sm2->verifySign(
            hex2bin($message3),
            $sign,
            $this->config['bank_public_key'],
            hex2bin('****************')
        );

        if ($outputDic['retType'] == 'E') {
            throw new Exception($outputDic['retMsg']);
        }

        return $outputDic['body'];
    }

    public function getToken()
    {
        $sm2 = new RtSm2();
        $sm3 = new RtSm3();
        $sm4 = new Sm4();

        $uuid     = md5(uniqid());
        $tranDate = date('Y-m-d H:i:s.v');
        $inputDic = ['equUniSeqNo' => '', 'randomSec' => $uuid, 'tranDate' => $tranDate];
        $sm3sgin  = $sm3->digest(json_encode($inputDic, JSON_UNESCAPED_UNICODE));
        $sign     = $sm2->doSign(hex2bin($sm3sgin), $this->config['private_key'], hex2bin('****************'));

        $inputDic['x-sign'] = $sign;

        $json       = json_encode($inputDic, JSON_UNESCAPED_UNICODE);
        $signedJson = $sm4->setKey($this->config['app_secret'])->encryptData($json);
        $headerMap  = ['x-appKey' => $this->config['app_key']];
        $resultDic  = $this->post('ApiGateWay/auth/getToken', $signedJson, $headerMap);
        $outputDic  = json_decode($sm4->setKey($this->config['app_secret'])->decryptData($resultDic), true);

        $sign = $outputDic['x-sign'];
        unset($outputDic['x-sign']);

        $message  = json_encode($outputDic, JSON_UNESCAPED_UNICODE);
        $message3 = $sm3->digest($message);
        $signFlag = $sm2->verifySign(
            hex2bin($message3),
            $sign,
            $this->config['bank_public_key'],
            hex2bin('****************')
        );

        if ($signFlag) {
            return $outputDic;
        } else {
            throw new Exception('验签错误');
        }
    }

    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws Exception
     */
    protected function post(string $uri, string $body, array $header): string
    {
        $header['Content-Type'] = 'application/json';

        $client = new Client([
            'base_uri'    => $this->baseUri,
            'verify'      => false,
            'http_errors' => false,
            'headers'     => $header,
        ]);

        $result = $client->post($uri, [
            'body' => $body,
        ]);

        if ($result->getStatusCode() == 200) {
            return str_replace("\n", '', $result->getBody()->getContents());
        } elseif ($result->getStatusCode() == 601) {
            $error = base64_decode(urldecode($result->getHeader('errBody')[0]));
            throw new Exception($error);
        } else {
            $error = json_decode(str_replace("\n", '', $result->getBody()->getContents()))['error'];
            throw new Exception($error);
        }
    }
}
