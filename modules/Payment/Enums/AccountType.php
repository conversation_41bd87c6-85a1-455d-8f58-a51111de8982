<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;

enum AccountType: string
{
    use EnumMethods;

    case BALANCE = 'balance';
    case SCORE   = 'score';
    case COINS   = 'coins';
    case CASH    = 'cash';
    case OTHER   = 'other';

    const ACCOUNT_TYPE_MAP = [
        self::BALANCE->value => '余额',
        self::SCORE->value   => '积分',
        self::COINS->value   => 'WT锁仓',
        self::CASH->value    => '现金',
        self::OTHER->value   => '其他',
    ];

    public function toString(): string
    {
        return self::ACCOUNT_TYPE_MAP[$this->value];
    }
}