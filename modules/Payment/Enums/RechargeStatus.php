<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;

enum RechargeStatus: string
{
    use EnumMethods;

    case INIT     = 'init';
    case PAID     = 'paid';
    case COMPLETE = 'complete';

    const STATUS_MAP = [
        self::INIT->value     => '初始化',
        self::PAID->value     => '已支付',
        self::COMPLETE->value => '已到账',
    ];

    const STATUS_LABEL = [
        self::INIT->value     => 'default',
        self::PAID->value     => 'success',
        self::COMPLETE->value => 'primary',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}
