<?php

namespace Modules\Payment\Http\Actions;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Modules\Payment\Console\InitUserAccount;

class InitAccountTool extends AbstractTool
{
    protected string $title = '初始化账户';

    public function handle(): Response
    {
        try {
            Artisan::call(InitUserAccount::class);
            return $this->response()->success('操作成功')->refresh();
        } catch (Exception $exception) {
            return $this->response()->error($exception->getMessage());
        }
    }

    public function confirm(): array
    {
        return ['确定要初始化账户?'];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}