<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Payment\Models\BankCard;

class BankCardController extends AdminController
{
    protected string $title = '银行卡列表';

    public function grid(): Grid
    {
        return Grid::make(BankCard::with(['bank', 'user.info'])->latest(), function (Grid $grid) {
            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('bank.name', '银行名称');
                $filter->like('card_no', '卡号');
                $filter->like('user.username', '持卡用户');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->equal('can_withdraw', '可提现')
                    ->radio([
                        0 => '否',
                        1 => '是'
                    ]);
                $filter->equal('can_quick', '快捷支付')
                    ->radio([
                        0 => '否',
                        1 => '是'
                    ]);
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->quickSearch(['name', 'card_no', 'user.username', 'user.info.nickname'])
                ->placeholder('持卡人/卡号/持卡用户');

            $grid->column('id');
            $grid->column('bank.name', '银行名称');
            $grid->column('持卡用户')
                ->display(fn() => $this->user->showName);
            $grid->column('name', '持卡人');
            $grid->column('card_no', '卡号');
            $grid->column('can_withdraw', '可提现')
                ->bool();
            $grid->column('can_quick', '快捷支付')
                ->bool();
            $grid->column('created_at');
        });
    }
}
