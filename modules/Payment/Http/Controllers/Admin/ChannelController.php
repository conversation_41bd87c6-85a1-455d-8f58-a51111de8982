<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Payment\Drivers\PaymentFactory;
use Modules\Payment\Enums\Gateway;

class ChannelController extends Controller
{
    public function index(Request $request): array
    {
        $q        = $request->q;
        $gateway  = Gateway::tryFrom($q);
        if (is_null($gateway)) {
            return [];
        }
        $channels = PaymentFactory::enabledChannels($gateway);

        $return = [];
        foreach ($channels as $key => $channel) {
            $return[] = [
                'id'   => $key,
                'text' => $channel
            ];
        }
        return $return;
    }
}