<?php

namespace Modules\Payment\Http\Controllers\Admin\Charts;

use Illuminate\Http\Request;
use Modules\Payment\Http\Controllers\Admin\Charts\Traits\PaymentStatisticBuild;

class PaymentAjaxBar extends PaymentStatistic
{
    use PaymentStatisticBuild;

    /**
     * 处理请求
     * 如果你的图表类中包含此方法，则可以通过此方法处理前端通过ajax提交的获取图表数据的请求
     *
     * @param  Request  $request
     * @return void
     */
    public function handle(Request $request): void
    {
        // 获取 parameters 方法设置的自定义参数
        $id = $request->get('id');
        $username = $request->get('username');

        $buildData = $this->paymentDaysCount((int) $request->get('option', 7));
        $data = $buildData['data'];
        $categories = $buildData['categories'];

        $this->withData($data);
        $this->withCategories($categories);
    }

    /**
     * 这里返回需要异步传递到 handler 方法的参数
     *
     * @return array
     */
    public function parameters(): array
    {
        return [
            'id'       => $this->id,
            'username' => $this->username,
        ];
    }

    /**
     * 这里覆写父类的方法，不再查询数据
     */
    protected function buildData(): void
    {
    }
}
