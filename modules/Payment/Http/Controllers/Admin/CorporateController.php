<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Payment\Enums\CorporateStatus;
use Modules\Payment\Http\Actions\CorporateAudit;
use Modules\Payment\Models\Corporate;

class CorporateController extends AdminController
{
    protected string $title = '对公账户打款';

    public function grid(): Grid
    {
        $model = Corporate::with(['payment', 'orderable'])
            ->latest();
        return Grid::make($model, function (Grid $grid) {
            $grid->disableRowSelector();
            $grid->disableCreateButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('payment.no', '支付单编号');
                $filter->like('orderable.no', '订单编号');
                $filter->between('amount', '	打款金额');
                $filter->equal('status', '状态')
                    ->select(CorporateStatus::STATUS_MAP);

                $filter->between('created_at', '创建时间')
                    ->datetime();
                $filter->between('audit_at', '审核时间')
                    ->datetime();
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->disableEdit();
                $actions->disableDelete();
                if ($actions->row->canAudit()) {
                    $actions->append(new CorporateAudit);
                }
            });

            $grid->column('payment.no', '支付单编号')
                ->copyable();
            $grid->column('orderable.no', '订单编号')
                ->copyable();
            $grid->column('payment.amount', '订单金额');
            $grid->column('amount', '打款金额')
                ->display(function () {
                    return $this->amount == $this->payment->amount ?
                        "<span style='color: green'>$this->amount</span>" :
                        "<span style='color: red'>$this->amount</span>";
                });
            $grid->column('打款用户')
                ->display(fn() => $this->payment->user->showName);
            $grid->column('bank_no', '打款账户');
            $grid->column('remark', '备注');
            $grid->column('status', '状态')
                ->using(CorporateStatus::STATUS_MAP)
                ->label(CorporateStatus::STATUS_LABEL);
            $grid->column('审核人')
                ->display(fn() => $this->approver->showName ?? '');
            $grid->column('audit_at', '审核时间');

            $grid->column('created_at');
        });
    }
}
