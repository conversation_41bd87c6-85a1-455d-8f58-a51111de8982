<?php

namespace Modules\Payment\Http\Controllers\Admin;

use App\Admin\Actions\Batches\BatchDisable;
use App\Admin\Actions\Batches\BatchEnable;
use App\Admin\Traits\WithUploads;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Payment\Drivers\WithdrawFactory;
use Modules\Payment\Enums\WithdrawAdapter;
use Modules\Payment\Models\WithdrawChannel;

class WithdrawChannelController extends AdminController
{
    use WithUploads;

    protected string $title = '提现渠道';

    public function grid()
    {
        return Grid::make(WithdrawChannel::latest(), function (Grid $grid) {
            $grid->batchActions([new BatchDisable(WithdrawChannel::class), new BatchEnable(WithdrawChannel::class)]);

            $grid->quickSearch(['name'])
                ->placeholder('渠道名称');
            $grid->column('cover', 'LOGO')
                ->thumb(32);
            $grid->column('name', '渠道名称');
            $grid->column('rate', '提现费率')
                ->append('%');
            $grid->column('min', '最低提现');
            $grid->column('max', '最高额度');
            $grid->column('adapter')
                ->using(WithdrawAdapter::ADAPTER_MAP)
                ->label(WithdrawAdapter::ADAPTER_LABEL);
            $grid->column('status')
                ->bool();
            $grid->column('created_at');
        });
    }

    public function form()
    {
        return Form::make(WithdrawChannel::class, function (Form $form) {
            $form->text('name', '渠道名称')
                ->required();
            $this->cover($form, 'cover', 'LOGO')
                ->width(3);
            $form->rate('rate', '提现费率')
                ->default(0)
                ->width(3);
            $form->currency('min', '最低提现');
            $form->currency('max', '最高额度');
            $form->switch('status');
            $form->select('adapter', '适配器')
                ->required()
                ->options(WithdrawAdapter::ADAPTER_MAP);
            $form->keyValue('params', '适配器参数');

            $form->saving(function (Form $form) {
                $factory = new WithdrawFactory(new WithdrawChannel($form->input()));
                $params  = $form->input('params');
                $data    = array_combine($params['keys'] ?? [], $params['values'] ?? []);

                [$result, $message] = $factory->checkRequestParams($data);

                if (! $result) {
                    return $form->response()->error($message);
                }
            });
        });
    }
}