<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Payment\Enums\WithdrawAdapter;
use Modules\Payment\Enums\WithdrawStatus;
use Modules\Payment\Http\Actions\WithdrawAudit;
use Modules\Payment\Models\Withdraw;

class WithdrawController extends AdminController
{
    protected string $title = '提现申请';

    public function grid(): Grid
    {
        return Grid::make(Withdraw::with(['user.info', 'channel'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableDeleteButton();
            $grid->disableEditButton();
            $grid->disableRowSelector();

            $grid->actions([new WithdrawAudit]);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('no', '提现单号');
                $filter->like('channel.name', '提现渠道');
                $filter->equal('channel.adapter', '付款通道')
                    ->select(WithdrawAdapter::ADAPTER_MAP);
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->between('amount', '提现金额');
                $filter->between('tax', '手续费');
                $filter->between('take', '实际到账');
                $filter->equal('status', '提现状态')
                    ->select(WithdrawStatus::STATUS_MAP);
                $filter->between('paid_at', '付款时间')
                    ->datetime();
                $filter->between('created_at', '申请时间')
                    ->datetime();
            });

            $grid->quickSearch(['no', 'user.username', 'user.info.nickname'])
                ->placeholder('提现单号/用户名/用户昵称');

            $grid->column('no', '提现单号');
            $grid->column('channel.name', '提现渠道');
            $grid->column('channel.adapter', '付款通道')
                ->using(WithdrawAdapter::ADAPTER_MAP)
                ->label(WithdrawAdapter::ADAPTER_LABEL);
            $grid->column('提现用户')
                ->display(fn() => $this->user->showName);
            $grid->column('amount', '提现金额');
            $grid->column('tax', '手续费');
            $grid->column('take', '实际到账');
            $grid->column('status', '提现状态')
                ->display(fn($status) => $status->toString())
                ->label(WithdrawStatus::STATUS_LABEL);
            $grid->column('paid_at', '付款时间');
            $grid->column('凭证/拒绝原因')
                ->display(fn() => $this->certificate.$this->reason);
            $grid->column('created_at', '申请时间');
        });
    }
}
