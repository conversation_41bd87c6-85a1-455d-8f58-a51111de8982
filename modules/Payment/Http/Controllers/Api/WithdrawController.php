<?php

namespace Modules\Payment\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Contracts\Database\Eloquent\Builder as WithBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Payment\Http\Requests\WithdrawRequest;
use Modules\Payment\Http\Resources\WithdrawChannelResource;
use Modules\Payment\Http\Resources\WithdrawCollection;
use Modules\Payment\Http\Resources\WithdrawResource;
use Modules\Payment\Models\Withdraw;
use Modules\Payment\Models\WithdrawChannel;

class WithdrawController extends ApiController
{
    /**
     * Notes   : 提现记录
     *
     * @Date   : 2023/10/27 14:24
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $list = Withdraw::ofUser(Api::user())
            ->select(['no', 'channel_id', 'amount', 'tax', 'take', 'status', 'paid_at', 'created_at'])
            ->with([
                'channel' => function (WithBuilder $query) {
                    $query->select(['id', 'name', 'rate', 'min', 'max', 'cover']);
                },
            ])
            ->latest()
            ->paginate($request->limit);

        return $this->success(new WithdrawCollection($list));
    }

    /**
     * Notes   : 可用的提现通道
     *
     * @Date   : 2023/10/30 16:30
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function channels(): JsonResponse
    {
        $list = WithdrawChannel::ofEnabled()
            ->select(['id', 'name', 'cover', 'rate', 'min', 'max'])
            ->get();

        return $this->success(WithdrawChannelResource::collection($list));
    }

    /**
     * Notes   : 申请提现
     *
     * @Date   : 2023/10/27 14:24
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Http\Requests\WithdrawRequest  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Modules\Payment\Exceptions\RuleException
     */
    public function store(WithdrawRequest $request): JsonResponse
    {
        $withdraw = Withdraw::create([
            'user'       => Api::user(),
            'amount'     => $request->safe()->amount,
            'channel_id' => $request->safe()->channel_id,
            'source'     => $request->safe()->extends,
        ]);

        Api::user()->account->exec(
            'withdraw_balance',
            -$request->safe()->amount,
            null,
            [
                'type' => 'withdraw',
                'no'   => $withdraw->no,
            ]
        );
        return $this->success(new WithdrawResource($withdraw));
    }
}
