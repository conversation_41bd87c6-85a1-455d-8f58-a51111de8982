<?php

namespace Modules\Payment\Http\Renders;

use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Card;
use Modules\Payment\Models\Payment;

class PaymentResultRender extends LazyRenderable
{
    /**
     * @throws \Exception
     */
    public function render(): string
    {
        $payment = Payment::find($this->payload['payment_id']);

        $value = $payment->getAdapter()->query();

        return Card::make('<pre>'.json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE).'</pre>');
    }
}