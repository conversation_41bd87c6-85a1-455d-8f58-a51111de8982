<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class AccountLogRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'type'     => 'bail|nullable|string',
            's_time'   => 'bail|nullable|date',
            'e_time'   => 'bail|nullable|date',
            's_amount' => 'bail|nullable|numeric',
            'e_amount' => 'bail|nullable|numeric',
        ];
    }

    public function messages(): array
    {
        return [
            'type.string'      => '账户类型必须是字符',
            's_time.date'      => '开始时间不是一个有效的日期',
            'e_time.date'      => '结束时间不是一个有效的日期',
            's_amount.numeric' => '最小金额不是一个有效数字',
            'e_amount.numeric' => '最大金额不是一个有效数字',
        ];
    }
}