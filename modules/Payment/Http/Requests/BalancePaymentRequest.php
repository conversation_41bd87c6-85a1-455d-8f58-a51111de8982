<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Modules\Payment\Rules\SecurityRule;

class BalancePaymentRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'password' => [
                'bail',
                'required',
                'size:6',
                new SecurityRule,
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'password.required' => '请输入支付密码',
            'password.size'     => '请输入:size位支付密码',
        ];
    }
}
