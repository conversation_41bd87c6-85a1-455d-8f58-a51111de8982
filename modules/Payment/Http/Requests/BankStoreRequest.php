<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class BankStoreRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'bank_id'      => 'bail|required|numeric|exists:Modules\Payment\Models\Bank,id',
            'name'         => 'bail|required',
            'card_no'      => 'bail|required|numeric|digits_between:16,20|unique:Modules\Payment\Models\BankCard',
            'can_withdraw' => 'bail|sometimes|boolean',
            'can_quick'    => 'bail|sometimes|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'bank_id.required'       => '银行必须选择',
            'bank_id.numeric'        => '银行必须选择',
            'bank_id.exists'         => '银行选择有误',
            'name.required'          => '持卡人姓名必须填写',
            'card_no.required'       => '银行卡号必须填写',
            'card_no.numeric'        => '银行卡号只能是数字',
            'card_no.digits_between' => '银行卡号必须在:min至:max位之间',
            'card_no.unique'         => '银行卡号已存在，不可重复添加',
            'can_withdraw.boolean'   => '是否可提现必须是0或1',
            'can_quick.boolean'      => '是否可快捷支付必须是0或1',
        ];
    }
}