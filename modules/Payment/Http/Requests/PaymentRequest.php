<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Rules\ChannelRule;
use Modules\Payment\Rules\NeedOpenId;
use Modules\Payment\Rules\PaymentableRule;

class PaymentRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'paymentable' => [
                'bail',
                'required',
                'string',
                new PaymentableRule,
            ],
            'gateway'     => [
                'bail',
                'required',
                'enum' => new Enum(Gateway::class),
            ],
            'channel'     => [
                'bail',
                'required',
                new ChannelRule,
            ],
            'open_id'     => [
                'bail',
                Rule::requiredIf(function () {
                    if (request()->input('gateway') == 'wechat'
                        && in_array(request()->input('channel'), ['mp', 'mini'])) {
                        return true;
                    } else {
                        return false;
                    }
                }),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'paymentable.required' => '支付对象不能为空',
            'paymentable.string'   => '支付对象格式不正确',
            'gateway.required'     => '支付网关必须选择',
            'channel.required'     => '支付通道必须选择',
        ];
    }
}