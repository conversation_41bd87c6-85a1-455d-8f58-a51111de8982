<?php

namespace Modules\Payment\Http\Resources;

use App\Http\Resources\BaseCollection;
use Illuminate\Http\Request;

class AccountLogCollection extends BaseCollection
{
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return [
                    'rule'        => new AccountRuleResource($item->rule),
                    'type'        => $item->type,
                    'type_text'   => $item->type->toString(),
                    'amount'      => $item->amount > 0 ? "+{$item->amount}" : $item->amount,
                    'balance'     => $item->balance,
                    'frozen'      => $item->frozen,
                    'source'      => $item->source,
                    'remark'      => $item->source['remark'] ?? $item->rule->name,
                    'unfreeze_at' => (string) $item->unfreeze_at,
                    'expired_at'  => (string) $item->expired_at,
                    'is_expired'  => $item->is_expired,
                    'created_at'  => (string) $item->created_at,
                ];
            }),
            'page' => $this->page(),
        ];
    }
}
