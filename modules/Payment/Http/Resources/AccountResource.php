<?php

namespace Modules\Payment\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AccountResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'balance' => $this->balance,
            'score'   => $this->score,
            'coins'   => $this->coins,
            'cash'    => $this->cash,
            'other'   => $this->other,
        ];
    }
}