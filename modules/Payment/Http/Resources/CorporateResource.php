<?php

namespace Modules\Payment\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CorporateResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'payment_corporate_id' => $this->id,
            'name'                 => $this->name,
            'price'                => $this->price,
            'bank_no'              => $this->bank_no,
            'cover'                => $this->cover_url,
            'remark'               => $this->remark,
            'audit_at'             => (string) $this->audit_at,
            'source'               => $this->source,
            'status'               => [
                'value' => $this->status,
                'text'  => $this->status->toString(),
            ],
        ];
    }
}