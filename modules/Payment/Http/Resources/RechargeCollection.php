<?php

namespace Modules\Payment\Http\Resources;

use App\Http\Resources\BaseCollection;

class RechargeCollection extends BaseCollection
{
    public function toArray($request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return [
                    'no'          => $item->no,
                    'type'        => $item->type,
                    'amount'      => $item->amount,
                    'receipts'    => $item->receipts,
                    'status'      => $item->status,
                    'status_text' => $item->status->toString(),
                    'created_at'  => (string) $item->created_at,
                ];
            }),
            'page' => $this->page(),
        ];
    }
}