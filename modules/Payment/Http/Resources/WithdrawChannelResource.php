<?php

namespace Modules\Payment\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WithdrawChannelResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'channel_id' => $this->id,
            'name'       => $this->name,
            'rate'       => $this->rate,
            'min'        => $this->min,
            'max'        => $this->max,
            'cover'      => $this->cover_url,
        ];
    }
}