<?php

namespace Modules\Payment\Jobs;

use Illuminate\Support\Facades\DB;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Enums\BalancePayStatus;
use Modules\Payment\Exceptions\AlreadyPaid;
use Modules\Payment\Exceptions\InsufficientFunds;
use Modules\Payment\Models\AccountRule;
use Modules\Payment\Models\Balance;

class BalanceJob extends PaymentBaseJob
{
    public function __construct(protected Balance $balance)
    {
    }

    public function handle(): void
    {
        DB::transaction(function () {
            if ($this->balance->status == BalancePayStatus::PAID) {
                throw new AlreadyPaid('订单已支付');
            }
            if ($this->balance->channel->value != AccountType::BALANCE->value) {
                if ($this->balance->account->{$this->balance->channel->value} < $this->balance->amount) {
                    throw new InsufficientFunds($this->balance->channel->toString());
                }
                $rule = AccountRule::where('type', $this->balance->channel)
                    ->where('slug', 'like', 'consume%')
                    ->first();

                if ($rule) {
                    $this->balance->account->exec($rule, -$this->balance->amount, 0, [
                        'remark' => "支付流水号:{$this->balance->payment->no}"
                    ]);
                    // 标记支付
                    $this->balance->paid_at = now();
                    $this->balance->status  = BalancePayStatus::PAID;
                    $this->balance->save();
                    $this->balance->paid();
                }
            }
        });
    }
}
