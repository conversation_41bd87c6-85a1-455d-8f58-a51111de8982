<?php

namespace Modules\Payment\Jobs;

use App\Packages\XinHuaERP\XinHuaERP;
use Modules\Mall\Models\Order;
use Modules\Payment\Models\Balance;

class OrderToErp extends PaymentBaseJob
{

    public function __construct(protected Order $order)
    {
    }

    public function handle(): void
    {
        try {
            $this->order->refresh();
            $resData = XinHuaERP::order()->addOrder($this->order);
            if (isset($resData['订单新单号'])) {
                $this->order->erp_no = $resData['订单新单号'];
                $this->order->save();
            }
            foreach ($this->order->items as $item) {
                $item->refreshStock();
            }
        } catch (\Exception $exception) {
            info('-----OrderPaidListener-----');
            info($exception);
        }
    }
}