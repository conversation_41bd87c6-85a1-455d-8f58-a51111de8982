<?php

namespace Modules\Payment\Jobs;

use App\Models\ReservationOrder;
use App\Packages\XinHuaERP\XinHuaERP;
use Modules\Mall\Models\Order;

class ReservationOrderToErpJob extends PaymentBaseJob
{
    public function __construct(protected ReservationOrder $order)
    {
    }

    public function handle(): void
    {
        try {
            $this->order->refresh();
            $resData = XinHuaERP::order()->addResOrder($this->order);
            if (isset($resData['订单新单号'])) {
                $this->order->erp_no = $resData['订单新单号'];
                $this->order->save();
            }
        } catch (\Exception $exception) {
            info('-----ReservationOrderToErpJob-----');
            info($exception);
        }
    }
}