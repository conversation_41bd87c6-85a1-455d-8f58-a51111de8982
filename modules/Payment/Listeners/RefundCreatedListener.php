<?php

namespace Modules\Payment\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Payment\Events\RefundCreatedEvent;

class RefundCreatedListener implements ShouldQueue
{
    public string $connection = 'redis';

    public int $delay = 0;

    public int $tries = 1;

    public function handle(RefundCreatedEvent $event): void
    {
        info(__CLASS__.' '.__LINE__.' '.$event->refund->id);
        $paymentRefund = $event->refund;

        if ($paymentRefund) {
            $paymentRefund->doRefund();
        }
    }
}