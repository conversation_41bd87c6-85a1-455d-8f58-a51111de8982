<?php

namespace Modules\Payment\Listeners;

use App\Events\UserCreatedEvent;
use App\Models\Module;
use Modules\Payment\Models\Account;

class UserCreatedListener
{
    public function handle(UserCreatedEvent $event): void
    {
        $user = $event->user;

        Account::create([
            'user' => $user,
        ]);

        if (Module::isEnabled('Supply')) {
            $user->security()->create([
                'password' => md5(uniqid().config('app.key')),
            ]);
        }
    }
}