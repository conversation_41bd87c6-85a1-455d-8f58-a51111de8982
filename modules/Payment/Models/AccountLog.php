<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use Dcat\Admin\Admin;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Models\Traits\BelongsToAccount;

class AccountLog extends Model
{
    use BelongsToAccount,
        SoftDeletes;

    protected $table = 'payment_account_logs';

    protected $casts = [
        'type'        => AccountType::class,
        'frozen'      => 'boolean',
        'unfreeze_at' => 'datetime',
        'source'      => 'json',
        'expired_at'  => 'datetime',
        'is_expired'  => 'boolean',
    ];

    public function rule(): BelongsTo
    {
        return $this->belongsTo(AccountRule::class)->withTrashed();
    }

    public function setRuleAttribute(AccountRule $rule): void
    {
        $this->attributes['rule_id'] = $rule->getKey();
    }

    /**
     * Notes   : 释放账户记录
     *
     * @Date   : 2023/5/23 13:57
     * <AUTHOR> <Jason.C>
     * @throws \Exception
     */
    public function unfreeze(): bool
    {
        if ($this->frozen) {
            $this->account->increment($this->type->value, $this->amount);

            $this->source      = array_merge($this->source, [
                'balance_before' => $this->balance,
                'balance_after'  => $this->balance + $this->amount,
                'admin_user'     => Admin::user() ? Admin::user()->getKey() : 'system',
                'unfreeze_until' => $this->unfreeze_at,
            ]);
            $this->balance     += $this->amount;
            $this->frozen      = false;
            $this->unfreeze_at = now();
            return $this->save();
        } else {
            throw new Exception('该记录已解冻', 95015);
        }
    }
}
