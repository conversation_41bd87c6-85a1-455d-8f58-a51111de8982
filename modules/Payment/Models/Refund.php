<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use DateTimeInterface;
use Exception;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\RefundStatus;
use Modules\Payment\Exceptions\AlreadyPaid;
use Modules\Payment\Models\Traits\BelongsToPayment;
use Throwable;

class Refund extends Model
{
    use AutoCreateOrderNo,
        BelongsToUser,
        BelongsToPayment,
        SoftDeletes;

    protected $table = 'payment_refunds';

    protected $casts = [
        'status'    => RefundStatus::class,
        'refund_at' => 'datetime',
    ];

    public function refundable(): MorphTo
    {
        return $this->morphTo();
    }

    public function setRefundableAttribute(Model $model): void
    {
        $this->attributes['refundable_type'] = $model->getMorphClass();
        $this->attributes['refundable_id']   = $model->getKey();
    }

    /**
     * Notes: 执行退款流程
     *
     * @Author: 玄尘
     * @Date: 2023/10/17 10:13
     */
    public function doRefund(): bool
    {
        try {
            if ($this->status != RefundStatus::INIT) {
                throw new AlreadyPaid('订单非可退款状态');
            }
            $payment = $this->payment;
            if ($payment->channel != Channel::BALANCE_BALANCE->value) {
                $res = $payment->getAdapter()->refund($this->no, $this->amount, $this->remark ?: '');
                $this->refunded(now());
                $this->refundable->refunded(true, '退款成功', $res);
                $payment->refunded();
            } else {
                $this->refunded(now());
                $this->refundable->refunded(true, '退款成功', []);
                $payment->refunded();
            }
            return true;
        } catch (Exception $exception) {
            Log::info($exception);
            dd($exception);
            $this->refundable->refunded(false, $exception->getMessage());
            return false;
        }
    }

    /**
     * @throws AlreadyPaid
     * @throws Throwable
     */
    public function refunded(DateTimeInterface $refundAt): bool
    {
        return DB::transaction(function () use ($refundAt) {
            $this->refund_at = $refundAt;
            $this->status    = RefundStatus::COMPLETE;

            return $this->save();
        });
    }
}
