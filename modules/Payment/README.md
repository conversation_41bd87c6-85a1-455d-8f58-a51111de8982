# Laravel9 支付模块

### 功能概览

- [x] 统一支付订单
- [x] 余额支付
- [x] 线下支付
- [ ] 合并付款
- [ ] 拆单支付

## 支付接入流程

### 1. 实现接口

implements Paymentable

### 2. 引用Trait

use HasPayment;

### 3. 获取支付对象

return new PaymentableResource();

### 4. 获取可用支付渠道

GET: payment/gateways

### 5. 发起支付

POST: payment/create

```php
[
    'paymentable' => '#3 返回的参数',
    'gateway' => '#4 GATEWAY',
    'channel' => '#4 GATEWAY_CHANNEL 拼接'
]
```

### 6. 查询支付结果

GET: payment/query/{payment_no}

## 验证安全密码

```php
use Modules\Payment\Rules\SecurityRule;

new SecurityRule()
```

## 需要使用的队列名称

- [x] BALANCE_PAY
- [x] UN_FREEZE

## PAYPAL

todo

> https://srmklive.github.io/laravel-paypal/docs.html