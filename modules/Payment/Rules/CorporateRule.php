<?php

namespace Modules\Payment\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Modules\Payment\Models\Payment;

class CorporateRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $payment = Payment::where('no', $value)->first();
        if (! $payment) {
            $fail('缺少支付单');
            return;
        }

        $order = $payment->paymentable;
        if (! $order) {
            $fail('订单不存在');
            return;
        }

        if (! $order->canPay()) {
            $fail('订单状态不对，不可支付');
        }
    }
}