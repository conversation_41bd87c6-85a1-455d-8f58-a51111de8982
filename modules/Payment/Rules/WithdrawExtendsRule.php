<?php

namespace Modules\Payment\Rules;

use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Modules\Payment\Drivers\WithdrawFactory;
use Modules\Payment\Models\WithdrawChannel;

class WithdrawExtendsRule implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $channel = WithdrawChannel::find($this->data['channel_id']);
        $factory = new WithdrawFactory($channel);
        [$result, $message] = $factory->checkRequestFields($value);

        if (! $result) {
            $fail($message);
        }
    }

    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }
}