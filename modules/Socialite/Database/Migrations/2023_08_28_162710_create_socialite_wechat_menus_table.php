<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Socialite\Enums\WechatMenuType;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('socialite_wechat_menus', function (Blueprint $table) {
            $table->comment('微信公众号菜单');
            $table->id();
            $table->unsignedBigInteger('parent_id')
                ->index();
            $table->string('name', 32);
            $table->enum('type', WechatMenuType::values());
            $table->json('params')
                ->nullable();
            $table->integer('order')
                ->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('socialite_wechat_menus');
    }
};
