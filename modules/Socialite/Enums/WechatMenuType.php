<?php

namespace Modules\Socialite\Enums;

use App\Traits\EnumMethods;

enum WechatMenuType: string
{
    use EnumMethods;

    case CLICK              = 'click';
    case VIEW               = 'view';
    case SCANCODE_PUSH      = 'scancode_push';
    case PIC_SYSPHOTO       = 'pic_sysphoto';
    case PIC_PHOTO_OR_ALBUM = 'pic_photo_or_album';
    case PIC_WEIXIN         = 'pic_weixin';
    case LOCATION_SELECT    = 'location_select';
    case MINI_PROGRAM       = 'miniprogram';

    const TYPE_MAP = [
        self::CLICK->value              => '点击推事件',
        self::VIEW->value               => '跳转URL',
        self::MINI_PROGRAM->value       => '跳转小程序',
        self::SCANCODE_PUSH->value      => '扫码推事件',
        self::PIC_SYSPHOTO->value       => '系统拍照',
        self::PIC_WEIXIN->value         => '微信相册',
        self::PIC_PHOTO_OR_ALBUM->value => '拍照或者相册',
        self::LOCATION_SELECT->value    => '地理位置选择',
    ];

    public function toString(): string
    {
        return self::TYPE_MAP[$this->value];
    }
}