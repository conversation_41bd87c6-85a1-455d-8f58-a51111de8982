<?php

namespace Modules\Socialite\Http\Actions;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Socialite\Enums\WechatMenuType;
use Modules\Socialite\Models\WechatMenu;

class DownloadMenuTool extends AbstractTool
{
    protected string $title = '下载菜单';

    public function handle(): Response
    {
        try {
            DB::table((new WechatMenu())->getTable())->truncate();

            $result = app('wechat.official_account')->getClient()->get('cgi-bin/get_current_selfmenu_info');
            $menus  = $result->toArray();

            if ($menus['is_menu_open']) {
                foreach ($menus['selfmenu_info']['button'] as $button) {
                    $withSubButton = isset($button['sub_button']);

                    $main = WechatMenu::create([
                        'parent_id' => 0,
                        'name'      => $button['name'],
                        'type'      => $withSubButton ? WechatMenuType::CLICK : WechatMenuType::tryFrom($button['type']),
                        'params'    => $withSubButton ? [] : $this->parseMenuParams($button),
                    ]);

                    if ($withSubButton) {
                        foreach ($button['sub_button']['list'] as $subButton) {
                            $main->children()->create([
                                'name'   => $subButton['name'],
                                'type'   => WechatMenuType::tryFrom($subButton['type']),
                                'params' => $this->parseMenuParams($subButton),
                            ]);
                        }
                    }
                }
                return $this->response()->success('下载成功')->refresh();
            } else {
                return $this->response()->error('公众号菜单未开启');
            }
        } catch (Exception $exception) {
            return $this->response()->error($exception->getMessage());
        }
    }

    protected function parseMenuParams(array $button): array
    {
        $type  = WechatMenuType::tryFrom($button['type']);
        $param = [];

        switch ($type) {
            case WechatMenuType::CLICK:
                $param['key'] = $button['key'];
                break;
            case WechatMenuType::VIEW:
                $param['url'] = $button['url'];
                break;
            case WechatMenuType::MINI_PROGRAM:
                $param['url']      = $button['failed_url'];
                $param['appid']    = $button['appid'];
                $param['pagepath'] = $button['pagepath'];
                break;
            case WechatMenuType::SCANCODE_PUSH:
                $param['scancode_push_key'] = $button['key'];
                break;
            case WechatMenuType::PIC_SYSPHOTO:
                $param['pic_sysphoto_key'] = $button['key'];
                break;
            case WechatMenuType::PIC_WEIXIN:
                $param['pic_weixin_key'] = $button['key'];
                break;
            case WechatMenuType::PIC_PHOTO_OR_ALBUM:
                $param['pic_photo_or_album_key'] = $button['key'];
                break;
            case WechatMenuType::LOCATION_SELECT:
                $param['location_key'] = $button['key'];
                break;
        }
        return $param;
    }

    public function confirm(): array
    {
        return [
            '下载菜单',
            '确定要下载当前菜单到本地么?此操作会清空现有数据',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}