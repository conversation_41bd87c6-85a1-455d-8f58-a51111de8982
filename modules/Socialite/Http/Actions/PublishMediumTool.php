<?php

namespace Modules\Socialite\Http\Actions;

use Exception;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Modules\Socialite\Jobs\WechatMediumUploadJob;
use Modules\Socialite\Models\WechatMedium;

class PublishMediumTool extends AbstractTool
{
    protected string $title = '上传素材';

    public function handle(): Response
    {
        try {
            $mediums = WechatMedium::where('status', 0)->get();
            foreach ($mediums as $medium) {
                if (empty($medium->status)) {
                    WechatMediumUploadJob::dispatch($medium);
                }
            }
            return $this->response()->success('上传成功')->refresh();
        } catch (Exception $exception) {
            return $this->response()->error($exception->getMessage());
        }
    }

    public function confirm(): array
    {
        return [
            '上传素材',
            '确定要上传当前列表素材到微信公众号么?',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}