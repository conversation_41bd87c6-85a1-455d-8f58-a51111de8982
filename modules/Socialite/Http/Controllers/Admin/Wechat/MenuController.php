<?php

namespace Modules\Socialite\Http\Controllers\Admin\Wechat;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Socialite\Enums\WechatMenuType;
use Modules\Socialite\Http\Actions\DownloadMenuTool;
use Modules\Socialite\Http\Actions\PublishMenuTool;
use Modules\Socialite\Models\WechatMenu;
use Modules\Socialite\Rules\WechatMenuRule;

class MenuController extends AdminController
{
    protected string $title = '微信公众号菜单';

    public function grid(): Grid
    {
        return Grid::make(WechatMenu::class, function (Grid $grid) {
            $grid->tools([new DownloadMenuTool, new PublishMenuTool]);

            $grid->column('name', '菜单名称')
                ->tree();
            $grid->column('order', '排序')
                ->orderable();
            $grid->column('type')
                ->using(WechatMenuType::TYPE_MAP)
                ->label();
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(WechatMenu::class, function (Form $form) {
            $form->select('parent_id', '上级菜单')
                ->default(0)
                ->options(WechatMenu::selectOptions(function (WechatMenu $menu) {
                    return $menu->where('parent_id', 0);
                }))
                ->required();
            $form->text('name', '菜单名称')
                ->rules(['required', new WechatMenuRule])
                ->help('中文菜单算作1位，英文算1/2位')
                ->required();
            $form->radio('type')
                ->options(WechatMenuType::TYPE_MAP)
                ->required()
                ->when(WechatMenuType::CLICK->value, function (Form $form) {
                    $form->text('params.key', 'KEY');
                })
                ->when(WechatMenuType::VIEW->value, function (Form $form) {
                    $form->url('params.url', 'URL');
                })
                ->when(WechatMenuType::MINI_PROGRAM->value, function (Form $form) {
                    $form->text('params.appid', '小程序APPID');
                    $form->text('params.pagepath', '打开路径');
                    $form->url('params.failed_url', '失败URL');
                })
                ->when(WechatMenuType::SCANCODE_PUSH->value, function (Form $form) {
                    $form->text('params.scancode_push_key', 'KEY');
                })
                ->when(WechatMenuType::PIC_SYSPHOTO->value, function (Form $form) {
                    $form->text('params.pic_sysphoto_key', 'KEY');
                })
                ->when(WechatMenuType::PIC_WEIXIN->value, function (Form $form) {
                    $form->text('params.pic_weixin_key', 'KEY');
                })
                ->when(WechatMenuType::PIC_PHOTO_OR_ALBUM->value, function (Form $form) {
                    $form->text('params.pic_photo_or_album_key', 'KEY');
                })
                ->when(WechatMenuType::LOCATION_SELECT->value, function (Form $form) {
                    $form->text('params.location_key', 'KEY');
                });
        });
    }
}