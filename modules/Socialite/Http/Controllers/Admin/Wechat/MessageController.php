<?php

namespace Modules\Socialite\Http\Controllers\Admin\Wechat;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Modules\Socialite\Models\WechatMessage;

class MessageController extends AdminController
{
    protected string $title = '微信消息';

    public function index(Content $content): Content
    {
        return $content->header($this->title)
            ->description('回调地址：'.route('api.socialite.wechat.callback'))
            ->body($this->grid());
    }

    public function grid(): Grid
    {
        return Grid::make(WechatMessage::latest(), static function (Grid $grid) {
            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->disableBatchActions();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->between('created_at')
                    ->datetime();
            });

            $grid->column('id');
            $grid->column('message.FromUserName');
            $grid->column('message.MsgType');
            $grid->column('created_at');
        });
    }
}