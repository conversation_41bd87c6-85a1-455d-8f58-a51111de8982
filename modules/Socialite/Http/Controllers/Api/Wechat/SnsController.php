<?php

namespace Modules\Socialite\Http\Controllers\Api\Wechat;

use App\Facades\Api;
use Exception;
use Illuminate\Http\JsonResponse;
use Modules\Socialite\Http\Controllers\Api\Controller;
use Modules\Socialite\Http\Requests\CodeRequest;
use Modules\Socialite\Http\Requests\OAuthRequest;
use Modules\Socialite\Models\Wechat;
use Modules\User\Http\Resources\LoginResource;
use Overtrue\Socialite\Exceptions\AuthorizeFailedException;

class SnsController extends Controller
{
    /**
     * Notes   : 获取网页授权地址
     *
     * @Date   : 2023/5/25 15:20
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Socialite\Http\Requests\OAuthRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function oauth(OAuthRequest $request): JsonResponse
    {
        $callback = $request->callback;
        $scope    = $request->scope;
        $state    = $request->state ?? '';

        $oauth = app('wechat.official_account')->getOAuth();

        $redirectUrl = $oauth->scopes([$scope])
            ->withState($state)
            ->redirect($callback);

        return $this->success([
            'redirect_url' => $redirectUrl,
        ]);
    }

    /**
     * Notes   : 用code兑换用户信息
     *
     * @Date   : 2023/5/25 15:18
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Socialite\Http\Requests\CodeRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function code(CodeRequest $request): JsonResponse
    {
        try {
            $oauth = app('wechat.official_account')->getOAuth();

            $user = $oauth->userFromCode($request->code);

            Wechat::updateOrCreate([
                'wx_openid' => $user->getTokenResponse()['openid'],
            ], [
                'user_id'  => Api::id() ?? 0,
                'union_id' => $user['raw']['union_id'] ?? null,
                'nickname' => $user->getNickname(),
                'avatar'   => $user->getAvatar(),
                'gender'   => $user['raw']['sex'] ?? 0,
            ]);

            return $this->success([
                'openid'   => $user->getTokenResponse()['openid'],
                'nickname' => $user->getNickname(),
                'avatar'   => $user->getAvatar(),
            ]);
        } catch (AuthorizeFailedException) {
            return $this->failed('授权失败，CODE可能已经被使用');
        } catch (Exception $exception) {
            return $this->failed($exception->getMessage());
        }
    }

    /**
     * Notes   : 拿CODE登录
     *
     * @Date   : 2023/5/25 16:46
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Socialite\Http\Requests\CodeRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(CodeRequest $request): JsonResponse
    {
        try {
            $oauth = app('wechat.official_account')->getOAuth();
            $user  = $oauth->userFromCode($request->code);

            $account = Wechat::where('wx_openid', $user->getTokenResponse()['openid'])->firstOrFail();

            if (! $account->user_id) {
                return $this->failed('当前微信未绑定用户');
            }

            $token = $this->loginUser($account->user);

            return $this->success(new LoginResource($token, false));
        } catch (Exception $exception) {
            return $this->failed($exception->getMessage());
        }
    }
}
