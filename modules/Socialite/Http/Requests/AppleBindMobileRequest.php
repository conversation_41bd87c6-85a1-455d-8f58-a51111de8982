<?php

namespace Modules\Socialite\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class AppleBindMobileRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'username' => 'required',
            'sub'      => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'username.required' => '请输入手机号',
            'sub.required'      => '参数错误SUB',
        ];
    }
}