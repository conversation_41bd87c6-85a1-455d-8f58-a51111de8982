<?php

namespace Modules\Socialite\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class OAuthRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'callback' => 'required|bail|url',
            'scope'    => 'required|bail|in:snsapi_base,snsapi_userinfo',
            'state'    => 'sometimes|bail|required|alpha_num:ascii',
        ];
    }

    public function messages(): array
    {
        return [
            'callback.required' => '回调地址必须填写',
            'callback.url'      => '回调地址不合规',
            'scope.required'    => '授权作用域必须填写',
            'scope.in'          => '授权作用域不正确',
            'state.required'    => '自定义信息必须填写',
            'state.alpha_num'   => '自定义信息只能是字母和数字',
        ];
    }
}
