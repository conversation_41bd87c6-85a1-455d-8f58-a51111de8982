<?php

namespace Modules\Socialite\Http\Requests\Wechat;

use Illuminate\Foundation\Http\FormRequest;

class WechatMiniRequest extends FormRequest
{

    public function rules(): array
    {
        return [
            'code'          => 'required',
            'iv'            => 'required',
            'encryptedData' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'code.required'          => '缺失CODE',
            'iv.required'            => '缺失向量',
            'encryptedData.required' => '缺失内容',
        ];
    }

}