<?php

namespace Modules\Socialite\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class WechatConfigRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'page_url'      => 'required|bail|url',
            'js_api_list'   => 'required|bail|array',
            'open_tag_list' => 'nullable|bail|array',
        ];
    }

    public function messages(): array
    {
        return [
            'page_url.required' => '页面地址必须填写',
            'page_url.url'      => '页面地址必须是一个网址',
        ];
    }
}
