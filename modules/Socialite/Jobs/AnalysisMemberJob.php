<?php

namespace Modules\Socialite\Jobs;

use App\Models\CustomerMember;

class AnalysisMemberJob extends SocialiteBaseJob
{
    public function __construct(protected CustomerMember $member)
    {
    }

    public function handle(): void
    {
        $client   = app('wechat.work')->getClient();
        $response = $client->postJson('/cgi-bin/kf/customer/batchget', [
            'external_userid_list' => [$this->member->external_userid],
        ]);
        if ($response->isSuccessful()) {
            $data     = $response->toArray();
            $customer = $data['customer_list'][0] ?? '';
            if ($customer) {
                $this->member->update($customer);
            }
        }
    }
}