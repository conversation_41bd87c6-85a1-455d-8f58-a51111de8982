<?php

namespace Modules\Socialite\Jobs;

use App\Facades\AnalysisWechatWorkLog;
use Modules\Socialite\Models\WechatMedium;

class AnalysisRecordKfJob extends SocialiteBaseJob
{
    public function __construct(protected string $open_kfid, protected string $cursor = '')
    {
    }

    public function handle(): void
    {
        $work = new AnalysisWechatWorkLog();
        $work->analysisKfid($this->open_kfid, $this->cursor);
    }
}