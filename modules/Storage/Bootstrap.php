<?php

namespace Modules\Storage;

use App\Contracts\ModuleBootstrap;
use Illuminate\Support\Facades\Artisan;

class Bootstrap implements ModuleBootstrap
{
    protected static string $menuKey = 'storage-module';

    public static function install(): void
    {
        Artisan::call('migrate', [
            '--path' => 'modules/Storage/Database/Migrations',
        ]);
        self::createAdminMenu();
    }

    public static function uninstall(): void
    {
        self::deleteAdminMenu();
    }

    public static function upgrade(): void
    {
    }

    public static function createAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');

        $main = $menuModel::create([
            'parent_id' => 0,
            'order'     => 92,
            'title'     => '文件存储模块',
            'icon'      => 'fa-database '.self::$menuKey,
        ]);

        $main->children()->createMany([
            [
                'order' => 1,
                'title' => '数据看板',
                'icon'  => 'fa-dashboard '.self::$menuKey,
                'uri'   => 'storage',
            ],
            [
                'order' => 2,
                'title' => '存储列表',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'storage/uploads',
            ],
        ]);
    }

    public static function deleteAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');
        $menuModel::where('icon', 'like', '%'.self::$menuKey)->delete();
    }
}