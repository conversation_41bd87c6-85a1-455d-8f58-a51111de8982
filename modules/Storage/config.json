[{"name": "基础配置", "type": "tabs", "key": "storage", "list": [{"name": "存储驱动", "type": "select", "key": "FILESYSTEM_DISK", "value": "public", "source": {"public": "本地存储", "oss": "阿里云", "cos": "腾讯云", "qiniu": "七牛云", "s3compatible": "S3兼容"}}, {"name": "上传文件大小", "help": "前端上传文件最大容量（kb）", "type": "number", "key": "FRONT_MAX_FILE_SIZE", "value": 1024}, {"name": "自动保存缩略图", "type": "radio", "key": "AUTO_MAKE_THUMB", "value": "1", "source": {"0": "否", "1": "是"}}]}, {"name": "阿里云", "type": "tabs", "key": "oss", "list": [{"name": "ACCESS_KEY", "type": "string", "key": "OSS_ACCESS_KEY", "value": ""}, {"name": "SECRET_KEY", "type": "string", "key": "OSS_SECRET_KEY", "value": ""}, {"name": "ENDPOINT", "type": "string", "key": "OSS_ENDPOINT", "value": ""}, {"name": "BUCKET", "type": "string", "key": "OSS_BUCKET", "value": ""}, {"name": "开启CDN", "type": "radio", "key": "OSS_IS_CNAME", "value": "0", "help": "是否是开启CDN域名", "source": {"0": "否", "1": "是"}}, {"name": "CDN_HOST", "type": "string", "key": "OSS_CDN_HOST", "help": "CDN域名，不需要加http或https", "value": ""}, {"name": "启用HTTPS", "type": "radio", "key": "OSS_USE_SSL", "value": "0", "help": "上传和获取地址时，是否启用https协议", "source": {"0": "否", "1": "是"}}, {"name": "加密传输", "type": "radio", "key": "OSS_SIGNED_URL", "value": "0", "help": "如果开启私有访问的时候，要开启此项", "source": {"0": "否", "1": "是"}}]}, {"name": "腾讯云", "type": "tabs", "key": "cos", "list": [{"name": "APP_ID", "type": "string", "key": "COS_APP_ID", "value": ""}, {"name": "SECRET_ID", "type": "string", "key": "COS_SECRET_ID", "value": ""}, {"name": "SECRET_KEY", "type": "string", "key": "COS_SECRET_KEY", "value": ""}, {"name": "REGION", "type": "string", "key": "COS_REGION", "value": ""}, {"name": "BUCKET", "type": "string", "key": "COS_BUCKET", "value": ""}, {"name": "开启CDN", "type": "radio", "key": "COS_IS_CNAME", "value": "0", "help": "是否是开启CDN域名", "source": {"0": "否", "1": "是"}}, {"name": "CDN_HOST", "type": "string", "key": "COS_CDN_HOST", "help": "CDN域名，不需要加http或https", "value": ""}, {"name": "启用HTTPS", "type": "radio", "key": "COS_USE_SSL", "value": "0", "help": "上传和获取地址时，是否启用https协议", "source": {"0": "否", "1": "是"}}, {"name": "加密传输", "type": "radio", "key": "COS_SIGNED_URL", "value": "0", "help": "如果开启私有访问的时候，要开启此项", "source": {"0": "否", "1": "是"}}]}, {"name": "七牛云", "type": "tabs", "key": "qiniu", "list": [{"name": "ACCESS_KEY", "type": "string", "key": "QINIU_ACCESS_KEY", "value": ""}, {"name": "SECRET_KEY", "type": "string", "key": "QINIU_SECRET_KEY", "value": ""}, {"name": "REGION", "type": "string", "key": "QINIU_REGION", "value": ""}, {"name": "ENDPOINT", "type": "string", "key": "QINIU_ENDPOINT", "value": ""}, {"name": "BUCKET", "type": "string", "key": "QINIU_BUCKET", "value": ""}, {"name": "开启CDN", "type": "radio", "key": "QINIU_IS_CNAME", "value": "0", "help": "是否是开启CDN域名", "source": {"0": "否", "1": "是"}}, {"name": "CDN_HOST", "type": "string", "key": "QINIU_CDN_HOST", "help": "CDN域名，不需要加http或https", "value": ""}, {"name": "启用HTTPS", "type": "radio", "key": "QINIU_USE_SSL", "value": "0", "help": "上传和获取地址时，是否启用https协议", "source": {"0": "否", "1": "是"}}, {"name": "加密传输", "type": "radio", "key": "QINIU_SIGNED_URL", "value": "0", "help": "如果开启私有访问的时候，要开启此项", "source": {"0": "否", "1": "是"}}]}, {"name": "S3兼容", "type": "tabs", "key": "s3compatible", "list": [{"name": "ACCESS_KEY", "type": "string", "key": "S3_ACCESS_KEY_ID", "value": ""}, {"name": "SECRET_KEY", "type": "string", "key": "S3_SECRET_ACCESS_KEY", "value": ""}, {"name": "REGION", "type": "string", "key": "S3_DEFAULT_REGION", "value": ""}, {"name": "BUCKET", "type": "string", "key": "S3_BUCKET", "value": ""}, {"name": "ENDPOINT", "type": "string", "key": "S3_ENDPOINT", "value": ""}, {"name": "CDN_HOST", "type": "string", "help": "如果CDN域名与ENDPOINT一致时可不填。默认规则为ENDPOINT/BUCKET", "key": "S3_URL", "value": ""}]}]