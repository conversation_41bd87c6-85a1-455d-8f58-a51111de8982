<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_login_devices', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->string('name')
                ->nullable()
                ->comment('设备名称');
            $table->string('device_id')
                ->index()
                ->nullable()
                ->comment('设备ID');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_login_devices');
    }
};
