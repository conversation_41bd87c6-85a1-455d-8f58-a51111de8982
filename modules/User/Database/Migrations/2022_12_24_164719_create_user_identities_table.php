<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_identities', function (Blueprint $table) {
            $table->id();
            $table->string('name')
                ->comment('身份名称');
            $table->string('description')
                ->nullable()
                ->comment('简介');
            $table->cover();
            $table->string('card')
                ->nullable()
                ->comment('身份卡片');
            $table->decimal('price')
                ->default(0)
                ->comment('订阅价格');
            $table->integer('order')
                ->default(0)
                ->comment('展示排序');
            $table->boolean('status')
                ->default(0);
            $table->boolean('is_default')
                ->default(0)
                ->comment('是否默认身份');
            $table->boolean('is_unique')
                ->default(0)
                ->comment('是否是唯一身份，订阅后不允许订阅其他身份');
            $table->boolean('can_subscribe')
                ->default(0)
                ->comment('是否可订阅');
            $table->unsignedInteger('days')
                ->default(0)
                ->comment('有效期（天）');
            $table->boolean('serial_open')
                ->default(0)
                ->comment('是否开启身份编号');
            $table->unsignedTinyInteger('serial_places')
                ->default(0)
                ->comment('身份编号位数');
            $table->unsignedInteger('serial_reserve')
                ->default(0)
                ->comment('预留编号数量');
            $table->string('serial_prefix', 4)
                ->nullable()
                ->comment('身份编号前缀');
            $table->json('conditions')
                ->nullable()
                ->comment('升级条件');
            $table->json('rules')
                ->nullable()
                ->comment('身份权益');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_identities');
    }
};
