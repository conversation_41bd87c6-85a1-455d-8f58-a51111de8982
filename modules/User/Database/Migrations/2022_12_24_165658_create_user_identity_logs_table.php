<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\User\Enums\IdentityChannel;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_identity_logs', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->unsignedBigInteger('before')
                ->comment('变化前身份');
            $table->unsignedBigInteger('after')
                ->comment('变化后身份');
            $table->enum('channel', IdentityChannel::values())
                ->default(IdentityChannel::SYSTEM->value)
                ->comment('变化渠道');
            $table->json('source')
                ->nullable()
                ->comment('附加溯源信息');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_identity_logs');
    }
};
