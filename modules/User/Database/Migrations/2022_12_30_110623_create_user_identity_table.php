<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_identity', function (Blueprint $table) {
            $table->comment('用户身份中间表');
            $table->id();
            $table->unsignedBigInteger('user_id')
                ->index();
            $table->unsignedBigInteger('identity_id')
                ->index();
            $table->dateTime('started_at')
                ->nullable()
                ->comment('身份的开始时间');
            $table->dateTime('ended_at')
                ->nullable()
                ->comment('角色的结束时间');
            $table->unsignedInteger('serial')
                ->nullable()
                ->comment('身份生成的编号');
            $table->timestamps();

            $table->unique(['user_id', 'identity_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_identity');
    }
};
