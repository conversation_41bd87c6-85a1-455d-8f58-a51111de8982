<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\User\Enums\CertificationStatus;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_certifications', function (Blueprint $table) {
            $table->comment('用户实名认证');
            $table->id();
            $table->user();
            $table->string('name');
            $table->string('id_card_no', 32);
            $table->string('front')
                ->nullable()
                ->comment('身份证头像面');
            $table->string('back')
                ->nullable()
                ->comment('身份证国徽面');
            $table->enum('status', CertificationStatus::values())
                ->default(CertificationStatus::INIT->value)
                ->comment('认证状态');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_certifications');
    }
};
