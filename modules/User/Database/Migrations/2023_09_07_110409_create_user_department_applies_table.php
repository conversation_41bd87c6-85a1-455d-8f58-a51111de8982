<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_department_applies', function (Blueprint $table) {
            $table->comment('用户申请创建部门');
            $table->id();
            $table->user()
                ->comment('申请用户');
            $table->string('name')
                ->comment('申请名称');
            $table->string('type')
                ->nullable()
                ->comment('申请类型');
            $table->easyApply();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_department_applies');
    }
};
