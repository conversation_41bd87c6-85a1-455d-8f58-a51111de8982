<?php

use App\Enums\ApplyStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_department_joins', function (Blueprint $table) {
            $table->comment('用户申请加入部门');
            $table->id();
            $table->user();
            $table->unsignedBigInteger('department_id')
                ->index();
            $table->approver();
            $table->enum('status', ApplyStatus::values())
                ->default(ApplyStatus::INIT->value);
            $table->string('description')
                ->comment('申请原因')
                ->nullable();
            $table->string('reason')
                ->comment('拒绝原因')
                ->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_department_joins');
    }
};
