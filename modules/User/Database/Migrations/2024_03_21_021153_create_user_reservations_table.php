<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_reservations', function (Blueprint $table) {
            $table->unsignedInteger('user_id')->primary();
            $table->string('name')->comment('名称');
            $table->string('contacts')->comment('联系人');
            $table->string('mobile')->comment('联系电话');
            $table->string('address')->comment('地址');
            $table->boolean('status')->default(1)->comment('状态');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_reservations');
    }
};
