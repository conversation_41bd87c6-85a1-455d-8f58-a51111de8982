<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_sign_rules', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('day')->default(1)->comment('连续天数');
            $table->boolean('only_one')->default(0)->comment('仅一次');
            $table->unsignedInteger('coupon_id')->nullable()->comment('目标ID');
            $table->unsignedInteger('number')->default(0)->comment('优惠券ID');
            $table->unsignedDecimal('score')->default(0)->comment('积分');
            $table->boolean('status')->default(1);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_sign_rules');
    }
};
