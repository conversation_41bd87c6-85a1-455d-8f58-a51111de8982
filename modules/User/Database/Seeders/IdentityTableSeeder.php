<?php

namespace Modules\User\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\User\Models\Identity;

class IdentityTableSeeder extends Seeder
{
    public function run(): void
    {
        $seeders = [
            [
                'name'        => '默认身份',
                'description' => '新用户注册默认的身份',
                'order'       => 0,
                'status'      => 1,
                'is_default'  => 1,
                'days'        => 0,
            ]
        ];

        foreach ($seeders as $seeder) {
            Identity::create($seeder);
        }
    }
}
