<?php

namespace Modules\User\Enums;

use App\Traits\EnumMethods;

enum LoveTag: string
{
    use EnumMethods;

    case SON        = 'son';
    case DAUGHTER   = 'daughter';
    case FATHER     = 'father';
    case MOTHER     = 'mother';
    case LOVER      = 'lover';
    case SPOUSE     = 'spouse';
    case BESTFRIEND = 'best_friend';
    case BROTHER    = 'brother';
    case OTHER      = 'other';

    const LOVER_MAP = [
        self::SON->value        => '儿子',
        self::DAUGHTER->value   => '女儿',
        self::FATHER->value     => '父亲',
        self::MOTHER->value     => '母亲',
        self::LOVER->value      => '恋人',
        self::SPOUSE->value     => '配偶',
        self::BESTFRIEND->value => '闺蜜',
        self::BROTHER->value    => '兄弟',
        self::OTHER->value      => '其他',
    ];

    public function toString(): string
    {
        return self::LOVER_MAP[$this->value];
    }
}