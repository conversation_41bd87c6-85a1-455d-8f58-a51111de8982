<?php

namespace Modules\User\Extensions\Sms;

use Modules\User\Extensions\SmsMessage;
use Overtrue\EasySms\Contracts\GatewayInterface;

class DefaultMessage  extends SmsMessage
{
    public function getContent(GatewayInterface $gateway = null): string
    {
        return sprintf('验证码%s，您正在进行身份验证，打死不要告诉别人哦！', $this->code);
    }

    public function getTemplate(GatewayInterface $gateway = null): string
    {
        return config('user.sms.channel_map.DEFAULT');
    }

    public function getData(GatewayInterface $gateway = null): array
    {
        return [
            'code' => $this->code,
        ];
    }

    public function getChannel(): string
    {
        return 'DEFAULT';
    }
}