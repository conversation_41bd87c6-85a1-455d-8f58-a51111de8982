<?php

namespace Modules\User\Http\Actions\Certification;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Modules\User\Models\Certification;

class ManualAuthentication extends RowAction
{
    protected string $title = '手动认证';

    public function handle(): Response
    {
        $cert = Certification::find($this->getKey());

        $cert->pass();

        return $this->response()->success('手动认证成功')->refresh();
    }

    public function confirm(): array
    {
        return [
            '手动认证',
            '确定要使当前用户通过认证么?'
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}