<?php

namespace Modules\User\Http\Actions\Department;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\User\Http\Forms\JoinAuditForm;

class JoinAudit extends RowAction
{
    protected string $title = '审核';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(JoinAuditForm::make()->payload(['join_id' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}