<?php

namespace Modules\User\Http\Actions\User;

use App\Models\User;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;

class BatchLock extends BatchAction
{
    protected string $title = '批量锁定';

    public function handle(): Response
    {
        $key = $this->getKey();

        foreach (User::whereIn('id', $key)->get() as $user) {
            $user->is_lock = true;
            $user->save();
        }

        return $this->response()->success('批量锁定用户成功')->refresh();
    }

    public function confirm(): array
    {
        return [
            '批量锁定',
            '确定要批量锁定用户么？',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator() || $user->can(str_replace('\\', '_', get_called_class()));
    }
}