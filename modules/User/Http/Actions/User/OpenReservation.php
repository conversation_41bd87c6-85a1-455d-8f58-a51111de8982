<?php

namespace Modules\User\Http\Actions\User;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\User\Http\Forms\OpenReservationForm;

class OpenReservation extends RowAction
{
    protected string $title = '开通/调整预约权限';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(OpenReservationForm::make()->payload(['userId' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}