<?php

namespace Modules\User\Http\Actions\User;

use App\Models\User;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;

class ResetCenterAccount extends RowAction
{
    protected string $title = '重置用户中心错误账变';

    public function handle(): Response
    {
        $user = User::find($this->getKey());
        $user->resetCenterAccount();
        return $this->response()->success($this->title.'成功')->refresh();
    }

    public function confirm(): array
    {
        return [
            $this->title,
            '确定要'.$this->title.'么?'
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator() || $user->can(str_replace('\\', '_', get_called_class()));
    }
}