<?php

namespace Modules\User\Http\Actions\User;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\User\Http\Forms\UpgradeParentForm;

class UpgradeParent extends RowAction
{
    protected string $title = '调整隶属';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(UpgradeParentForm::make()->payload(['userId' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}