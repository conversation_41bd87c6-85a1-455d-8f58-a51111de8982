<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\User\Enums\CertificationStatus;
use Modules\User\Http\Actions\Certification\ManualAuthentication;
use Modules\User\Models\Certification;

class CertificationController extends AdminController
{
    protected string $title = '身份认证';

    public function grid(): Grid
    {
        return Grid::make(Certification::with(['user.info'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableRowSelector();
            $grid->disableEditButton();
            $grid->disableDeleteButton();

            $grid->actions([new ManualAuthentication]);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->equal('name', '真实姓名');
                $filter->equal('id_card_no', '身份证号码');
                $filter->equal('status', '认证状态')
                    ->select(CertificationStatus::STATUS_MAP);
                $filter->between('created_at', '创建时间')
                    ->datetime();
                $filter->between('updated_at', '更新时间')
                    ->datetime();
            });

            $grid->quickSearch(['id', 'user.username', 'user.info.nickname', 'name', 'id_card_no'])
                ->placeholder('ID/用户名/用户昵称/真实姓名/身份证号码');

            $grid->column('用户信息')
                ->display(fn() => $this->user->showName);
            $grid->column('name', '真实姓名');
            $grid->column('id_card_no', '身份证号码');
            $grid->column('status', '认证状态')
                ->using(CertificationStatus::STATUS_MAP);
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');
        });
    }
}