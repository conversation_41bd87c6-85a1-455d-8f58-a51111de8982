<?php

namespace Modules\User\Http\Controllers\Admin;

use App\Enums\ApplyStatus;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\User\Http\Actions\Department\DepartmentAudit;
use Modules\User\Models\DepartmentApply;

class DepartmentApplyController extends AdminController
{
    protected string $title = '成立部门申请';

    public function grid(): Grid
    {
        return Grid::make(DepartmentApply::with(['user', 'reviewer'])->latest(), function (Grid $grid) {
            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableRowSelector();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (in_array($actions->row->apply_status, [ApplyStatus::INIT, ApplyStatus::REVIEW])) {
                    $actions->append(new DepartmentAudit);
                }
            });

            $grid->quickSearch(['user.username', 'user.info.nickname', 'name'])
                ->placeholder('申请用户/部门名称');

            $grid->column('id');
            $grid->column('user', '申请用户')
                ->display(fn($user) => $user->showName);
            $grid->column('name', '部门名称');
            $grid->column('type', '申请类型');
            $grid->column('apply_text', '申请说明');
            $grid->column('rejcet_reason', '驳回原因');
            $grid->column('apply_status', '状态')
                ->using(ApplyStatus::STATUS_MAP)
                ->label(ApplyStatus::LABEL_MAP);
            $grid->column('reviewer', '审核用户')
                ->display(fn($user) => $user?->showName);
            $grid->column('created_at');
        });
    }
}