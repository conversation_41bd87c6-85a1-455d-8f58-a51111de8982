<?php

namespace Modules\User\Http\Controllers\Admin;

use App\Admin\Traits\WithUploads;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\User\Models\Identity;

class IdentityController extends AdminController
{
    use WithUploads;

    protected string $title = '用户身份';

    public function grid(): Grid
    {
        return Grid::make(Identity::orderBy('order')->withCount('users'), function (Grid $grid) {
            $grid->quickSearch(['name'])
                ->placeholder('身份名称');

            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '身份名称');
                $filter->between('price', '订阅价格');
                $filter->between('days', '有效时长');
                $filter->equal('serial_open', '身份编号')
                    ->radio([
                        0 => '否',
                        1 => '是',
                    ]);
                $filter->equal('is_default', '默认身份')
                    ->radio([
                        0 => '否',
                        1 => '是',
                    ]);
                $filter->equal('status', '状态')
                    ->radio([
                        0 => '禁用',
                        1 => '启用',
                    ]);
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->column('id');
            $grid->column('cover', 'ICON')
                ->thumb(32);
            $grid->column('name', '身份名称');
            $grid->column('price', '订阅价格');
            $grid->column('days', '有效时长（天）');
            $grid->column('order', '排序')
                ->orderable();
            $grid->column('serial_open', '身份编号')
                ->bool();
            $grid->column('is_default', '默认身份')
                ->bool();
            $grid->column('is_unique', '唯一身份')
                ->bool();
            $grid->column('status', '状态')
                ->bool();
            $grid->column('can_subscribe', '可订阅')
                ->bool();
            $grid->column('users_count', '用户数量');
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(Identity::class, function (Form $form) {
            $form->block(8, function (Form\BlockForm $form) {
                $form->title('基础信息');
                $form->text('name', '身份名称')
                    ->required();
                $form->textarea('description', '身份简介');
                $form->switch('status', '状态')
                    ->default(true);
                $this->cover($form, 'cover', '身份图标');
                $this->cover($form, 'card', '身份卡片');
                $form->showFooter();
            });
            $form->block(4, function (Form\BlockForm $form) {
                $form->title('订阅相关');
                $form->switch('is_unique', '唯一身份')
                    ->help('如果是唯一身份，订阅后则不允许订阅其他身份');
                $form->switch('is_default', '默认身份')
                    ->help('是否是用户注册的时候，默认给予的身份');
                $form->switch('can_subscribe', '可订阅')
                    ->default(true)
                    ->help('不可订阅的身份，不会在前台订阅列表显示');
                $form->currency('price', '订阅价格')
                    ->default(0);
                $form->number('days', '有效时长（天）')
                    ->default(365)
                    ->help('设置为0的时候，表示永久有效');

                $form->next(function (Form\BlockForm $form) {
                    $form->divider('身份编号');
                    $form->radio('serial_open', '开启编号')
                        ->options([0 => '不开启', 1 => '开启'])
                        ->default(0)
                        ->when(1, function (Form\BlockForm $form) {
                            $form->number('serial_places', '编号长度')->default(6);
                            $form->text('serial_prefix', '编号前缀');
                            $form->number('serial_reserve', '预留数量')
                                ->help('例如预留100，编号将从101开始');
                        });
                });
            });

            $form->block(8, function (Form\BlockForm $form) {
                $form->title('身份权益');
                $form->table('rules', '身份权益', function ($table) {
                    $table->text('key', '标识');
                    $table->text('value', '权益值');
                    $table->textarea('desc', '描述');
                    $this->cover($table, 'cover', '权益图标', true);
                });
            });

            $form->saving(function (Form $form) {
                if ($form->serial_open == 0) {
                    $form->serial_places = 0;
                    $form->serial_prefix = null;
                }
            });
        });
    }

    public function ajax(Request $request): LengthAwarePaginator
    {
        $q = $request->get('q');

        return Identity::ofEnabled()
            ->where('name', 'like', "%$q%")
            ->ordered()
            ->paginate(10, ['id', 'name as text']);
    }
}
