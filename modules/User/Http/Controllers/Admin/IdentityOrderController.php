<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\User\Enums\IdentityOrderStatus;
use Modules\User\Models\IdentityOrder;

class IdentityOrderController extends AdminController
{
    protected string $title = '身份订阅';

    protected array $description = [
        'index' => '订单列表',
    ];

    public function grid(): Grid
    {
        return Grid::make(IdentityOrder::with(['identity', 'user.info'])->latest(), function (Grid $grid) {
            $grid->disableRowSelector();
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->quickSearch(['user.username', 'user.info.nickname', 'identity.name'])
                ->placeholder('用户名/用户昵称/订阅身份');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->between('qty', '订阅周期');
                $filter->between('amount', '订单金额');
                $filter->equal('status', '状态')
                    ->select(IdentityOrderStatus::STATUS_MAP);
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });
            $grid->id();
            $grid->column('订阅用户')
                ->display(fn() => $this->user->showName);
            $grid->column('identity.name', '订阅身份');
            $grid->column('qty', '订阅周期');
            $grid->column('amount', '订单金额');
            $grid->column('status', '状态')
                ->using(IdentityOrderStatus::STATUS_MAP)
                ->label(IdentityOrderStatus::STATUS_LABEL);
            $grid->column('created_at');
        });
    }
}
