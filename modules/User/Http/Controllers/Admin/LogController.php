<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\User\Models\LoginLog;

class LogController extends AdminController
{
    protected string $title = '登录';

    public function grid(): Grid
    {
        return Grid::make(LoginLog::with(['user.info'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableBatchActions();
            $grid->disableActions();
            $grid->disableRowSelector();

            $grid->quickSearch(['id', 'user.username', 'user.info.nickname', 'name', 'device_id', 'ip'])
                ->placeholder('ID/用户名/用户昵称/设备名称/设备识别码/登录IP');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->equal('name', '设备名称');
                $filter->equal('device_id', '设备识别码');
                $filter->equal('ip', '登录IP');
                $filter->between('created_at', '登录时间')
                    ->datetime();
            });

            $grid->column('id');
            $grid->column('登录用户')
                ->display(fn() => $this->user->showName);
            $grid->column('name', '设备名称');
            $grid->column('device_id', '设备识别码');
            $grid->column('ip', '登录IP');
            $grid->column('created_at', '登录时间');
        });
    }
}