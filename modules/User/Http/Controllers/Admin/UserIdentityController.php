<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\User\Http\Actions\User\UpdateIdentitySerial;
use Modules\User\Models\UserIdentity;

class UserIdentityController extends AdminController
{
    protected string $title = '会员信息';

    public function grid(): Grid
    {
        return Grid::make(UserIdentity::with(['user', 'identity'])->latest(), function (Grid $grid) {
            $grid->disableDeleteButton();
            $grid->disableEditButton();
            $grid->disableCreateButton();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '昵称');
                $filter->like('identity.name', '订阅类型');
                $filter->equal('serial', '会员编号');
                $filter->between('started_at', '开始时间')
                    ->datetime();
                $filter->between('ended_at', '结束时间')
                    ->datetime();
            });

            $grid->quickSearch(['user.username', 'user.info.nickname', 'identity.name', 'serial']);

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->identity && $actions->row->identity->can_subscribe && $actions->row->identity->serial_open) {
                    $actions->append(new UpdateIdentitySerial);
                }
            });

            $grid->column('用户')
                ->display(fn() => $this->user->showName);
            $grid->column('identity.name', '订阅类型');
            $grid->column('serial', '会员编号')
                ->display(function () {
                    return $this->getSerialNoAttribute();
                });
            $grid->column('started_at', '开始时间');
            $grid->column('ended_at', '结束时间');
            $grid->column('created_at');
        });
    }
}