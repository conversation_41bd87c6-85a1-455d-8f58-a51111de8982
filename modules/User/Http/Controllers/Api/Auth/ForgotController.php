<?php

namespace Modules\User\Http\Controllers\Api\Auth;

use App\Http\Controllers\ApiController;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Modules\User\Extensions\Sms\DefaultMessage;
use Modules\User\Extensions\Sms\ForgotMessage;
use Modules\User\Extensions\Sms\LoginMessage;
use Modules\User\Extensions\Sms\RegisterMessage;
use Modules\User\Http\Requests\ForgotSmsPassRequest;
use Modules\User\Http\Requests\ForgotSmsRequest;
use Modules\User\Http\Requests\SmsRequest;
use Modules\User\Models\Sms;

class ForgotController extends ApiController
{
    public function index(ForgotSmsRequest $request): JsonResponse
    {
        $mobile  = $request->safe()->mobile;
        $message = new ForgotMessage();

        # 如果用户发送了新的验证码，要把之前所有的验证码，全部都给取消可用
        Sms::where('mobile', $mobile)
            ->where('channel', $message->getChannel())
            ->where('used', 0)
            ->update(['used' => 2]);

        $smsId = Sms::send($mobile, $message);

        return $this->success([
            'sms_id' => $smsId,
        ]);
    }

    public function password(ForgotSmsPassRequest $request): JsonResponse
    {
        $username       = $request->safe()->username;
        $user           = User::where('username', $username)->first();
        $user->password = $request->password;
        $user->save();

        return $this->success('重置密码成功');
    }
}