<?php

namespace Modules\User\Http\Controllers\Api\Auth;

use App\Facades\Api;
use App\Models\User;
use App\Packages\XinHuaERP\XinHuaERP;
use Illuminate\Http\JsonResponse;
use Modules\User\Http\Requests\Auth\LoginRequest;
use Modules\User\Http\Requests\Auth\SmsLoginRequest;

class LoginController extends AuthController
{
    /**
     * Notes   : 使用【用户名、密码】登录
     *
     * @Date   : 2023/8/10 10:18
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Http\Requests\Auth\LoginRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function password(LoginRequest $request): JsonResponse
    {
        $credentials = $request->safe()->only(['username', 'password']);

        $credentials['is_lock'] = 0;

        if (Api::attempt($credentials)) {
            $user = Api::user();
            if ($unionId = $user->getUnionId()) {
                XinHuaERP::user()->addVisitor($unionId);
            }
            return $this->afterLogin($user);
        } else {
            return $this->failed('账号或密码错误');
        }
    }

    /**
     * Notes   : 使用【短信验证码】登录
     *
     * @Date   : 2023/8/9 17:08
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Http\Requests\Auth\SmsLoginRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sms(SmsLoginRequest $request): JsonResponse
    {
        $user = User::where('username', $request->safe()->username)->first();

        if (! $user->is_lock) {
            return $this->failed('用户已被禁止登录');
        }
        Api::login($user);
        if ($unionId = $user->getUnionId()) {
            XinHuaERP::user()->addVisitor($unionId);
        }
        return $this->afterLogin($user);
    }
}
