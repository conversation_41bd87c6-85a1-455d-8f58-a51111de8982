<?php

namespace Modules\User\Http\Controllers\Api\Auth;

use App\Models\User;
use App\Packages\XinHuaERP\XinHuaERP;
use Illuminate\Http\JsonResponse;
use Modules\User\Http\Requests\Auth\RegisterRequest;
use Modules\User\Http\Requests\Auth\SmsRegisterRequest;

class RegisterController extends AuthController
{
    /**
     * Notes   : 使用【用户名、密码】注册
     *
     * @Date   : 2023/7/14 09:23
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Http\Requests\Auth\RegisterRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function password(RegisterRequest $request): JsonResponse
    {
        $user = User::create($request->safe()->toArray());
        
        return $this->afterLogin($user);
    }

    /**
     * Notes   : 使用【短信验证码】注册
     *
     * @Date   : 2023/8/9 15:59
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Http\Requests\Auth\SmsRegisterRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sms(SmsRegisterRequest $request): JsonResponse
    {
        $user = User::create([
            'username' => $request->safe()->username,
        ]);

        return $this->afterLogin($user);
    }
}