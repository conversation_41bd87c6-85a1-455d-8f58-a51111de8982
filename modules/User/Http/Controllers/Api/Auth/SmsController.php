<?php

namespace Modules\User\Http\Controllers\Api\Auth;

use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\User\Extensions\Sms\DefaultMessage;
use Modules\User\Extensions\Sms\LoginMessage;
use Modules\User\Extensions\Sms\RegisterMessage;
use Modules\User\Http\Requests\SmsRequest;
use Modules\User\Models\Sms;

class SmsController extends ApiController
{
    /**
     * Notes   : 获取登录验证码
     *
     * @Date   : 2022/12/26 17:35
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Http\Requests\SmsRequest  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function index(SmsRequest $request): JsonResponse
    {
        $mobile = $request->safe()->mobile;

        $message = match ($request->channel) {
            'LOGIN' => new LoginMessage(),
            'REGISTER' => new RegisterMessage(),
            default => new DefaultMessage(),
        };

        # 如果用户发送了新的验证码，要把之前所有的验证码，全部都给取消可用
        Sms::where('mobile', $mobile)
            ->where('channel', $request->safe()->channel)
            ->where('used', 0)
            ->update(['used' => 2]);

        $smsId = Sms::send($mobile, $message);

        return $this->success([
            'sms_id' => $smsId,
        ]);
    }
}