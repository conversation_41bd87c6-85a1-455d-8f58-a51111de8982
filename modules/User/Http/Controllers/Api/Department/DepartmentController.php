<?php

namespace Modules\User\Http\Controllers\Api\Department;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\User\Http\Requests\DepartmentUpdateRequest;
use Modules\User\Http\Resources\DepartmentCollection;
use Modules\User\Http\Resources\DepartmentResource;
use Modules\User\Models\Department;

class DepartmentController extends ApiController
{
    public function index(): JsonResponse
    {
        $list = Department::ofEnabled()
            ->select(['id', 'name', 'alias', 'cover', 'remark', 'status', 'allow_user', 'created_at'])
            ->where('parent_id', 0)
            ->ordered()
            ->get();

        return $this->success(DepartmentCollection::collection($list));
    }

    public function show(Department $department): JsonResponse
    {
        if (! $department->status) {
            return $this->failed('部门不存在', 404);
        }
        return $this->success(new DepartmentResource($department));
    }

    /**
     * Notes   : 更新部门资料
     *
     * @Date   : 2023/9/7 16:12
     * <AUTHOR> <Jason.C>
     */
    public function update(Department $department, DepartmentUpdateRequest $request): JsonResponse
    {
        $user = Api::user();

        $depIds = $user->departments->pluck('id')->toArray();
        if (! in_array($department->id, $depIds)) {
            return $this->failed('无权操作');
        }

        $department->update($request->safe()->toArray());

        return $this->success();
    }
}