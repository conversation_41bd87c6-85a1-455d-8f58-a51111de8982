<?php

namespace Modules\User\Http\Controllers\Api\Department;

use App\Enums\ApplyStatus;
use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Contracts\Database\Eloquent\Builder as WithBuilder;
use Illuminate\Http\JsonResponse;
use Modules\User\Http\Requests\DepartmentJoinAuditRequest;
use Modules\User\Http\Requests\DepartmentJoinRequest;
use Modules\User\Http\Resources\DepartmentJoinCollection;
use Modules\User\Models\DepartmentJoin;

class JoinController extends ApiController
{
    /**
     * Notes   : 我的申请
     *
     * @Date   : 2023/9/7 13:28
     * <AUTHOR> <Jason.C>
     */
    public function index(): JsonResponse
    {
        $list = DepartmentJoin::ofUser(Api::user())
            ->select([
                'id', 'user_id', 'department_id', 'approver_type', 'approver_id',
                'description', 'reason', 'status', 'created_at', 'updated_at',
            ])
            ->with([
                'user'          => function (WithBuilder $query) {
                    $query->select(['id', 'username']);
                }, 'user.info'  => function (WithBuilder $query) {
                    $query->select(['user_id', 'nickname', 'avatar']);
                }, 'department' => function (WithBuilder $query) {
                    $query->select(['id', 'name', 'alias', 'cover', 'remark', 'status', 'allow_user', 'created_at']);
                }
            ])
            ->paginate();

        return $this->success(new DepartmentJoinCollection($list));
    }

    /**
     * Notes   : 保存信息
     *
     * @Date   : 2023/9/7 13:29
     * <AUTHOR> <Jason.C>
     */
    public function store(DepartmentJoinRequest $request): JsonResponse
    {
        DepartmentJoin::create([
            'user_id'       => Api::id(),
            'department_id' => $request->safe()->department_id,
            'description'   => $request->safe()->description,
        ]);

        return $this->success('申请提交成功，请等待审核。');
    }

    /**
     * Notes   : 入会申请
     *
     * @Date   : 2023/9/7 15:20
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(): JsonResponse
    {
        $user = Api::user();

        $depIds = $user->departments->pluck('id');
        $list   = DepartmentJoin::select([
            'id', 'user_id', 'department_id', 'approver_type', 'approver_id',
            'description', 'reason', 'status', 'created_at', 'updated_at',
        ])
            ->whereIn('department_id', $depIds)
            ->orderBy('status')
            ->orderBy('created_at', 'desc')
            ->paginate();

        return $this->success(new DepartmentJoinCollection($list));
    }

    /**
     * Notes   : 审核，目前缺少权限验证，是否本部门的验证
     *
     * @Date   : 2023/9/7 13:29
     * <AUTHOR> <Jason.C>
     */
    public function audit(DepartmentJoin $join, DepartmentJoinAuditRequest $request): JsonResponse
    {
        $user = Api::user();

        $depIds = $user->departments->pluck('id')->toArray();
        if (! in_array($join->department_id, $depIds)) {
            return $this->failed('无权操作');
        }

        switch ($request->safe()->status) {
            case ApplyStatus::PASS->value:
                $join->pass(Api::user());
                break;
            case ApplyStatus::REJECT->value:
                $join->reject(Api::user(), $request->safe()->reason);
                break;
        }

        return $this->success('');
    }
}