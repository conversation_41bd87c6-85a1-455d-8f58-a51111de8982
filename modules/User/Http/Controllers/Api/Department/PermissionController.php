<?php

namespace Modules\User\Http\Controllers\Api\Department;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\User\Http\Resources\UserPermissionResource;

class PermissionController extends ApiController
{
    public function index(): JsonResponse
    {
        $user        = Api::user();
        $permissions = $user->getAllPermissions();

        return $this->success([
            'roles'       => $user->getRoleNames(),
            'permissions' => UserPermissionResource::collection($permissions),
        ]);
    }
}