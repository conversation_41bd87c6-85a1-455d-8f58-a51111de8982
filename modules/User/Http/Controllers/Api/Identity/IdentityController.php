<?php

namespace Modules\User\Http\Controllers\Api\Identity;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Payment\Http\Resources\PaymentableResource;
use Modules\User\Http\Resources\IdentityResource;
use Modules\User\Models\Identity;

class IdentityController extends ApiController
{
    /**
     * Notes   : 获取所有可订阅的身份
     *
     * @Date   : 2023/8/10 10:19
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $identities = Identity::ofEnabled()
            ->select([
                'id', 'name', 'description', 'cover', 'price', 'is_default',
                'is_unique', 'days', 'conditions', 'rules', 'card',
            ])
            ->when($request->all, function (Builder $builder) {
            }, function (Builder $builder) {
                $builder->where('can_subscribe', true);
            })
            ->ordered()
            ->get();

        return $this->success(IdentityResource::collection($identities));
    }

    /**
     * Notes   : 身份详情
     *
     * @Date   : 2023/10/10 13:35
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Models\Identity  $identity
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Identity $identity)
    {
        return $this->success(new IdentityResource($identity));
    }

    /**
     * Notes   : 创建订阅
     *
     * @Date   : 2023/4/19 17:45
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Http\Request  $request
     * @param  \Modules\User\Models\Identity  $identity
     * @return \Illuminate\Http\JsonResponse
     */
    public function subscribe(Request $request, Identity $identity): JsonResponse
    {
        $qty = $request->qty ?? 1;

        if (! $identity->status || ! $identity->can_subscribe) {
            return $this->failed('当前身份不可订阅');
        }

        $user = Api::user();

        $has = $user->identities()
            ->whereNot('identity_id', $identity->getKey())
            ->where('is_unique', 1)
            ->first();
        if ($has) {
            return $this->failed(sprintf('已拥有[%s]身份，不允许订阅其他身份', $has->name));
        }

        $order = $identity->orders()->create([
            'user'   => Api::user(),
            'qty'    => $qty,
            'amount' => bcmul($identity->price, $qty, 2),
        ]);
        return $this->success(new PaymentableResource($order));
    }
}