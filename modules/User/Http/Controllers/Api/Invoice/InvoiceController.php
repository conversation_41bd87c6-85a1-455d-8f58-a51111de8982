<?php

namespace Modules\User\Http\Controllers\Api\Invoice;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Database\Eloquent\Builder;
use Modules\User\Http\Requests\Invoice\CreateRequest;
use Modules\User\Http\Resources\Invoice\InvoiceResource;
use Modules\User\Models\InvoiceTitle;

class InvoiceController extends ApiController
{
    public function init()
    {
        $channel = getKeyTitle(InvoiceTitle::CHANNEL);
        $type    = getKeyTitle(InvoiceTitle::TYPE);

        return $this->success([
            'channel' => $channel,
            'type'    => $type,
        ]);
    }

    public function store(CreateRequest $request)
    {
        $user   = Api::user();
        $exists = $user->invoices()
            ->ofTitle($request->title)
            ->when($request->type == InvoiceTitle::TYPE_COMPANY, function (Builder $builder) use ($request) {
                $builder->where('no', $request->no);
            })
            ->exists();
        if ($exists) {
            return $this->failed('发票抬头信息已存在');
        }
        if ($user->invoices()->create([
            'channel'      => $request->channel,
            'type'         => $request->type,
            'title'        => $request->title,
            'no'           => $request->no,
            'account_bank' => $request->account_bank,
            'account_no'   => $request->account_no,
            'reg_mobile'   => $request->reg_mobile,
            'province_id'  => $request->province_id,
            'city_id'      => $request->city_id,
            'district_id'  => $request->district_id,
            'address'      => $request->address,
            'default'      => $request->default,
            'email'      => $request->email,
        ])) {
            return $this->success('创建成功');
        } else {
            return $this->success('创建失败');
        }
    }

    public function show(InvoiceTitle $invoice)
    {
        $this->checkPermission($invoice);
        return $this->success(new InvoiceResource($invoice));
    }

    public function update(InvoiceTitle $invoice, CreateRequest $request)
    {
        $this->checkPermission($invoice);
        if ($invoice->update([
            'channel'      => $request->channel,
            'type'         => $request->type,
            'title'        => $request->title,
            'no'           => $request->no,
            'account_bank' => $request->account_bank,
            'account_no'   => $request->account_no,
            'reg_mobile'   => $request->reg_mobile,
            'province_id'  => $request->province_id,
            'city_id'      => $request->city_id,
            'district_id'  => $request->district_id,
            'address'      => $request->address,
            'default'      => $request->default,
            'email'      => $request->email,

        ])) {
            return $this->success(lecho('Modified successfully'));
        } else {
            return $this->failed(lecho('Modification failed'));
        }
    }

    public function destroy(InvoiceTitle $invoice)
    {
        $this->checkPermission($invoice);
        if ($invoice->delete()) {
            return $this->success('删除成功');
        } else {
            return $this->failed('删除失败');
        }
    }

    public function lists()
    {
        $user = Api::user();
        return $this->success([
            'person'  => InvoiceResource::collection($user->invoices()
//                ->ofChannel(InvoiceTitle::ORDINARY)
                ->ofType(InvoiceTitle::TYPE_PERSON)
                ->latest()
                ->get()),
            'company' => InvoiceResource::collection($user->invoices()
                ->ofChannel(InvoiceTitle::ORDINARY)
                ->ofType(InvoiceTitle::TYPE_COMPANY)
                ->latest()
                ->get()),
            'special' => InvoiceResource::collection($user->invoices()
                ->ofChannel(InvoiceTitle::SPECIAL)
                ->ofType(InvoiceTitle::TYPE_COMPANY)
                ->latest()
                ->get()),
        ]);
    }

}
