<?php

namespace Modules\User\Http\Controllers\Api\Sign;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Coupon\Models\Coupon;
use Modules\User\Models\UserSignLog;
use Modules\User\Models\UserSignRule;

class SignController extends ApiController
{
    public function index(Request $request)
    {
        $type        = $request->type ?: 'week';
        $user        = Api::user();
        $timeBetween = match ($type) {
            'week' => [now()->startOfWeek(), now()->endOfWeek()],
            'month' => [now()->startOfMonth()->startOfWeek(), now()->endOfMonth()->endOfWeek()],
            default => [],
        };
        $signed      = $user->signs()
            ->ofBetween($timeBetween)
            ->select('*', DB::raw('DATE_FORMAT(sign_at,"%Y-%m-%d") as sign_at_day'))
            ->get();
        $daies       = [];
        for ($time = $timeBetween[0]; $time < $timeBetween[1]; $time->addDay()) {
            $daies[] = [
                'date'          => $time->toDateString(),
                'day'           => $time->day,
                'current_month' => $time->isCurrentMonth(),
                'is_sign'       => (bool) $signed->where('sign_at_day', $time->toDateString())->first(),
                'today'         => $time->isCurrentDay(),
            ];
        }
        $daySign = $user->signs()->ofDay(now())->first();
        $tasks   = UserSignRule::ofEnabled()
            ->orderBy('day')
            ->latest()
            ->get()
            ->map(function ($item) use ($user) {
                return [
                    'title'    => $item->day > 1 ? '连续签到' : '签到',
                    'day'      => $item->day,
                    'only_one' => (bool)$item->only_one,
                    'coupon'   => $item->coupon ? [
                        'name'   => $item->coupon->name,
                        'number' => $item->number,
                    ] : (object) [],
                    'score'    => $item->score,
                    'complete' => $user->signLogs()
                        ->when($item->only_one === 0, function (Builder $builder) {
                            $builder->ofDay();
                        })
                        ->ofRule($item)
                        ->exists(),
                ];
            })
            ->toArray();
        return $this->success([
            'type'              => $type,
            'daies'             => $daies,
            'total'             => $user->signs()->count(),
            'can_sign'          => ! $daySign,
            'continuous_day'    => $daySign->continuous_day ?? 0,
            'task_time_between' => null,
            'task'              => $tasks,
        ]);
    }

    public function sign()
    {
        $user = Api::user();
        if ($user->signs()->ofDay(now())->exists()) {
            return $this->failed('今日已经签到');
        }
        $last          = $user->signs()->ofDay(now()->subDay())->value('continuous_day') ?: 0;
        $continuousDay = $last + 1;
        $log           = $user->signs()->create([
            'sign_at'        => now(),
            'continuous_day' => $continuousDay,
        ]);
        $rules         = UserSignRule::ofEnabled()
            ->where(function (Builder $builder) use ($continuousDay, $user) {
                $noRuleIds = UserSignLog::whereRelation('rule', 'only_one', '=', 1)
                    ->where('user_id', $user->id)
                    ->pluck('rule_id')
                    ->toArray();
                $builder->where('only_one', 1)
                    ->where('day', $continuousDay)
                    ->when($noRuleIds, function (Builder $builder, $noRuleIds) {
                        $builder->whereNotIn('id', $noRuleIds);
                    });
            })
            ->orWhere(function (Builder $builder) use ($continuousDay) {
                $builder->where('only_one', 0)
                    ->whereRaw("MOD({$continuousDay}, day) = 0");
            })
            ->get();
        $message       = '';
        DB::transaction(function () use ($rules, $user, &$message, $log) {
            if ($rules->count() > 0) {
                $score = $rules->sum('score');
                foreach ($rules->where('number', '>', 0) as $rule) {
                    if ($rule->coupon && $rule->number > 0) {
                        $count = 0;
                        for ($i = 1; $i <= $rule->number; $i++) {
                            try {
                                $rule->coupon->receive($user);
                                $count++;
                            } catch (\Exception $exception) {
                            }
                        }
                        $message .= "优惠券【{$rule->coupon->name}】× {$count}张\n";
                    }
                }
                if ($score > 0) {
                    $user->account->exec('recharge_score', $score, null, [
                        'type' => 'sign',
                        'date' => now()->toDateString(),
                    ]);
                    $message .= "积分 × $score";
                }
                UserSignLog::addLogs($log, $rules, $user);
                $log->message = $message;
                $log->save();
            }
        });

        return $this->success([
            'sign_at'        => $log->sign_at->toDateTimeString(),
            'continuous_day' => $log->continuous_day,
            'message'        => $message,
        ]);
    }

}