<?php

namespace Modules\User\Http\Controllers\Api\User;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Lara<PERSON>\Sanctum\PersonalAccessToken;
use Modules\Interaction\Http\Resources\FavoriteResource;
use Modules\Interaction\Http\Resources\SubscribeResource;
use Modules\Interaction\Models\Favorite;
use Modules\Interaction\Models\Subscribe;
use Modules\User\Enums\Gender;
use Modules\User\Extensions\Sms\DefaultMessage;
use Modules\User\Http\Requests\UpdateUserInfoRequest;
use Modules\User\Http\Resources\UserInfoResource;
use Modules\User\Http\Resources\UserPcInfoResource;
use Modules\User\Models\Sms;

class InfoController extends ApiController
{

    public function safe()
    {
        $user = Api::user();
        return $this->success([
            'username'    => hideMobilePhoneNo($user->username),
            'bind_wechat' => $user->getUnionId() ? true : false,
        ]);
    }

    public function pcUserOther()
    {
        $user       = Api::user();
        $favorites  = Favorite::ofUser(Api::user())
            ->limit(5)
            ->latest()
            ->get();
        $subscribes = Subscribe::ofUser($user)
            ->limit(5)
            ->latest()
            ->get();
        return $this->success([
            'favorites'  => $favorites->map(function ($item) {
                return new FavoriteResource($item, true, false);
            }),
            'subscribes' => $subscribes->map(function ($item) {
                return new SubscribeResource($item, true, false);
            }),
        ]);
    }

    /**
     * Notes   : 获取当前用户的基础资料
     *
     * @Date   : 2023/9/13 09:36
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        $user = Api::user();

        return $this->success(new UserInfoResource($user));
    }

    public function pc(): JsonResponse
    {
        $user = Api::user();

        return $this->success(new UserPcInfoResource($user));
    }

    /**
     * 编辑页面前置，仅头像及昵称
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function settingInit(): JsonResponse
    {
        $user = Api::user();
        return $this->success([
            'username' => hideMobilePhoneNo($user->username),
            'nickname' => $user->info->nickname,
            'avatar'   => [
                'url'  => $user->info->avatar_url,
                'path' => $user->info->avatar ?: '',
            ],
            'birthday' => $user->info->birthday?->toDateString(),
            'gender'   => [
                'key'   => $user->info->gender,
                'value' => Gender::GENDER_MAP[$user->info->gender->value],
            ],
        ]);
    }

    public function logSms()
    {
        $user    = Api::user();
        $message = new DefaultMessage();
        Sms::where('mobile', $user->username)
            ->where('channel', 'DEFAULT')
            ->where('used', 0)
            ->update(['used' => 2]);

        $smsId = Sms::send($user->username, $message);

        return $this->success([
            'sms_id' => $smsId,
        ]);
    }

    /**
     * Notes   : 修改用户资料
     *
     * @Date   : 2023/8/4 16:46
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Http\Requests\UpdateUserInfoRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateUserInfoRequest $request): JsonResponse
    {
        $user = Api::user();
        $data = $request->safe()->all();
        $user->info->update($data);

        return $this->success();
    }

    public function logOff()
    {
        $user = Api::user();
        $user->info()->update([
            'nickname' => '用户已注销',
        ]);
        $user->wechat()->update([
            'wx_openid'   => null,
            'mini_openid' => null,
            'union_id'    => null,
        ]);
        $user->apple()->update([
            'name'  => null,
            'email' => null,
        ]);
        $count          = User::where('username', 'like', "{$user->username}_%")->count() + 1;
        $user->username .= '_'.$count;
        $user->save();
        PersonalAccessToken::where('tokenable_type', $user->getMorphClass())
            ->where('tokenable_id', $user->getKey())
            ->delete();
        return $this->success('账号注销成功');
    }
}
