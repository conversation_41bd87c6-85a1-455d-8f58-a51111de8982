<?php

namespace Modules\User\Http\Forms;

use App\Models\User;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Exception;

class UpgradeParentForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $user = User::find($this->payload['userId']);

        try {
            # 如果用户没创建关联关系，在这里给创建一下
            if (is_null($user->relation->parent_id)) {
                $user->relation()->create(['parent_id' => 0, 'layer' => 1, 'line' => '0,']);
                $user->refresh();
            }

            $user->relation->changeParent($input['parent_id'] ?? 0);
            return $this->response()->success('隶属关系调整成功')->refresh();
        } catch (Exception $exception) {
            return $this->response()->error($exception->getMessage());
        }
    }

    /**
     * @throws \Dcat\Admin\Exception\RuntimeException
     */
    public function form(): void
    {
        $this->select('parent_id', '上级用户')
            ->options(function ($userId) {
                if ($userId) {
                    return [$userId => User::find($userId)->showName];
                } else {
                    return [0 => '无'];
                }
            })
            ->ajax(route('admin.user.users.ajax'));

        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }

    public function default(): array
    {
        $user = User::find($this->payload['userId']);

        return [
            'parent_id' => $user->relation->parent_id ?? 0,
        ];
    }
}