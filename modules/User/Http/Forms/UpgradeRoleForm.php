<?php

namespace Modules\User\Http\Forms;

use App\Models\User;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Spatie\Permission\Models\Role;

class UpgradeRoleForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $user = User::find($this->payload['userId']);

        $user->roles()->sync($input['role_id']);

        return $this->response()->success('用户角色调整成功')->refresh();
    }

    public function form(): void
    {
        $this->multipleSelect('role_id', '用户角色')
            ->options(function ($roleId) {
                if ($roleId) {
                    return Role::whereIn('id', $roleId)->pluck('name', 'id');
                } else {
                    return [];
                }
            })
            ->required()
            ->ajax(route('admin.user.roles.ajax'));
        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }

    public function default(): array
    {
        $user = User::find($this->payload['userId']);

        return [
            'role_id' => $user->roles->pluck('id'),
        ];
    }
}