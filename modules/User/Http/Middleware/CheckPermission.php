<?php

namespace Modules\User\Http\Middleware;

use App\Facades\Api;
use Closure;
use Exception;
use Illuminate\Http\Request;

class CheckPermission
{
    /**
     * @throws \Exception
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Api::user();

        $abilities = sprintf('%s@%s', $request->method(), $request->route()->uri());

        if (! $user) {
            throw new Exception('用户未登录', 401);
        }

        if ($user->cannot($abilities)) {
            throw new Exception('无权限', 403);
        }

        return $next($request);
    }
}