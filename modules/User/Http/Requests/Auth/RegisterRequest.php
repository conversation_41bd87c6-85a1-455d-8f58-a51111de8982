<?php

namespace Modules\User\Http\Requests\Auth;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\Password;
use Modules\User\Rules\MobileRule;

class RegisterRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'username' => [
                'required',
                'bail',
                new MobileRule(),
                'unique:App\Models\User',
            ],
            'password' => [
                'bail',
                'required',
                config('user.STRONG_PASSWORD') ? Password::min(8)->mixedCase()->numbers() : Password::min(6),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'username.required' => '手机号必须填写',
            'username.unique'   => '手机号已注册',
            'password.required' => '密码必须填写',
        ];
    }
}
