<?php

namespace Modules\User\Http\Requests\Auth;

use App\Http\Requests\BaseFormRequest;
use Modules\User\Rules\MobileRule;

class SmsLoginRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'username' => [
                'bail',
                'required',
                new MobileRule(),
                'exists:App\Models\User',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'username.required' => '手机号码必须填写',
            'username.exists'   => '账户不存在',
        ];
    }
}
