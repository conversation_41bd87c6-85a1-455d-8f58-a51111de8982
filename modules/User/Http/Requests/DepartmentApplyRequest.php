<?php

namespace Modules\User\Http\Requests;

use App\Facades\Api;
use App\Http\Requests\BaseFormRequest;
use Closure;
use Modules\User\Models\DepartmentApply;

class DepartmentApplyRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'name'       => [
                'bail',
                'required',
                'min:3',
                'max:64',
                function (string $attribute, mixed $value, Closure $fail) {
                    if (DepartmentApply::ofUser(Api::user())->ofInited()->exists()) {
                        $fail('您有正在审核中的申请，请勿重复提交');
                        return;
                    }
                    if (DepartmentApply::ofUser(Api::user())->ofPassed()->exists()) {
                        $fail('您已有审核通过的申请，请勿重复提交');
                    }
                },
            ],
            'type'       => 'bail|required',
            'apply_text' => 'bail|required|max:1024',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required'        => '部门名称必须填写',
            'name.min'             => '部门名称最少:min字符',
            'name.max'             => '部门名称最大:max字符',
            'type.required'        => '申请类型必须填写',
            'apply_text.required' => '申请原因必须填写',
            'apply_text.max'      => '申请原因最多:max字符',
        ];
    }
}
