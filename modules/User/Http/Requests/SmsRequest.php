<?php

namespace Modules\User\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Modules\User\Rules\CanSmsRule;
use Modules\User\Rules\MobileRule;

class SmsRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'mobile'  => [
                'required',
                'bail',
                new MobileRule(),
            ],
            'channel' => [
                'required',
                'bail',
                new CanSmsRule(),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'username.required' => '手机号必须填写',
            'channel.required'  => '短信渠道必须选择',
        ];
    }
}
