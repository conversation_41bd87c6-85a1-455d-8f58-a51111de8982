<?php

namespace Modules\User\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use App\Rules\FileExistsRule;

class UpdateUserInfoRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'nickname' => 'sometimes|bail|required|string|min:2|max:32',
            'gender'   => 'sometimes|bail|required|in:0,1,2,S,M,F,保密,男,女',
            'avatar'   => [
                'sometimes',
                'bail',
                'nullable',
                'string',
                new FileExistsRule('头像文件不存在'),
            ],
            'birthday' => 'sometimes|bail|nullable|date',
        ];
    }

    public function messages(): array
    {
        return [
            'nickname.required' => '昵称必须填写',
            'nickname.string'   => '昵称必须是字符',
            'nickname.min'      => '昵称长度最小:min位',
            'nickname.max'      => '昵称长度最大:max位',
            'gender.required'   => '性别必须填写',
            'gender.in'         => '性别选择有误',
            'avatar.required'   => '头像必须上传',
            'avatar.string'     => '头像信息有误',
            'birthday.required' => '出生日期必须填写',
            'birthday.date'     => '出生日期格式不正确',
        ];
    }
}
