<?php

namespace Modules\User\Http\Resources;

use App\Facades\Api;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InviteCodeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'code'     => app('user.hashids')->encode(Api::id()),
            'user_id'  => $this->id,
            'nickname' => $this->info->nickname,
            'avatar'   => $this->info->avatar_url,
        ];
    }
}