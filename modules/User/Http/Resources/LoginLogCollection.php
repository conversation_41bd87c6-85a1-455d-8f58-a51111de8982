<?php

namespace Modules\User\Http\Resources;

use App\Http\Resources\BaseCollection;

class LoginLogCollection extends BaseCollection
{
    public function toArray($request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return [
                    'user_agent' => $item->user_agent,
                    'device_id'  => $item->device_id,
                    'ip'         => $item->ip,
                    'created_at' => (string) $item->created_at,
                ];
            }),
            'page' => $this->page(),
        ];
    }
}
