<?php

namespace Modules\User\Http\Resources;

use App\Http\Resources\BaseCollection;

class SlaveCollection extends BaseCollection
{
    public function toArray($request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                $identity = $item->identities->first();

                return [
                    'user_id'    => $item->id,
                    'username'   => $item->username,
                    'nickname'   => $item->info->nickname,
                    'avatar'     => $item->info->avatar_url,
                    'created_at' => (string) $item->created_at,
                    'identity'   => new UserIdentityResource($identity),
                ];
            }),
            'page' => $this->page(),
        ];
    }
}