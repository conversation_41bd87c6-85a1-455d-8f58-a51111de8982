<?php

namespace Modules\User\Jobs;

use App\Jobs\BaseJob;
use App\Models\CardItem;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Exception;
use Modules\User\Enums\IdentityChannel;

class ActivateCardJob extends BaseJob
{
    public string $queue = 'CARD';

    public function __construct(protected CardItem $cardItem, protected User $user)
    {
    }

    public function handle(): void
    {
        try {
            DB::transaction(function () {
                if ($this->cardItem->status == CardItem::STATUS_INIT) {
                    if (filled($this->cardItem->mobile) && $this->cardItem->mobile != $this->user->username) {
                        throw new Exception('会员卡指定用户与登录用户不符');
                    }

                    if ($this->cardItem->amount > 0) {
                        $this->user->account->exec('recharge_balance', $this->cardItem->amount, null, [
                            'remark'  => "会员卡激活【{$this->cardItem->no}】",
                            'card_id' => $this->cardItem->id,
                        ]);
                    }
                    if ($this->cardItem->identity > 0) {
                        $this->cardItem->identityModel->entry($this->user, IdentityChannel::CARD);
                    }
                    $this->cardItem->user_id = $this->user->id;
                    $this->cardItem->status  = CardItem::STATUS_USED;
                    $this->cardItem->save();
                } else {
                    throw new Exception('会员卡状态不正确');
                }
            });
        } catch (Exception $exception) {
            $this->job->fail($exception->getMessage());
        }
    }
}