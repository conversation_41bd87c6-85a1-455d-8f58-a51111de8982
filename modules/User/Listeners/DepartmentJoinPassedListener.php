<?php

namespace Modules\User\Listeners;

use App\Models\Module;
use Modules\User\Events\DepartmentJoinPassed;
use Modules\User\Notifications\DepartmentJoinNotification;

class DepartmentJoinPassedListener extends UserBaseListener
{
    public function handle(DepartmentJoinPassed $event): void
    {
        $join = $event->join;

        $join->user->departments()->sync($join->department);

        if (Module::isEnabled('Notification')) {
            $join->user->notify(new DepartmentJoinNotification($join));
        }
    }
}