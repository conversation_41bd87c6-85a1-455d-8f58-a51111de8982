<?php

namespace Modules\User\Listeners;

use App\Events\ScoreAddEvent;
use App\Models\SystemConfig;
use Modules\User\Events\Authenticated;

class RecordLoginListener
{
    public function handle(Authenticated $event): void
    {
        $brandName  = app('user.device')->getBrandName();
        $brandModel = app('user.device')->getModel();
        $name       = sprintf('%s-%s', $brandName ?: 'Unknown', $brandModel ?: 'unknown');
        $deviceId   = request()->header('X-Device-Id');
        $isFirst    = $event->user->loginLogs()->whereDate('created_at', now())->doesntExist();
        if ($isFirst) {
            $amount = SystemConfig::getValue('score', 'day_login', 0);
            if ($amount > 0) {
                ScoreAddEvent::dispatch($event->user, 'day_login', [
                    'amount'   => $amount,
                    'login_at' => now()->toDateString(),
                    'remark'   => now()->toDateString(), '首次登录',
                ]);
            }
        }
        $event->user->loginLogs()->create([
            'name'      => $name,
            'device_id' => $deviceId,
            'ip'        => request()->ip(),
        ]);
    }
}