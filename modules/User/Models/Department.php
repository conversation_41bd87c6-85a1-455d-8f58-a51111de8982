<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Models\User;
use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use Dcat\Admin\Traits\ModelTree;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Overtrue\Pinyin\Converter;
use Overtrue\Pinyin\Pinyin;
use Spatie\EloquentSortable\Sortable;

class Department extends Model implements Sortable
{
    use Cachable,
        ModelTree,
        SoftDeletes,
        HasCovers,
        HasEasyStatus;

    protected $table = 'user_departments';

    protected $titleColumn = 'name';

    protected $sortable = [
        'order_column_name'  => 'order',
        'sort_when_creating' => true,
    ];

    protected static function boot(): void
    {
        parent::boot();
        self::saving(function ($model) {
            $model->pinyin = Pinyin::abbr($model->name, Converter::TONE_STYLE_NONE)->join('');
        });
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_department')
            ->withPivot(['position'])
            ->withTimestamps();
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(__CLASS__);
    }

    public function children(): HasMany
    {
        return $this->hasMany(__CLASS__, 'parent_id');
    }
}
