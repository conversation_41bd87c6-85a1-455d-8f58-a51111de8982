<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Models\Module;
use App\Models\User;
use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Modules\User\Enums\IdentityChannel;
use Modules\User\Notifications\IdentityChangedNotification;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class Identity extends Model implements Sortable
{
    use Cachable,
        HasCovers,
        SoftDeletes,
        SortableTrait,
        HasEasyStatus;

    protected $table = 'user_identities';

    protected $casts = [
        'is_default'    => 'bool',
        'is_unique'     => 'bool',
        'can_subscribe' => 'bool',
        'conditions'    => 'json',
        'rules'         => 'json',
    ];

    protected array $sortable = [
        'order_column_name'  => 'order',
        'sort_when_creating' => true,
    ];

    protected static function boot(): void
    {
        parent::boot();

        self::saved(function ($model) {
            if ($model->is_default) {
                Identity::where('id', '<>', $model->id)->where('is_default', true)->update(['is_default' => false]);
            }
        });
    }

    public function getConditions(string $key, string $default = '')
    {
        $conditions = collect($this->conditions)->where('key', $key)->first();
        return blank($conditions) ? $default : $conditions['value'];
    }

    public function getRules(string $key, string $default = '')
    {
        $rules = collect($this->rules)->where('key', $key)->first();
        return blank($rules) ? $default : $rules['value'];
    }

    /**
     * Notes   : 当前身份对应的用户
     *
     * @Date   : 2023/1/3 09:49
     * <AUTHOR> <Jason.C>
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_identity')
            ->withPivot(['started_at', 'ended_at', 'serial'])
            ->using(UserIdentity::class)
            ->withTimestamps();
    }

    /**
     * Notes   : 加入身份，差个身份变更记录，
     *
     * @Date   : 2023/1/2 21:10
     * <AUTHOR> <Jason.C>
     * @param  \App\Models\User  $user
     * @param  \Modules\User\Enums\IdentityChannel  $channel  渠道
     * @param  int  $qty  周期
     * @param  array  $source
     */
    public function entry(
        User $user,
        IdentityChannel $channel = IdentityChannel::AUTO,
        int $qty = 1,
        array $source = []
    ): void {
        $pivot = UserIdentity::where('user_id', $user->getKey())->where('identity_id', $this->getKey())->first();

        $data['ended_at'] = match (true) {
            $pivot && $this->days => $this->parseEndedAtTime(Carbon::parse($pivot->ended_at)
                ->addDays($this->days * $qty)),
            ! $pivot && $this->days => $this->parseEndedAtTime(now()->addDays($this->days * $qty)),
            default => null
        };

        $data['serial'] = $pivot ? $pivot->serial : UserIdentity::getNewestSerialNo($this);
        ! $pivot && $data['started_at'] = now();

        $before = null;
        if (! config('user.CAN_HAS_MANY_IDENTITY')) {
            $before = $user->identities()->first();
            $user->identities()->syncWithPivotValues([$this->getKey()], $data);
        } elseif ($pivot) {
            $user->identities()->updateExistingPivot($this->getKey(), $data);
        } else {
            $user->identities()->attach($this->getKey(), $data);
        }
        $this->generateIdentityLog($user, $before, $channel, $source);
    }

    /**
     * Notes   : 到期时间的格式化，防止超过 9999-12-31
     *
     * @Date   : 2023/1/3 10:39
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Support\Carbon  $endedAT
     * @return \Illuminate\Support\Carbon
     */
    private function parseEndedAtTime(Carbon $endedAT): Carbon
    {
        $maxDate = Carbon::create(9999, 12, 31, 23, 59, 59);
        if ($endedAT->greaterThan($maxDate)) {
            return $maxDate;
        }
        return $endedAT;
    }

    /**
     * Notes   : 记录身份变更日志
     *
     * @Date   : 2023/1/4 16:35
     * <AUTHOR> <Jason.C>
     * @param  \App\Models\User  $user
     * @param  \Modules\User\Models\Identity|null  $before
     * @param  \Modules\User\Enums\IdentityChannel  $channel
     * @param  array  $source
     */
    private function generateIdentityLog(
        User $user,
        ?Identity $before,
        IdentityChannel $channel = IdentityChannel::AUTO,
        array $source = []
    ): void {
        IdentityLog::create([
            'user'    => $user,
            'before'  => $before?->getKey() ?? 0,
            'after'   => $this->id,
            'channel' => $channel,
            'source'  => $source,
        ]);
        if (Module::isEnabled('Notification')) {
            $user->notify(new IdentityChangedNotification($before, $this));
        }
    }

    /**
     * Notes   : 身份订阅的订单
     *
     * @Date   : 2023/8/7 14:53
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orders(): HasMany
    {
        return $this->hasMany(IdentityOrder::class);
    }
}
