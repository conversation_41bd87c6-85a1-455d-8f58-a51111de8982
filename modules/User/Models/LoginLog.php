<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Casts\Attribute;

class LoginLog extends Model
{
    use BelongsToUser;

    protected $table = 'user_login_logs';

    public function ip(): Attribute
    {
        return new Attribute(
            get: fn($ip) => long2ip($ip),
            set: fn($ip) => ip2long($ip)
        );
    }
}
