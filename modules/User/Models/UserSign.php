<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Models\User;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

class UserSign extends Model
{
    use BelongsToUser;

    public    $timestamps = false;
    protected $casts      = [
        'sign_at' => 'datetime',
    ];

    public function info()
    {
        return $this->belongsTo(UserInfo::class, 'user_id', 'user_id');
    }

    public function rewards(): HasMany
    {
        return $this->hasMany(UserSignLog::class, 'sign_id');
    }

    public function scopeOfBetween(Builder $query, array $timeBetween): void
    {
        $query->whereBetween('sign_at', $timeBetween);
    }

    public function scopeOfDay(Builder $query, Carbon|string $day): void
    {
        $query->whereDate('sign_at', $day);
    }
}
