<?php

namespace Modules\User\Providers;

use App\Events\UserCreatedEvent;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\User\Events\Authenticated;
use Modules\User\Events\DepartmentApplyPassed;
use Modules\User\Events\DepartmentApplyRejected;
use Modules\User\Events\DepartmentJoinCreated;
use Modules\User\Events\DepartmentJoinPassed;
use Modules\User\Events\DepartmentJoinRejected;
use Modules\User\Listeners\DepartmentApplyPassedListener;
use Modules\User\Listeners\DepartmentApplyRejectedListener;
use Modules\User\Listeners\DepartmentJoinCreatedListener;
use Modules\User\Listeners\DepartmentJoinPassedListener;
use Modules\User\Listeners\DepartmentJoinRejectedListener;
use Modules\User\Listeners\LoginListener;
use Modules\User\Listeners\RecordLoginListener;
use Modules\User\Listeners\UserCreatedListener;
use Modules\User\Models\Relation;
use Modules\User\Observers\RelationObserver;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        Authenticated::class           => [
            RecordLoginListener::class,
            LoginListener::class,
        ],
        UserCreatedEvent::class        => [
            UserCreatedListener::class,
        ],
        DepartmentApplyPassed::class   => [
            DepartmentApplyPassedListener::class,
        ],
        DepartmentApplyRejected::class => [
            DepartmentApplyRejectedListener::class,
        ],
        DepartmentJoinPassed::class    => [
            DepartmentJoinPassedListener::class,
        ],
        DepartmentJoinRejected::class  => [
            DepartmentJoinRejectedListener::class,
        ],
        DepartmentJoinCreated::class   => [
            DepartmentJoinCreatedListener::class,
        ],
    ];

    protected $observers = [
        Relation::class => [
            RelationObserver::class,
        ],
    ];
}