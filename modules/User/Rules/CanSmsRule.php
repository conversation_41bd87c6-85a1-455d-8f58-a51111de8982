<?php

namespace Modules\User\Rules;

use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class CanSmsRule implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        switch ($value) {
            case 'LOGIN':
            case 'FORGOT':
                if (! User::where('username', $this->data['mobile'])->exists()) {
                    $fail('账号不存在');
                }
                break;
            case 'REGISTER':
                if (User::where('username', $this->data['mobile'])->exists()) {
                    $fail('账号已存在');
                }
                break;
            case 'DEFAULT':
                break;
            default:
                $fail('账号已存在');
        }
    }

    public function setData($data): CanSmsRule
    {
        $this->data = $data;

        return $this;
    }
}