<?php

namespace Modules\User\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class IdCardRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (strlen($value) !== 18) {
            $fail('请输入18位身份证号码');
        }

        if (! $this->isIdCard($value)) {
            $fail('身份证号码 校验错误');
        }
    }

    private function isIdCard(string $id): bool
    {
        $id   = strtoupper($id);
        $regx = "/(^\d{17}([0-9]|X)$)/";
        if (! preg_match($regx, $id)) {
            return false;
        }

        $split = [];
        $regx  = "/^(\d{6})+(\d{4})+(\d{2})+(\d{2})+(\d{3})([0-9]|X)$/";
        @preg_match($regx, $id, $split);
        $birth = $split[2].'/'.$split[3].'/'.$split[4];
        if (! strtotime($birth)) {
            return false;
        }

        $factor     = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        $verifyList = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        $sign       = 0;
        for ($i = 0; $i < 17; $i++) {
            $sign += (int) $id[$i] * $factor[$i];
        }
        $mod    = $sign % 11;
        $verify = $verifyList[$mod];

        return $verify === substr($id, -1);
    }
}
