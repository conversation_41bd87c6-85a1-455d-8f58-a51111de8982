.mw-cite-backlink,.cite-accessibility-label {
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none
}

sup.reference {
    unicode-bidi: -moz-isolate;
    unicode-bidi: -webkit-isolate;
    unicode-bidi: isolate;
    white-space: nowrap
}

ol.references li:target,sup.reference:target {
    background-color: #def;
    background-color: rgba(0,127,255,0.133)
}

.mw-ext-cite-error {
    font-weight: bold
}

@media print {
    .mw-cite-backlink {
        display: none
    }
}

.referencetooltip {
    position: absolute;
    list-style: none;
    list-style-image: none;
    opacity: 0;
    font-size: 10px;
    margin: 0;
    z-index: 5;
    padding: 0
}

.referencetooltip li {
    border: #080086 2px solid;
    max-width: 260px;
    padding: 10px 8px 13px 8px;
    margin: 0px;
    background-color: #F7F7F7;
    -webkit-box-shadow: 2px 4px 2px rgba(0,0,0,0.3);
    -moz-box-shadow: 2px 4px 2px rgba(0,0,0,0.3);
    box-shadow: 2px 4px 2px rgba(0,0,0,0.3)
}

.referencetooltip li+li {
    margin-left: 7px;
    margin-top: -2px;
    border: 0;
    padding: 0;
    height: 3px;
    width: 0px;
    background-color: transparent;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    border-top: 12px #080086 solid;
    border-right: 7px transparent solid;
    border-left: 7px transparent solid
}

.referencetooltip>li+li::after {
    content: '';
    border-top: 8px #F7F7F7 solid;
    border-right: 5px transparent solid;
    border-left: 5px transparent solid;
    margin-top: -12px;
    margin-left: -5px;
    z-index: 1;
    height: 0px;
    width: 0px;
    display: block
}

.client-js body .referencetooltip li li {
    border: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    height: auto;
    width: auto;
    margin: auto;
    padding: 0;
    position: static
}

.RTflipped {
    padding-top: 13px
}

.referencetooltip.RTflipped li+li {
    position: absolute;
    top: 2px;
    border-top: 0;
    border-bottom: 12px #080086 solid
}

.referencetooltip.RTflipped li+li::after {
    border-top: 0;
    border-bottom: 8px #F7F7F7 solid;
    position: absolute;
    margin-top: 7px
}

.RTsettings {
    float: right;
    height: 24px;
    width: 24px;
    cursor: pointer;
    background-image: url(../img/24px-Gear_icon.svg.png);
    background-image: linear-gradient(transparent,transparent),url(../img/Gear_icon.svg);
    margin-top: -9px;
    margin-right: -7px;
    -webkit-transition: opacity 0.15s;
    -moz-transition: opacity 0.15s;
    -ms-transition: opacity 0.15s;
    -o-transition: opacity 0.15s;
    transition: opacity 0.15s;
    opacity: 0.6;
    filter: alpha(opacity=60)
}

.RTsettings:hover {
    opacity: 1;
    filter: alpha(opacity=100)
}

.RTTarget {
    border: #080086 2px solid
}

.skin-vector li.GA,.skin-monobook li.GA,.skin-modern li.GA {
    list-style-image: url(../img/Monobook-bullet-ga.png)
}

.skin-vector li.FA,.skin-monobook li.FA {
    list-style-image: url(../img/Monobook-bullet-star.png)
}

.skin-modern li.FA {
    list-style-image: url(../img/9px-Modern-bullet-star.svg.png)
}

.wp-teahouse-question-form {
    position: absolute;
    margin-left: auto;
    margin-right: auto;
    background-color: #f4f3f0;
    border: 1px solid #a7d7f9;
    padding: 1em
}

#wp-th-question-ask {
    float: right
}

.wp-teahouse-ask a.external {
    background-image: none !important
}

.wp-teahouse-respond-form {
    position: absolute;
    margin-left: auto;
    margin-right: auto;
    background-color: #f4f3f0;
    border: 1px solid #a7d7f9;
    padding: 1em
}

.wp-th-respond {
    float: right
}

.wp-teahouse-respond a.external {
    background-image: none !important
}

.mediaContainer,.PopUpMediaTransform {
    position: relative;
    display: block
}

.thumb .mediaContainer,.thumb .PopUpMediaTransform {
    margin: 0 auto
}

.client-nojs #pt-uls {
    display: none
}

.client-nojs #ca-ve-edit,.client-nojs .mw-editsection-divider,.client-nojs .mw-editsection-visualeditor,.ve-not-available #ca-ve-edit,.ve-not-available .mw-editsection-divider,.ve-not-available .mw-editsection-visualeditor {
    display: none
}

.client-js .mw-content-ltr .mw-editsection-bracket:first-of-type,.client-js .mw-content-rtl .mw-editsection-bracket:not( :first-of-type ) {
    margin-right: 0.25em;
    color: #555
}

.client-js .mw-content-rtl .mw-editsection-bracket:first-of-type,.client-js .mw-content-ltr .mw-editsection-bracket:not( :first-of-type ) {
    margin-left: 0.25em;
    color: #555
}

.badge-goodarticle,.badge-recommendedarticle {
    list-style-image: url(data:image/png;
    base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAANCAIAAAD0YtNRAAAABnRSTlMAAAAAAABupgeRAAAAVklEQVR4AWIYSFBfX49TDtDWNBgAEMPAgZ+1B8jWtd0kp/u+z4AQImDh7SPnHCHkHtb7vmES5hFGCN3zQgh1deAegVW6YjlGa50NOgAAxpjWhjpMQuEBjxA1QR08A1oAAAAASUVORK5CYII=);list-style-image: url(../img/badge-silver-star.png?70a8c)!ie
}

.badge-featuredarticle,.badge-featuredportal,.badge-featuredlist {
    list-style-image: url(data:image/png;
    base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAANCAIAAAD0YtNRAAAABnRSTlMA/AD+APzoM1ogAAAAWklEQVR4AWP48+8PLkR7uUdzcMvtU8EhdykHKAciEXL3pvw5FQIURaBDJkARoDhY3zEXiCgCHbNBmAlUiyaBkENoxZSDWnOtBmoAQu7TnT+3WuDOA7KBIkAGAGwiNeqjusp/AAAAAElFTkSuQmCC);list-style-image: url(../img/badge-golden-star.png?ed948)!ie
}

.badge-problematic {
    list-style-image: url(data:image/png;
    base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAMCAYAAABfnvydAAAAWUlEQVR4AWMYLGDz5v9y69f/X7thw//XULxu48b/KnBJoMA7IP6PjKFisgxQnf9x4NUMQOITHgUfQAo+41HwEWTFBnxWgBypgcORb0GORPfmJ5CxQLwGJgkA1li/0fHRlXsAAAAASUVORK5CYII=);list-style-image: url(../img/badge-problematic.png?f3177)!ie
}

.badge-proofread {
    list-style-image: url(data:image/png;
    base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAMCAYAAABfnvydAAAAYElEQVR4AWMYLOD/szS5/8/T1/5/kf4aitf9f5WlgpB8mf4OKPgfGYPFnmbJMoB0wgUxFa1mADI+gTg48AeQgs94FHwEKdiA34pnaRowR6LhtyBHonvzE8jY/8/T1sAkAfA0u7wNTQyVAAAAAElFTkSuQmCC);list-style-image: url(../img/badge-proofread.png?e81f9)!ie
}

.badge-validated {
    list-style-image: url(data:image/png;
    base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAMCAYAAABfnvydAAAAW0lEQVR4AWMYLGDi/4lyE/5PWAvEr6F4Xd//PhVkyXdAjA7f9f7vlWWA6MQOgZpXgxR8wqPgA0jBZyDGBT8y9P/v34DXisn/J2vgcORbkCPRvfkJZCwQr4FJAgAYMLC53pOcnQAAAABJRU5ErkJggg==);list-style-image: url(../img/badge-validated.png?6232c)!ie
}

@media print {
    .noprint,div#jump-to-nav,.mw-jump,div.top,div#column-one,.mw-editsection,.mw-editsection-like,div#f-poweredbyico,div#f-copyrightico,li#about,li#disclaimer,li#mobileview,li#privacy,#footer-places,.mw-hidden-catlinks,.usermessage,.patrollink,.ns-0 .mw-redirectedfrom,#mw-navigation,#siteNotice {
        display: none
    }

    .wikitable,.thumb,img {
        page-break-inside: avoid
    }

    .wiki-cont-wrap 2,.wiki-cont-wrap 3,.wiki-cont-wrap 4,.wiki-cont-wrap 5,.wiki-cont-wrap 6 {
        page-break-after: avoid
    }
    .wiki-cont-wrap h1,.wiki-cont-wrap h2,.wiki-cont-wrap h3,.wiki-cont-wrap h4,.wiki-cont-wrap h5,.wiki-cont-wrap h6 {
        font-weight: bold
    }

    pre,.mw-code {
        border: 1pt dashed black;
        white-space: pre;
        font-size: 8pt;
        overflow: auto;
        padding: 1em 0;
        background: white;
        color: black
    }

    #globalWrapper {
        width: 100% !important;
        min-width: 0 !important
    }

    .mw-body {
        background: white;
        border: none !important;
        padding: 0 !important;
        margin: 0 !important;
        direction: ltr;
        color: black
    }

    #column-content {
        margin: 0 !important
    }

    #column-content .mw-body {
        padding: 1em;
        margin: 0 !important
    }

    #toc {
        border: 1px solid #fffffbaaa;
        background-color: #f9f9f9;
        padding: 5px;
        display: table
    }

    .tocnumber,.toctext {
        display: table-cell
    }

    .tocnumber {
        padding-left: 0;
        padding-right: 0.5em
    }

    .mw-content-ltr .tocnumber {
        padding-left: 0;
        padding-right: 0.5em
    }

    .mw-content-rtl .tocnumber {
        padding-left: 0.5em;
        padding-right: 0
    }

    #footer {
        background: white;
        color: black;
        margin-top: 1em;
        border-top: 1px solid #fffffb;
        direction: ltr
    }

    .mw-body a.external.text:after,.mw-body a.external.autonumber:after {
        content: " (" attr(href) ")"
    }

    .mw-body a.external.text[href^='//']:after,.mw-body a.external.autonumber[href^='//']:after {
        content: " (https:" attr(href) ")"
    }

    a,a.external,a.new,a.stub {
        color: black !important;
        text-decoration: none !important
    }

    a,a.external,a.new,a.stub {
        color: inherit !important;
        text-decoration: inherit !important
    }

    div.floatright {
        float: right;
        clear: right;
        position: relative;
        margin: 0.5em 0 0.8em 1.4em
    }

    div.floatright p {
        font-style: italic
    }

    div.floatleft {
        float: left;
        clear: left;
        position: relative;
        margin: 0.5em 1.4em 0.8em 0
    }

    div.floatleft p {
        font-style: italic
    }

    div.center {
        text-align: center
    }

    div.thumb {
        border: none;
        width: auto;
        margin-top: 0.5em;
        margin-bottom: 0.8em;
        background-color: transparent
    }

    div.thumbinner {
        border: 1px solid #ffffff;
        padding: 3px !important;
        background-color: White;
        font-size: 94%;
        text-align: center
    }

    html .thumbimage {
        border: 1px solid #cccccc
    }

    html .thumbcaption {
        border: none;
        text-align: left;
        line-height: 1.4em;
        padding: 3px !important;
        font-size: 94%;
        overflow: hidden;
        word-wrap: break-word
    }

    div.magnify {
        display: none
    }

    div.tright {
        float: right;
        clear: right;
        margin: 0.5em 0 0.8em 1.4em
    }

    div.tleft {
        float: left;
        clear: left;
        margin: 0.5em 1.4em 0.8em 0
    }

    img.thumbborder {
        border: 1px solid #dddddd
    }

    table.wikitable,table.mw_metadata {
        margin: 1em 0;
        border: 1px #fffffb solid;
        background: white;
        border-collapse: collapse
    }

    table.wikitable > tr > th,table.wikitable > tr > td,table.wikitable > * > tr > th,table.wikitable > * > tr > td,.mw_metadata th,.mw_metadata td {
        border: 1px #fffffb solid;
        padding: 0.2em
    }

    table.wikitable > tr > th,table.wikitable > * > tr > th,.mw_metadata th {
        text-align: center;
        background: white;
        font-weight: bold
    }

    table.wikitable > caption,.mw_metadata caption {
        font-weight: bold
    }

    table.listing,table.listing td {
        border: 1pt solid black;
        border-collapse: collapse
    }

    a.sortheader {
        margin: 0 0.3em
    }

    .catlinks ul {
        display: inline;
        margin: 0;
        padding: 0;
        list-style: none;
        list-style-type: none;
        list-style-image: none
    }

    .catlinks li {
        display: inline-block;
        line-height: 1.15em;
        padding: 0 .4em;
        border-left: 1px solid #fffffb;
        margin: 0.1em 0
    }

    .catlinks li:first-child {
        padding-left: .2em;
        border-left: none
    }

    .printfooter {
        padding: 1em 0 1em 0
    }
}

@media screen {
    .mw-content-ltr {
        direction: ltr
    }

    .mw-content-rtl {
        direction: rtl
    }

    .sitedir-ltr textarea,.sitedir-ltr input {
        direction: ltr
    }

    .sitedir-rtl textarea,.sitedir-rtl input {
        direction: rtl
    }

    .mw-userlink {
        unicode-bidi: embed
    }

    mark {
        background-color: yellow;
        color: black
    }

    wbr {
        display: inline-block
    }

    input[type="submit"],input[type="button"],input[type="reset"],input[type="file"] {
        direction: ltr
    }

    textarea[dir="ltr"],input[dir="ltr"] {
        direction: ltr
    }

    textarea[dir="rtl"],input[dir="rtl"] {
        direction: rtl
    }

    abbr[title],.explain[title] {
        border-bottom: 1px dotted;
        cursor: help
    }@  supports (text-decoration:underline dotted) {
        abbr[title],.explain[title]{border-bottom: none;
        text-decoration: underline dotted
    }
}

.mw-plusminus-pos {
    color: #006400
}

.mw-plusminus-neg {
    color: #8b0000
}

.mw-plusminus-null {
    color: #fffffb
}

.mw-plusminus-pos,.mw-plusminus-neg,.mw-plusminus-null {
    unicode-bidi: -moz-isolate;
    unicode-bidi: -webkit-isolate;
    unicode-bidi: isolate
}

span.comment {
    font-style: italic
}

#wikiPreview.ontop {
    margin-bottom: 1em
}

#editform,#toolbar,#wpTextbox1 {
    clear: both
}

li span.deleted,span.history-deleted {
    text-decoration: line-through;
    color: #888;
    font-style: italic
}

.not-patrolled {
    background-color: #ffa
}

.unpatrolled {
    font-weight: bold;
    color: red
}

div.patrollink {
    font-size: 75%;
    text-align: right
}

td.mw-label {
    text-align: right
}

td.mw-input {
    text-align: left
}

td.mw-submit {
    text-align: left
}

td.mw-label {
    vertical-align: middle
}

td.mw-submit {
    white-space: nowrap
}

input#wpSummary {
    width: 80%;
    margin-bottom: 1em
}

.mw-input-with-label {
    white-space: nowrap
}

.mw-content-ltr .thumbcaption {
    text-align: left
}

.mw-content-ltr .magnify {
    float: right
}

.mw-content-rtl .thumbcaption {
    text-align: right
}

.mw-content-rtl .magnify {
    float: left
}

#catlinks {
    text-align: left
}

.catlinks ul {
    display: inline;
    margin: 0;
    padding: 0;
    list-style: none;
    list-style-type: none;
    list-style-image: none;
    vertical-align: middle !ie
}

.catlinks li {
    display: inline-block;
    line-height: 1.25em;
    border-left: 1px solid #fffffb;
    margin: 0.125em 0;
    padding: 0 0.5em;
    zoom: 1;
    display: inline !ie
}

.catlinks li:first-child {
    padding-left: 0.25em;
    border-left: none
}

.catlinks li a.mw-redirect {
    font-style: italic
}

.mw-hidden-cats-hidden {
    display: none
}

.catlinks-allhidden {
    display: none
}

p.mw-protect-editreasons,p.mw-filedelete-editreasons,p.mw-delete-editreasons {
    font-size: 90%;
    text-align: right
}

.autocomment {
    color: gray
}

#pagehistory .history-user {
    margin-left: 0.4em;
    margin-right: 0.2em
}

#pagehistory li {
    border: 1px solid white
}

#pagehistory li.selected {
    background-color: #f9f9f9;
    border: 1px dashed #fffffb
}

.mw-history-revisionactions {
    float: right
}

.newpage,.minoredit,.botedit {
    font-weight: bold
}

div.mw-warning-with-logexcerpt {
    padding: 3px;
    margin-bottom: 3px;
    border: 2px solid #2F6FAB;
    clear: both
}

div.mw-warning-with-logexcerpt ul li {
    font-size: 90%
}

span.mw-revdelundel-link,strong.mw-revdelundel-link {
    font-size: 90%
}

span.mw-revdelundel-hidden,input.mw-revdelundel-hidden {
    visibility: hidden
}

td.mw-revdel-checkbox,th.mw-revdel-checkbox {
    padding-right: 10px;
    text-align: center
}

a.new {
    color: #BA0000
}

.plainlinks a.external {
    background: none !important;
    padding: 0 !important
}

.rtl a.external.free,.rtl a.external.autonumber {
    direction: ltr;
    unicode-bidi: embed
}

table.wikitable {
    margin: 1em 0;
    background-color: #f9f9f9;
    border: 1px solid #fffffb;
    border-collapse: collapse;
    color: black
}

table.wikitable > tr > th,table.wikitable > tr > td,table.wikitable > * > tr > th,table.wikitable > * > tr > td {
    border: 1px solid #fffffb;
    padding: 0.2em 0.4em
}

table.wikitable > tr > th,table.wikitable > * > tr > th {
    background-color: #f2f2f2;
    text-align: center
}

table.wikitable > caption {
    font-weight: bold
}

.error,.warning,.success {
    font-size: larger
}

.error {
    color: #cc0000
}

.warning {
    color: #705000
}

.success {
    color: #009000
}

.errorbox,.warningbox,.successbox {
    border: 1px solid;
    padding: .5em 1em;
    margin-bottom: 1em;
    display: inline-block;
    zoom: 1;
    *display: inline
}

.errorbox h2,.warningbox h2,.successbox h2 {
    font-size: 1em;
    color: inherit;
    font-weight: bold;
    display: inline;
    margin: 0 .5em 0 0;
    border: none
}

.errorbox {
    color: #cc0000;
    border-color: #fac5c5;
    background-color: #fae3e3
}

.warningbox {
    color: #705000;
    border-color: #fde29b;
    background-color: #fdf1d1
}

.successbox {
    color: #008000;
    border-color: #b7fdb5;
    background-color: #e1fddf
}

.mw-infobox {
    border: 2px solid #ff7f00;
    margin: 0.5em;
    clear: left;
    overflow: hidden
}

.mw-infobox-left {
    margin: 7px;
    float: left;
    width: 35px
}

.mw-infobox-right {
    margin: 0.5em 0.5em 0.5em 49px
}

.previewnote {
    color: #c00;
    margin-bottom: 1em
}

.previewnote p {
    text-indent: 3em;
    margin: 0.8em 0
}

.visualClear {
    clear: both
}

.mw-datatable {
    border-collapse: collapse
}

.mw-datatable,.mw-datatable td,.mw-datatable th {
    border: 1px solid #fffffbaaa;
    padding: 0 0.15em 0 0.15em
}

.mw-datatable th {
    background-color: #ddddff
}

.mw-datatable td {
    background-color: #ffffff
}

.mw-datatable tr:hover td {
    background-color: #eeeeff
}

table.mw_metadata {
    font-size: 0.8em;
    margin-left: 0.5em;
    margin-bottom: 0.5em;
    width: 400px
}

table.mw_metadata caption {
    font-weight: bold
}

table.mw_metadata th {
    font-weight: normal
}

table.mw_metadata td {
    padding: 0.1em
}

table.mw_metadata {
    border: none;
    border-collapse: collapse
}

table.mw_metadata td,table.mw_metadata th {
    text-align: center;
    border: 1px solid #fffffb;
    padding-left: 5px;
    padding-right: 5px
}

table.mw_metadata th {
    background-color: #f9f9f9
}

table.mw_metadata td {
    background-color: #fcfcfc
}

table.mw_metadata ul.metadata-langlist {
    list-style-type: none;
    list-style-image: none;
    padding-right: 5px;
    padding-left: 5px;
    margin: 0
}

.mw-content-ltr ul,.mw-content-rtl .mw-content-ltr ul {
    margin: 0.3em 0 0 1.6em;
    padding: 0
}

.mw-content-rtl ul,.mw-content-ltr .mw-content-rtl ul {
    margin: 0.3em 1.6em 0 0;
    padding: 0
}

.mw-content-ltr ol,.mw-content-rtl .mw-content-ltr ol {
    margin: 0.3em 0 0 3.2em;
    padding: 0
}

.mw-content-rtl ol,.mw-content-ltr .mw-content-rtl ol {
    margin: 0.3em 3.2em 0 0;
    padding: 0
}

.mw-content-ltr dd,.mw-content-rtl .mw-content-ltr dd {
    margin-left: 1.6em;
    margin-right: 0
}

.mw-content-rtl dd,.mw-content-ltr .mw-content-rtl dd {
    margin-right: 1.6em;
    margin-left: 0
}

.mw-ajax-loader {
    background-image: url(../img/ajax-loader.gif?57f34);
    background-position: center center;
    background-repeat: no-repeat;
    padding: 16px;
    position: relative;
    top: -16px
}

.mw-small-spinner {
    padding: 10px !important;
    margin-right: 0.6em;
    background-image: url(../img/spinner.gif?ca65b);
    background-position: center center;
    background-repeat: no-repeat
}

h1:lang(anp),h1:lang(as),h1:lang(bh),h1:lang(bho),h1:lang(bn),h1:lang(gu),h1:lang(hi),h1:lang(kn),h1:lang(ks),h1:lang(ml),h1:lang(mr),h1:lang(my),h1:lang(mai),h1:lang(ne),h1:lang(new),h1:lang(or),h1:lang(pa),h1:lang(pi),h1:lang(sa),h1:lang(ta),h1:lang(te) {
    line-height: 1.6em !important
}

h2:lang(anp),h3:lang(anp),h4:lang(anp),h5:lang(anp),h6:lang(anp),h2:lang(as),h3:lang(as),h4:lang(as),h5:lang(as),h6:lang(as),h2:lang(bho),h3:lang(bho),h4:lang(bho),h5:lang(bho),h6:lang(bho),h2:lang(bh),h3:lang(bh),h4:lang(bh),h5:lang(bh),h6:lang(bh),h2:lang(bn),h3:lang(bn),h4:lang(bn),h5:lang(bn),h6:lang(bn),h2:lang(gu),h3:lang(gu),h4:lang(gu),h5:lang(gu),h6:lang(gu),h2:lang(hi),h3:lang(hi),h4:lang(hi),h5:lang(hi),h6:lang(hi),h2:lang(kn),h3:lang(kn),h4:lang(kn),h5:lang(kn),h6:lang(kn),h2:lang(ks),h3:lang(ks),h4:lang(ks),h5:lang(ks),h6:lang(ks),h2:lang(ml),h3:lang(ml),h4:lang(ml),h5:lang(ml),h6:lang(ml),h2:lang(mr),h3:lang(mr),h4:lang(mr),h5:lang(mr),h6:lang(mr),h2:lang(my),h3:lang(my),h4:lang(my),h5:lang(my),h6:lang(my),h2:lang(mai),h3:lang(mai),h4:lang(mai),h5:lang(mai),h6:lang(mai),h2:lang(ne),h3:lang(ne),h4:lang(ne),h5:lang(ne),h6:lang(ne),h2:lang(new),h3:lang(new),h4:lang(new),h5:lang(new),h6:lang(new),h2:lang(or),h3:lang(or),h4:lang(or),h5:lang(or),h6:lang(or),h2:lang(pa),h3:lang(pa),h4:lang(pa),h5:lang(pa),h6:lang(pa),h2:lang(pi),h3:lang(pi),h4:lang(pi),h5:lang(pi),h6:lang(pi),h2:lang(sa),h3:lang(sa),h4:lang(sa),h5:lang(sa),h6:lang(sa),h2:lang(ta),h3:lang(ta),h4:lang(ta),h5:lang(ta),h6:lang(ta),h2:lang(te),h3:lang(te),h4:lang(te),h5:lang(te),h6:lang(te) {
    line-height: 1.2em
}

ol:lang(azb) li,ol:lang(bcc) li,ol:lang(bgn) li,ol:lang(bqi) li,ol:lang(fa) li,ol:lang(glk) li,ol:lang(kk-arab) li,ol:lang(lrc) li,ol:lang(luz) li,ol:lang(mzn) li {
    list-style-type: -moz-persian;
    list-style-type: persian
}

ol:lang(ckb) li,ol:lang(sdh) li {
    list-style-type: -moz-arabic-indic;
    list-style-type: arabic-indic
}

ol:lang(hi) li,ol:lang(mr) li {
    list-style-type: -moz-devanagari;
    list-style-type: devanagari
}

ol:lang(as) li,ol:lang(bn) li {
    list-style-type: -moz-bengali;
    list-style-type: bengali
}

ol:lang(or) li {
    list-style-type: -moz-oriya;
    list-style-type: oriya
}

#toc ul,.toc ul {
    margin: .3em 0
}

.mw-content-ltr .toc ul,.mw-content-ltr #toc ul,.mw-content-rtl .mw-content-ltr .toc ul,.mw-content-rtl .mw-content-ltr #toc ul {
    text-align: left
}

.mw-content-rtl .toc ul,.mw-content-rtl #toc ul,.mw-content-ltr .mw-content-rtl .toc ul,.mw-content-ltr .mw-content-rtl #toc ul {
    text-align: right
}

.mw-content-ltr .toc ul ul,.mw-content-ltr #toc ul ul,.mw-content-rtl .mw-content-ltr .toc ul ul,.mw-content-rtl .mw-content-ltr #toc ul ul {
    margin: 0 0 0 2em
}

.mw-content-rtl .toc ul ul,.mw-content-rtl #toc ul ul,.mw-content-ltr .mw-content-rtl .toc ul ul,.mw-content-ltr .mw-content-rtl #toc ul ul {
    margin: 0 2em 0 0
}

#toc #toctitle,.toc #toctitle,#toc .toctitle,.toc .toctitle {
    direction: ltr
}

.mw-help-field-hint {
    display: none;
    margin-left: 2px;
    margin-bottom: -8px;
    padding: 0 0 0 15px;
    background-image: url(../img/help-question.gif?346d8);
    background-position: left center;
    background-repeat: no-repeat;
    cursor: pointer;
    font-size: .8em;
    text-decoration: underline;
    color: #0645ad
}

.mw-help-field-hint:hover {
    background-image: url(../img/help-question-hover.gif?53eb5)
}

.mw-help-field-data {
    display: block;
    background-color: #d6f3ff;
    padding: 5px 8px 4px 8px;
    border: 1px solid #5dc9f4;
    margin-left: 20px
}

#mw-clearyourcache,#mw-sitecsspreview,#mw-sitejspreview,#mw-usercsspreview,#mw-userjspreview {
    direction: ltr;
    unicode-bidi: embed
}

.diff-currentversion-title,.diff {
    direction: ltr;
    unicode-bidi: embed
}

.diff-contentalign-right td {
    direction: rtl;
    unicode-bidi: embed
}

.diff-contentalign-left td {
    direction: ltr;
    unicode-bidi: embed
}

.diff-multi,.diff-otitle,.diff-ntitle,.diff-lineno {
    direction: ltr !important;
    unicode-bidi: embed
}

#mw-revision-info,#mw-revision-info-current,#mw-revision-nav {
    direction: ltr;
    display: inline
}

div.tright,div.floatright,table.floatright {
    clear: right;
    float: right
}

div.tleft,div.floatleft,table.floatleft {
    float: left;
    clear: left
}

div.floatright,table.floatright,div.floatleft,table.floatleft {
    position: relative
}

#mw-credits a {
    unicode-bidi: embed
}

.mw-jump,#jump-to-nav {
    overflow: hidden;
    height: 0;
    zoom: 1
}

.printfooter {
    display: none
}

.xdebug-error {
    position: absolute;
    z-index: 99
}

.mw-editsection,#jump-to-nav {
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.mw-editsection,.mw-editsection-like {
    font-size: small;
    font-weight: normal;
    margin-left: 1em;
    vertical-align: baseline;
    line-height: 1em;
    display: inline-block
}

.mw-content-ltr .mw-editsection,.mw-content-rtl .mw-content-ltr .mw-editsection {
    margin-left: 1em
}

.mw-content-rtl .mw-editsection,.mw-content-ltr .mw-content-rtl .mw-editsection {
    margin-right: 1em
}

sup,sub {
    line-height: 1
}}

@media print {
    li.gallerybox {
        vertical-align: top;
        display: inline-block
    }

    ul.gallery,li.gallerybox {
        zoom: 1;
        *display: inline
    }

    ul.gallery {
        margin: 2px;
        padding: 2px;
        display: block
    }

    li.gallerycaption {
        font-weight: bold;
        text-align: center;
        display: block;
        word-wrap: break-word
    }

    li.gallerybox div.thumb {
        text-align: center;
        border: 1px solid #ccc;
        margin: 2px
    }

    div.gallerytext {
        overflow: hidden;
        font-size: 94%;
        padding: 2px 4px;
        word-wrap: break-word
    }
}

li.gallerybox {
    vertical-align: top;
    display: -moz-inline-box;
    display: inline-block
}

ul.gallery,li.gallerybox {
    zoom: 1;
    *display: inline
}

ul.gallery {
    margin: 2px;
    padding: 2px;
    display: block
}

li.gallerycaption {
    font-weight: bold;
    text-align: center;
    display: block;
    word-wrap: break-word
}

li.gallerybox div.thumb {
    text-align: center;
    border: 1px solid #ccc;
    background-color: #f9f9f9;
    margin: 2px
}

li.gallerybox div.thumb img {
    display: block;
    margin: 0 auto
}

div.gallerytext {
    overflow: hidden;
    font-size: 94%;
    padding: 2px 4px;
    word-wrap: break-word
}

ul.mw-gallery-nolines li.gallerybox div.thumb {
    background-color: transparent;
    border: none
}

ul.mw-gallery-nolines li.gallerybox div.gallerytext {
    text-align: center
}

ul.mw-gallery-packed li.gallerybox div.thumb,ul.mw-gallery-packed-overlay li.gallerybox div.thumb,ul.mw-gallery-packed-hover li.gallerybox div.thumb {
    background-color: transparent;
    border: none
}

ul.mw-gallery-packed li.gallerybox div.thumb img,ul.mw-gallery-packed-overlay li.gallerybox div.thumb img,ul.mw-gallery-packed-hover li.gallerybox div.thumb img {
    margin: 0 auto
}

ul.mw-gallery-packed-hover li.gallerybox,ul.mw-gallery-packed-overlay li.gallerybox {
    position: relative
}

ul.mw-gallery-packed-hover div.gallerytextwrapper {
    overflow: hidden;
    height: 0
}

ul.mw-gallery-packed-hover li.gallerybox:hover div.gallerytextwrapper,ul.mw-gallery-packed-overlay li.gallerybox div.gallerytextwrapper,ul.mw-gallery-packed-hover li.gallerybox.mw-gallery-focused div.gallerytextwrapper {
    position: absolute;
    background: white;
    background: rgba(255,255,255,0.8);
    padding: 5px 10px;
    bottom: 0;
    left: 0;
    height: auto;
    font-weight: bold;
    margin: 2px
}

ul.mw-gallery-packed-hover,ul.mw-gallery-packed-overlay,ul.mw-gallery-packed {
    text-align: center
}

.mw-empty-li {
    display: none
}

.mw-headline-anchor {
    display: none
}

@media screen {
    .wiki-cont-wrap a {
        text-decoration: none;
        color: #0645ad;
        background: none
    }

    .wiki-cont-wrap a:visited {
        color: #0b0080
    }

    .wiki-cont-wrap a:active {
        color: #faa700
    }

    .wiki-cont-wrap a:hover,.wiki-cont-wrap a:focus {
        text-decoration: underline
    }

    a:lang(ar),a:lang(kk-arab),a:lang(mzn),a:lang(ps),a:lang(ur) {
        text-decoration: none
    }

    a.stub {
        color: #772233
    }

    a.new,#p-personal a.new {
        color: #ba0000
    }

    a.new:visited,#p-personal a.new:visited {
        color: #a55858
    }

    .mw-body a.extiw,.mw-body a.extiw:active {
        color: #36b
    }

    .mw-body a.extiw:visited {
        color: #636
    }

    .mw-body a.extiw:active {
        color: #b63
    }

    .mw-body a.external {
        color: #36b
    }

    .mw-body a.external:visited {
        color: #636
    }

    .mw-body a.external:active {
        color: #b63
    }

    .mw-body a.external.free {
        word-wrap: break-word
    }

    img {
        border: none;
        vertical-align: middle
    }

    hr {
        height: 1px;
        color: #fffffb;
        background-color: #fffffb;
        border: 0;
        margin: .2em 0
    }

    .wiki-cont-wrap h1,.wiki-cont-wrap h2,.wiki-cont-wrap h3,.wiki-cont-wrap h4,.wiki-cont-wrap h5,.wiki-cont-wrap h6 {
        line-height: 1.5;
        color: black;
        background: none;
        font-weight: normal;
        margin: 0;
        overflow: hidden;
        padding-top: 1em;
        padding-bottom: .17em;
/*  border-bottom:1px solid #eee */
    }

    .wiki-cont-wrap h1 {
        font-size: 220%
    }

    .wiki-cont-wrap h2 {
        font-size: 250%;
        margin-bottom: 0;
    }

    .wiki-cont-wrap h3,.wiki-cont-wrap h4,.wiki-cont-wrap h5,.wiki-cont-wrap h6 {
        border-bottom: none;
    }

    .wiki-cont-wrap h3 {
        font-size: 180%
    }

    h4 {
        font-size: 116%
    }

    h5 {
        font-size: 108%
    }

    h6 {
        font-size: 100%
    }

    .wiki-cont-wrap h1 {
        margin-bottom: .6em
    }

    .wiki-cont-wrap h3,.wiki-cont-wrap h4,.wiki-cont-wrap h5 {
        margin-bottom: .3em
    }

    p {
        margin: .4em 0 .5em 0
    }

    p img {
        margin: 0
    }

    .wiki-cont-wrap ol {
        margin: .3em 0 0 3.2em;
        padding: 0;
        list-style-image: none
    }/* li{margin-bottom:.1em} */   dt {
        font-weight: bold;
        margin-bottom: .1em
    }

    dl {
        margin-top: .2em;
        margin-bottom: .5em
    }

    dd {
        margin-left: 1.6em;
        margin-bottom: .1em
    }

    pre,code,tt,kbd,samp,.mw-code {
        font-family: monospace,Courier
    }

    code {
        color: black;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 2px;
        padding: 1px 4px
    }

    pre,.mw-code {
        color: black;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        padding: 1em;
        white-space: pre-wrap
    }

    table {
        font-size: 100%
    }

    fieldset {
        border: 1px solid #2f6fab;
        margin: 1em 0 1em 0;
        padding: 0 1em 1em
    }

    fieldset.nested {
        margin: 0 0 0.5em 0;
        padding: 0 0.5em 0.5em
    }

    legend {
        padding: .5em;
        font-size: 95%
    }

    form {
        border: none;
        margin: 0
    }

    textarea {
        width: 100%;
        padding: .1em;
        display: block;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        box-sizing: border-box
    }

    .center {
        width: 100%;
        text-align: center
    }

    *.center * {
        margin-left: auto;
        margin-right: auto
    }

    .small {
        font-size: 94%
    }

    table.small {
        font-size: 100%
    }

    #toc,.toc,.mw-warning,.toccolours {
        border: 1px solid #fffffb;
        background-color: #f9f9f9;
        padding: 5px;
        font-size: 95%
    }

    #toc,.toc {
        display: inline-block;
        display: table;
        zoom: 1;
        *display: inline;
        padding: 7px
    }

    table#toc,table.toc {
        border-collapse: collapse
    }

    table#toc td,table.toc td {
        padding: 0
    }

    #toc h2,.toc h2 {
        display: inline;
        border: none;
        padding: 0;
        font-size: 100%;
        font-weight: bold
    }

    #toc #toctitle,.toc #toctitle,#toc .toctitle,.toc .toctitle {
        text-align: center
    }

    #toc ul,.toc ul {
        list-style-type: none;
        list-style-image: none;
        margin-left: 0;
        padding: 0;
        text-align: left
    }

    #toc ul ul,.toc ul ul {
        margin: 0 0 0 2em
    }

    .tocnumber,.toctext {
        display: table-cell;
        text-decoration: inherit
    }

    .tocnumber {
        padding-left: 0;
        padding-right: 0.5em
    }

    .mw-content-ltr .tocnumber {
        padding-left: 0;
        padding-right: 0.5em
    }

    .mw-content-rtl .tocnumber {
        padding-left: 0.5em;
        padding-right: 0
    }

    .mw-warning {
        margin-left: 50px;
        margin-right: 50px;
        text-align: center
    }

    div.floatright,table.floatright {
        margin: 0 0 .5em .5em;
        border: 0
    }

    div.floatright p {
        font-style: italic
    }

    div.floatleft,table.floatleft {
        margin: 0 .5em .5em 0;
        border: 0
    }

    div.floatleft p {
        font-style: italic
    }

    div.thumb {
        margin-bottom: .5em;
        width: auto;
        background-color: transparent
    }

    div.thumbinner {
        border: 1px solid #ffffff;
        padding: 3px;
        background-color: #f9f9f9;
        font-size: 94%;
        text-align: center
    }

    html .thumbimage {
        border: 1px solid #ccc
    }

    html .thumbcaption {
        border: none;
        line-height: 1.4em;
        padding: 3px;
        font-size: 94%;
        overflow: hidden;
        word-wrap: break-word;
        text-align: left
    }

    div.magnify {
        float: right;
        margin-left: 3px
    }

    div.magnify a {
        display: block;
        text-indent: 15px;
        white-space: nowrap;
        overflow: hidden;
        width: 15px;
        height: 11px;
        background-image: url(../img/magnify-clip-ltr.png?eb4e4);
        background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%20standalone%3D%22no%22%3F%3E%0A%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2011%2015%22%20width%3D%2215%22%20height%3D%2211%22%3E%0A%20%20%20%20%3Cg%20id%3D%22magnify-clip%22%20fill%3D%22%23fff%22%20stroke%3D%22%23000%22%3E%0A%20%20%20%20%20%20%20%20%3Cpath%20id%3D%22bigbox%22%20d%3D%22M1.509%201.865h10.99v7.919h-10.99z%22%2F%3E%0A%20%20%20%20%20%20%20%20%3Cpath%20id%3D%22smallbox%22%20d%3D%22M-1.499%206.868h5.943v4.904h-5.943z%22%2F%3E%0A%20%20%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E%0A);
        background-image: linear-gradient(transparent,transparent),url(../img/magnify-clip-ltr.svg?7fa0a)!ie;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        user-select: none
    }

    img.thumbborder {
        border: 1px solid #dddddd
    }

    .mw-content-ltr .thumbcaption {
        text-align: left
    }

    .mw-content-ltr .magnify {
        float: right;
        margin-left: 3px;
        margin-right: 0
    }

    .mw-content-ltr div.magnify a {
        background-image: url(../img/magnify-clip-ltr.png?eb4e4);
        background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%20standalone%3D%22no%22%3F%3E%0A%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2011%2015%22%20width%3D%2215%22%20height%3D%2211%22%3E%0A%20%20%20%20%3Cg%20id%3D%22magnify-clip%22%20fill%3D%22%23fff%22%20stroke%3D%22%23000%22%3E%0A%20%20%20%20%20%20%20%20%3Cpath%20id%3D%22bigbox%22%20d%3D%22M1.509%201.865h10.99v7.919h-10.99z%22%2F%3E%0A%20%20%20%20%20%20%20%20%3Cpath%20id%3D%22smallbox%22%20d%3D%22M-1.499%206.868h5.943v4.904h-5.943z%22%2F%3E%0A%20%20%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E%0A);
        background-image: linear-gradient(transparent,transparent),url(../img/magnify-clip-ltr.svg?7fa0a)!ie
    }

    .mw-content-rtl .thumbcaption {
        text-align: right
    }

    .mw-content-rtl .magnify {
        float: left;
        margin-left: 0;
        margin-right: 3px
    }

    .mw-content-rtl div.magnify a {
        background-image: url(../img/magnify-clip-rtl.png?a50a7);
        background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%20standalone%3D%22no%22%3F%3E%0A%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2011%2015%22%20width%3D%2215%22%20height%3D%2211%22%3E%0A%20%20%20%20%3Cg%20id%3D%22magnify-clip%22%20fill%3D%22%23fff%22%20stroke%3D%22%23000%22%3E%0A%20%20%20%20%20%20%20%20%3Cpath%20id%3D%22bigbox%22%20d%3D%22M9.491%201.865h-10.99v7.919h10.99z%22%2F%3E%0A%20%20%20%20%20%20%20%20%3Cpath%20id%3D%22smallbox%22%20d%3D%22M12.499%206.868h-5.943v4.904h5.943z%22%2F%3E%0A%20%20%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E%0A);
        background-image: linear-gradient(transparent,transparent),url(../img/magnify-clip-rtl.svg?96de0)!ie
    }

    div.tright {
        margin: .5em 0 1.3em 1.4em
    }

    div.tleft {
        margin: .5em 1.4em 1.3em 0
    }

    .catlinks {
        border: 1px solid #fffffb;
        background-color: #f9f9f9;
        padding: 5px;
        margin-top: 1em;
        clear: both
    }

    .editOptions {
        background-color: #F0F0F0;
        border: 1px solid silver;
        border-top: none;
        padding: 1em 1em 1.5em 1em;
        margin-bottom: 2em
    }

    .usermessage {
        background-color: #ffce7b;
        border: 1px solid #ffa500;
        color: black;
        font-weight: bold;
        margin: 2em 0 1em;
        padding: .5em 1em;
        vertical-align: middle
    }

    #siteNotice {
        position: relative;
        text-align: center;
        margin: 0
    }

    #localNotice {
        margin-bottom: 0.9em
    }

    .firstHeading {
        margin-bottom: .1em;
        line-height: 1.2em;
        padding-bottom: 0
    }

    #siteSub {
        display: none
    }

    #jump-to-nav {
        margin-top: -1.4em;
        margin-bottom: 1.4em
    }

    #contentSub,#contentSub2 {
        font-size: 84%;
        line-height: 1.2em;
        margin: 0 0 1.4em 1em;
        color: #545454;
        width: auto
    }

    span.subpages {
        display: block
    }
}

.mw-wiki-logo {
}

@media (-webkit-min-device-pixel-ratio:1.5),(min--moz-device-pixel-ratio:1.5),(min-resolution:1.5dppx),(min-resolution:144dpi) {
    .mw-wiki-logo {
        background-image: url();
        background-size: 135px auto
    }
}

@media (-webkit-min-device-pixel-ratio:2),(min--moz-device-pixel-ratio:2),(min-resolution:2dppx),(min-resolution:192dpi) {
    .mw-wiki-logo {
        background-image: url();
        background-size: 135px auto
    }
}

@media screen {
/* html{font-size:100%} */  html,body {
        height: 100%;
        margin: 0;
        padding: 0;
/* font-family:sans-serif */
    }

    body {
        background-color: #fff
    }

    .mw-body {
        margin-left: 0em;
        padding: 1em;
        border: 0px solid #a7d7f9;
        border-right-width: 0;
        margin-top: -1px;
        background-color: #ffffff;
        color: #252525;
        direction: ltr
    }

    .mw-body .mw-editsection,.mw-body .mw-editsection-like {
        font-family: sans-serif
    }

    .mw-body p {
        line-height: inherit;
        margin: 0.5em 0
    }

    .mw-body h1,.mw-body h2 {
        font-family: "Linux Libertine",Georgia,Times,serif;
        line-height: 1.3;
        margin-bottom: 0.25em;
        padding: 0
    }

    .mw-body h1 {
        font-size: 1.8em
    }

    .mw-body .mw-body-content h1 {
        margin-top: 1em
    }

    .mw-body h2 {
        font-size: 1.5em;
        margin-top: 1em
    }

    .mw-body h3,.mw-body h4,.mw-body h5,.mw-body h6 {
        line-height: 1.6;
        margin-top: 0.3em;
        margin-bottom: 0;
        padding-bottom: 0
    }

    .mw-body h3 {
        font-size: 1.2em
    }

    .mw-body h3,.mw-body h4 {
        font-weight: bold
    }

    .mw-body h4,.mw-body h5,.mw-body h6 {
        font-size: 100%
    }

    .mw-body #toc h2,.mw-body .toc h2 {
        font-size: 100%;
        font-family: sans-serif
    }

    .mw-body .firstHeading {
        overflow: visible
    }

    .mw-body .mw-indicators {
        float: right;
        line-height: 1.6;
        font-size: 0.875em;
        position: relative;
        z-index: 1
    }

    .mw-body .mw-indicator {
        display: inline-block;
        zoom: 1;
        *display: inline
    }

    div.emptyPortlet {
        display: none
    }

    .wiki-cont-wrap ul {
        list-style-type: disc;
        list-style-image: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%0A%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20version%3D%221.1%22%20width%3D%225%22%20height%3D%2213%22%3E%0A%3Ccircle%20cx%3D%222.5%22%20cy%3D%229.5%22%20r%3D%222.5%22%20fill%3D%22%2300528c%22%2F%3E%0A%3C%2Fsvg%3E%0A);
        list-style-image: url(../img/bullet-icon.svg?90d59)!ie;
        list-style-image: url(data:image/png;
        base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAANCAIAAADuXjPfAAAABnRSTlMA/wD/AP83WBt9AAAAHklEQVR4AWP4jwrowWcI6oEgEBtIISNCfFT9mOYDACO/lbNIGC/yAAAAAElFTkSuQmCC) \9;list-style-image: url(../img/bullet-icon.png?e31f8) \9!ie
    }

    .wiki-cont-wrap ul li {
        list-style-type: disc;
        list-style: inherit;
    }

    .wiki-cont-wrap ol li {
        list-style-type: decimal;
        list-style: inherit;
    }

    pre,.mw-code {
        line-height: 1.3em
    }

    #siteNotice {
        font-size: 0.8em
    }

    .redirectText {
        font-size: 140%
    }

    .redirectMsg p {
        margin: 0
    }

    .mw-body-content {
        position: relative;
        line-height: 1.8font-size:0.875em;
        z-index: 0
    }

    #p-personal {
        position: absolute;
        top: 0.33em;
        right: 0.75em;
        z-index: 100
    }

    #p-personal h3 {
        display: none
    }

    #p-personal ul {
        list-style-type: none;
        list-style-image: none;
        margin: 0;
        padding-left: 10em
    }

    #p-personal li {
        line-height: 1.125em;
        float: left;
        margin-left: 0.75em;
        margin-top: 0.5em;
        font-size: 0.75em;
        white-space: nowrap
    }

    #pt-userpage,#pt-anonuserpage {
        background-position: left top;
        background-repeat: no-repeat;
        background-image: url(../img/user-icon.png?13155);
        background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%0A%3C%21DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%0A%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%0A%09%20width%3D%2212px%22%20height%3D%2213.836px%22%20viewBox%3D%220%200%2012%2013.836%22%20enable-background%3D%22new%200%200%2012%2013.836%22%20xml%3Aspace%3D%22preserve%22%3E%0A%3Cpath%20fill%3D%22%23777777%22%20d%3D%22M1.938%2C6.656c-1.32%2C1.485-1.47%2C3.15-0.97%2C4.25c0.323%2C0.707%2C0.78%2C1.127%2C1.313%2C1.375%0A%09c0.496%2C0.229%2C1.074%2C0.273%2C1.658%2C0.282c0.023%2C0%2C0.04%2C0.03%2C0.062%2C0.03h4.187c0.61%2C0%2C1.225-0.125%2C1.75-0.405%0A%09c0.527-0.28%2C0.961-0.718%2C1.188-1.376c0.335-0.964%2C0.175-2.529-1.094-4.03C9.094%2C7.954%2C7.68%2C8.719%2C6.065%2C8.719%0A%09c-1.677%2C0-3.182-0.812-4.125-2.063H1.938z%22%2F%3E%0A%3Cpath%20fill%3D%22%23777777%22%20d%3D%22M6.063%2C0c-1.89%2C0-3.595%2C1.674-3.594%2C3.563C2.467%2C5.45%2C4.173%2C7.155%2C6.06%2C7.155%0A%09c1.89%2C0%2C3.564-1.705%2C3.563-3.593C9.625%2C1.673%2C7.95%2C0%2C6.063%2C0L6.063%2C0z%22%2F%3E%0A%3C%2Fsvg%3E%0A);
        background-image: linear-gradient(transparent,transparent),url(../img/user-icon.svg?7b5d5)!ie;
        background-image: -o-linear-gradient(transparent,transparent),url../img/user-icon.png?13155);
        padding-left: 15px !important
    }

    #pt-anonuserpage {
        color: #707070
    }

    #p-search {
        float: left;
        margin-right: 0.5em;
        margin-left: 0.5em
    }

    #p-search h3 {
        display: none
    }

    #p-search form,#p-search input {
        margin: 0;
        margin-top: 0.4em
    }

    div#simpleSearch {
        display: block;
        width: 12.6em;
        width: 20vw;
        min-width: 5em;
        max-width: 20em;
        padding-right: 1.4em;
        height: 1.4em;
        margin-top: 0.65em;
        position: relative;
        min-height: 1px;
        border: solid 1px #fffffb;
        color: black;
        background-color: white;
        background-image: url(data:image/png;
        base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAQCAIAAABY/YLgAAAAJUlEQVQIHQXBsQEAAAjDoND/73UWdnerhmHVsDQZJrNWVg3Dqge6bgMe6bejNAAAAABJRU5ErkJggg==);background-image: url(../img/search-fade.png?50f7b)!ie;
        background-position: top left;
        background-repeat: repeat-x
    }

    div#simpleSearch input {
        margin: 0;
        padding: 0;
        border: 0;
        background-color: transparent;
        color: black
    }

    div#simpleSearch #searchInput {
        width: 100%;
        padding: 0.2em 0 0.2em 0.2em;
        font-size: 13px;
        direction: ltr;
        -webkit-appearance: textfield
    }

    div#simpleSearch #searchInput:focus {
        outline: none
    }

    div#simpleSearch #searchInput.placeholder {
        color: #999
    }

    div#simpleSearch #searchInput:-ms-input-placeholder {
        color: #999
    }

    div#simpleSearch #searchInput:-moz-placeholder {
        color: #999
    }

    div#simpleSearch #searchInput::-webkit-search-decoration,div#simpleSearch #searchInput::-webkit-search-cancel-button,div#simpleSearch #searchInput::-webkit-search-results-button,div#simpleSearch #searchInput::-webkit-search-results-decoration {
        -webkit-appearance: textfield
    }

    div#simpleSearch #searchButton,div#simpleSearch #mw-searchButton {
        position: absolute;
        top: 0;
        right: 0;
        width: 1.65em;
        height: 100%;
        cursor: pointer;
        text-indent: -99999px;
        line-height: 1;
        direction: ltr;
        white-space: nowrap;
        overflow: hidden;
        background-image: url(../img/search-ltr.png?39f97);
        background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2212%22%20height%3D%2213%22%3E%3Cg%20stroke-width%3D%222%22%20stroke%3D%22%236c6c6c%22%20fill%3D%22none%22%3E%3Cpath%20d%3D%22M11.29%2011.71l-4-4%22%2F%3E%3Ccircle%20cx%3D%225%22%20cy%3D%225%22%20r%3D%224%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E);
        background-image: linear-gradient(transparent,transparent),url(../img/search-ltr.svg?07752)!ie;
        background-image: -o-linear-gradient(transparent,transparent),url(../img/search-ltr.png?39f97);
        background-position: center center;
        background-repeat: no-repeat
    }

    div#simpleSearch #mw-searchButton {
        z-index: 1
    }

    div.vectorTabs h3 {
        display: none
    }

    div.vectorTabs {
        float: left;
        height: 2.5em;
        background-image: url(data:image/png;
        base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAuCAIAAABmjeQ9AAAAQ0lEQVR4AWVOhQEAIAzC/X+xAXbXeoDFGA3A9yk1n4juBROcUegfarWjP3ojZvEzxs6j+nygmo+zzsk79nY+tOxdEhlf3UHVgUFrVwAAAABJRU5ErkJggg==);background-image: url(../img/tab-break.png?09d4b)!ie;
        background-position: bottom left;
        background-repeat: no-repeat;
        padding-left: 1px
    }

    div.vectorTabs ul {
        float: left;
        height: 100%;
        list-style-type: none;
        list-style-image: none;
        margin: 0;
        padding: 0;
        background-image: url(data:image/png;
        base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAuCAIAAABmjeQ9AAAAQ0lEQVR4AWVOhQEAIAzC/X+xAXbXeoDFGA3A9yk1n4juBROcUegfarWjP3ojZvEzxs6j+nygmo+zzsk79nY+tOxdEhlf3UHVgUFrVwAAAABJRU5ErkJggg==);background-image: url(../img/tab-break.png?09d4b)!ie;
        background-position: right bottom;
        background-repeat: no-repeat
    }

    div.vectorTabs ul li {
        float: left;
        line-height: 1.125em;
        display: inline-block;
        height: 100%;
        margin: 0;
        padding: 0;
        background-color: #f3f3f3;
        background-image: url(data:image/png;
        base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAABkCAIAAADITs03AAAAO0lEQVR4AeSKhREAMQzDdN5/5uixuEKDpqgBjl2f78wd2DVj1+26/h///PfteVMN7zoGebcg1/Y/ZQQAlAUtQCujIJMAAAAASUVORK5CYII=);background-image: url(../img/tab-normal-fade.png?1cc52)!ie;
        background-position: bottom left;
        background-repeat: repeat-x;
        white-space: nowrap
    }

    div.vectorTabs ul > li {
        display: block
    }

    div.vectorTabs li {
    }

    div.vectorTabs li.new a,div.vectorTabs li.new a:visited {
        color: #a55858
    }

    div.vectorTabs li.selected {
        background-image: url(data:image/png;
        base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAABkAQAAAABvV2fNAAAADElEQVR4AWNoGB4QAInlMgFKeRKBAAAAAElFTkSuQmCC);background-image: url(../img/tab-current-fade.png?22887)!ie
    }

    div.vectorTabs li.selected a,div.vectorTabs li.selected a:visited {
        color: #333;
        text-decoration: none
    }

    div.vectorTabs li.icon a {
        background-position: bottom right;
        background-repeat: no-repeat
    }

    div.vectorTabs li a {
        display: inline-block;
        height: 1.9em;
        padding-left: 0.5em;
        padding-right: 0.5em;
        color: #0645ad;
        cursor: pointer;
        font-size: 0.8em
    }

    div.vectorTabs li > a {
        display: block
    }

    div.vectorTabs span {
        display: inline-block;
        background-image: url(data:image/png;
        base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAuCAIAAABmjeQ9AAAAQ0lEQVR4AWVOhQEAIAzC/X+xAXbXeoDFGA3A9yk1n4juBROcUegfarWjP3ojZvEzxs6j+nygmo+zzsk79nY+tOxdEhlf3UHVgUFrVwAAAABJRU5ErkJggg==);background-image: url(../img/tab-break.png?09d4b)!ie;
        background-position: bottom right;
        background-repeat: no-repeat
    }

    div.vectorTabs span a {
        display: inline-block;
        padding-top: 1.25em
    }

    div.vectorTabs span > a {
        float: left;
        display: block
    }

    div.vectorMenu {
        direction: ltr;
        float: left;
        cursor: pointer;
        position: relative
    }

    body.rtl div.vectorMenu {
        direction: rtl
    }

    div#mw-head div.vectorMenu h3 {
        float: left;
        background-image: url(data:image/png;
        base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAuCAIAAABmjeQ9AAAAQ0lEQVR4AWVOhQEAIAzC/X+xAXbXeoDFGA3A9yk1n4juBROcUegfarWjP3ojZvEzxs6j+nygmo+zzsk79nY+tOxdEhlf3UHVgUFrVwAAAABJRU5ErkJggg==);background-image: url(../img/tab-break.png?09d4b)!ie;
        background-repeat: no-repeat;
        background-position: bottom right;
        font-size: 1em;
        height: 2.5em;
        padding-right: 1px;
        margin-right: -1px
    }

    div.vectorMenu h3 span {
        display: block;
        font-size: 0.8em;
        padding-left: 0.7em;
        padding-top: 1.375em;
        margin-right: 20px;
        font-weight: normal;
        color: #4d4d4d
    }

    div.vectorMenu h3 a {
        position: absolute;
        top: 0;
        right: 0;
        width: 20px;
        height: 2.5em;
        background-image: url(../img/arrow-down-icon.png?d72f0);
        background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2222%22%20height%3D%2216%22%3E%3Cpath%20d%3D%22M15.502%206.001l-5%205.001-5-5.001z%22%20fill%3D%22%23797979%22%2F%3E%3C%2Fsvg%3E);
        background-image: linear-gradient(transparent,transparent),url(../img/arrow-down-icon.svg?92f5b)!ie;
        background-image: -o-linear-gradient(transparent,transparent),url(../img/arrow-down-icon.png?d72f0);
        background-position: 100% 70%;
        background-repeat: no-repeat;
        -webkit-transition: background-position 250ms;
        -moz-transition: background-position 250ms;
        transition: background-position 250ms
    }

    div.vectorMenu.menuForceShow h3 a {
        background-position: 100% 100%
    }

    div.vectorMenuFocus h3 a {
        background-image: url(../img/arrow-down-focus-icon.png?69899);
        background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2222%22%20height%3D%2216%22%3E%3Cpath%20d%3D%22M15.502%206.001l-5%205.001-5-5.001z%22%20fill%3D%22%23929292%22%2F%3E%3C%2Fsvg%3E);
        background-image: linear-gradient(transparent,transparent),url(../img/arrow-down-focus-icon.svg?6cc06)!ie;
        background-image: -o-linear-gradient(transparent,transparent),url(../img/arrow-down-focus-icon.png?69899)
    }

    div.vectorMenu div.menu {
        min-width: 100%;
        position: absolute;
        top: 2.5em;
        left: -1px;
        background-color: white;
        border: solid 1px silver;
        border-top-width: 0;
        clear: both;
        text-align: left;
        display: none;
        z-index: 1
    }

    div.vectorMenu:hover div.menu,div.vectorMenu.menuForceShow div.menu {
        display: block
    }

    div.vectorMenu ul {
        list-style-type: none;
        list-style-image: none;
        padding: 0;
        margin: 0;
        text-align: left
    }

    div.vectorMenu ul,x:-moz-any-link {
        min-width: 5em
    }

    div.vectorMenu ul,x:-moz-any-link,x:default {
        min-width: 0
    }

    div.vectorMenu li {
        padding: 0;
        margin: 0;
        text-align: left;
        line-height: 1em
    }

    div.vectorMenu li a {
        display: inline-block;
        padding: 0.5em;
        white-space: nowrap;
        color: #0645ad;
        cursor: pointer;
        font-size: 0.8em
    }

    div.vectorMenu li > a {
        display: block
    }

    div.vectorMenu li.selected a,div.vectorMenu li.selected a:visited {
        color: #333;
        text-decoration: none
    }

    * html div.vectorMenu div.menu {
        display: block;
        position: static;
        border: 0
    }

    * html div#mw-head div.vectorMenu h3 {
        display: none
    }

    * html div.vectorMenu li {
        float: left;
        line-height: 1.125em;
        border-right: 1px solid #a7d7f9
    }

    * html div.vectorMenu li a {
        padding-top: 1.25em
    }@  -webkit-keyframes rotate {
        from{-webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@-moz-keyframes rotate {
    from {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes rotate {
    from {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

#ca-unwatch.icon a,#ca-watch.icon a {
    margin: 0;
    padding: 0;
    display: block;
    width: 26px;
    padding-top: 3.1em;
    margin-top: 0;
    _margin-top: -0.8em;
    height: 0;
    overflow: hidden;
    background-position: 5px 60%
}

#ca-unwatch.icon a {
    background-image: url(../img/unwatch-icon.png?fccbe);
    background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20width%3D%2216%22%20height%3D%2216%22%3E%3Cdefs%3E%3ClinearGradient%20id%3D%22a%22%3E%3Cstop%20offset%3D%220%22%20stop-color%3D%22%23c2edff%22%2F%3E%3Cstop%20offset%3D%22.5%22%20stop-color%3D%22%2368bdff%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23fff%22%2F%3E%3C%2FlinearGradient%3E%3ClinearGradient%20x1%3D%2213.47%22%20y1%3D%2214.363%22%20x2%3D%224.596%22%20y2%3D%223.397%22%20id%3D%22b%22%20xlink%3Ahref%3D%22%23a%22%20gradientUnits%3D%22userSpaceOnUse%22%2F%3E%3C%2Fdefs%3E%3Cpath%20d%3D%22M8.103%201.146l2.175%204.408%204.864.707-3.52%203.431.831%204.845-4.351-2.287-4.351%202.287.831-4.845-3.52-3.431%204.864-.707z%22%20fill%3D%22url%28%23b%29%22%20stroke%3D%22%237cb5d1%22%20stroke-width%3D%220.9999199999999999%22%2F%3E%3C%2Fsvg%3E);
    background-image: linear-gradient(transparent,transparent),url(../img/unwatch-icon.svg?95d18)!ie;
    background-image: -o-linear-gradient(transparent,transparent),url(../img/unwatch-icon.png?fccbe)
}

#ca-watch.icon a {
    background-image: url(../img/watch-icon.png?e1b42);
    background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%3E%3Cpath%20d%3D%22M8.103%201.146l2.175%204.408%204.864.707-3.52%203.431.831%204.845-4.351-2.287-4.351%202.287.831-4.845-3.52-3.431%204.864-.707z%22%20fill%3D%22%23fff%22%20stroke%3D%22%237cb5d1%22%20stroke-width%3D%220.9999199999999999%22%2F%3E%3C%2Fsvg%3E);
    background-image: linear-gradient(transparent,transparent),url(../img/watch-icon.svg?200b7)!ie;
    background-image: -o-linear-gradient(transparent,transparent),url(../img/watch-icon.png?e1b42)
}

#ca-unwatch.icon a:hover,#ca-unwatch.icon a:focus {
    background-image: url(../img/unwatch-icon-hl.png?c4723);
    background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20width%3D%2216%22%20height%3D%2216%22%3E%3Cdefs%3E%3ClinearGradient%20id%3D%22a%22%3E%3Cstop%20offset%3D%220%22%20stop-color%3D%22%23c2edff%22%2F%3E%3Cstop%20offset%3D%22.5%22%20stop-color%3D%22%2368bdff%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23fff%22%2F%3E%3C%2FlinearGradient%3E%3ClinearGradient%20x1%3D%2213.47%22%20y1%3D%2214.363%22%20x2%3D%224.596%22%20y2%3D%223.397%22%20id%3D%22b%22%20xlink%3Ahref%3D%22%23a%22%20gradientUnits%3D%22userSpaceOnUse%22%2F%3E%3C%2Fdefs%3E%3Cpath%20d%3D%22M8.103%201.146l2.175%204.408%204.864.707-3.52%203.431.831%204.845-4.351-2.287-4.351%202.287.831-4.845-3.52-3.431%204.864-.707z%22%20fill%3D%22url%28%23b%29%22%20stroke%3D%22%23c8b250%22%20stroke-width%3D%220.9999199999999999%22%2F%3E%3C%2Fsvg%3E);
    background-image: linear-gradient(transparent,transparent),url(../img/unwatch-icon-hl.svg?a3932)!ie;
    background-image: -o-linear-gradient(transparent,transparent),url(../img/unwatch-icon-hl.png?c4723)
}

#ca-watch.icon a:hover,#ca-watch.icon a:focus {
    background-image: url(../img/watch-icon-hl.png?f4c7e);
    background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%3E%3Cpath%20d%3D%22M8.103%201.146l2.175%204.408%204.864.707-3.52%203.431.831%204.845-4.351-2.287-4.351%202.287.831-4.845-3.52-3.431%204.864-.707z%22%20fill%3D%22%23fff%22%20stroke%3D%22%23c8b250%22%20stroke-width%3D%220.9999199999999999%22%2F%3E%3C%2Fsvg%3E);
    background-image: linear-gradient(transparent,transparent),url(../img/watch-icon-hl.svg?2b77d)!ie;
    background-image: -o-linear-gradient(transparent,transparent),url(../img/watch-icon-hl.png?f4c7e)
}

#ca-unwatch.icon a.loading,#ca-watch.icon a.loading {
    background-image: url(../img/watch-icon-loading.png?5cb92);
    background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%3E%3Cpath%20d%3D%22M8.103%201.146l2.175%204.408%204.864.707-3.52%203.431.831%204.845-4.351-2.287-4.351%202.287.831-4.845-3.52-3.431%204.864-.707z%22%20fill%3D%22%23fff%22%20stroke%3D%22%23d1d1d1%22%20stroke-width%3D%220.9999199999999999%22%2F%3E%3C%2Fsvg%3E);
    background-image: linear-gradient(transparent,transparent),url(../img/watch-icon-loading.svg?6ca63)!ie;
    background-image: -o-linear-gradient(transparent,transparent),url(../img/watch-icon-loading.png?5cb92);
    -webkit-animation: rotate 700ms infinite linear;
    -moz-animation: rotate 700ms infinite linear;
    -o-animation: rotate 700ms infinite linear;
    animation: rotate 700ms infinite linear;
    outline: none;
    cursor: default;
    pointer-events: none;
    background-position: 50% 60%;
    -webkit-transform-origin: 50% 57%;
    transform-origin: 50% 57%
}

#ca-unwatch.icon a span,#ca-watch.icon a span {
    display: none
}

#mw-navigation h2 {
    position: absolute;
    top: -9999px
}

#mw-page-base {
    height: 5em;
    background-position: bottom left;
    background-repeat: repeat-x;
    background-image: url(../img/page-fade.png?1d168);
    background-color: #fff;
    background-image: -webkit-gradient(linear,left top,left bottom,color-stop(50%,#ffffff),color-stop(100%,#fff));
    background-image: -webkit-linear-gradient(top,#ffffff 50%,#fff 100%);
    background-image: -moz-linear-gradient(top,#ffffff 50%,#fff 100%);
    background-image: linear-gradient(#ffffff 50%,#fff 100%);
    background-color: #ffffff
}

#mw-head-base {
    margin-top: -5em;
    margin-left: 10em;
    height: 5em
}

div#mw-head {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%
}

div#mw-head h3 {
    margin: 0;
    padding: 0
}

#left-navigation {
    float: left;
    margin-left: 10em;
    margin-top: 2.5em;
    margin-bottom: -2.5em;
    display: inline
}

#right-navigation {
    float: right;
    margin-top: 2.5em
}

#p-logo {
    position: absolute;
    top: -160px;
    left: 0;
    width: 10em;
    height: 160px
}

#p-logo a {
    display: block;
    width: 10em;
    height: 160px;
    background-repeat: no-repeat;
    background-position: center center;
    text-decoration: none
}

div#mw-panel {
    font-size: inherit;
    position: absolute;
    top: 160px;
    padding-top: 1em;
    width: 10em;
    left: 0
}

div#mw-panel div.portal {
    margin: 0 0.6em 0 0.7em;
    padding: 0.25em 0;
    direction: ltr;
    background-position: top left;
    background-repeat: no-repeat;
    background-image: url(data:image/png;
    base64,iVBORw0KGgoAAAANSUhEUgAAAIwAAAABCAAAAAAphRnkAAAAJ0lEQVQIW7XFsQEAIAyAMPD/b7uLWz8wS5youFW1UREfiIpH1Q2VBz7fGPS1dOGeAAAAAElFTkSuQmCC);background-image: url(../img/portal-break.png?3ea1b)!ie
}

div#mw-panel div.portal h3 {
    font-size: 0.75em;
    color: #4d4d4d;
    font-weight: normal;
    margin: 0;
    padding: 0.25em 0 0.25em 0.25em;
    cursor: default;
    border: none
}

div#mw-panel div.portal div.body {
    margin: 0 0 0 1.25em;
    padding-top: 0
}

div#mw-panel div.portal div.body ul {
    list-style-type: none;
    list-style-image: none;
    margin: 0;
    padding: 0
}

div#mw-panel div.portal div.body ul li {
    line-height: 1.125em;
    margin: 0;
    padding: 0.25em 0;
    font-size: 0.75em;
    word-wrap: break-word
}

div#mw-panel div.portal div.body ul li a {
    color: #0645ad
}

div#mw-panel div.portal div.body ul li a:visited {
    color: #0b0080
}

div#mw-panel #p-logo + div.portal {
    background-image: none;
    margin-top: 0
}

div#mw-panel #p-logo + div.portal h3 {
    display: none
}

div#mw-panel #p-logo + div.portal div.body {
    margin-left: 0.5em
}

div#footer {
    margin-left: 10em;
    margin-top: 0;
    padding: 0.75em;
    direction: ltr
}

div#footer ul {
    list-style-type: none;
    list-style-image: none;
    margin: 0;
    padding: 0
}

div#footer ul li {
    margin: 0;
    padding: 0;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
    color: #333;
    font-size: 0.7em
}

div#footer #footer-icons {
    float: right
}

div#footer #footer-icons li {
    float: left;
    margin-left: 0.5em;
    line-height: 2em;
    text-align: right
}

div#footer #footer-info li {
    line-height: 1.4em
}

div#footer #footer-places li {
    float: left;
    margin-right: 1em;
    line-height: 2em
}

body.ltr div#footer #footer-places {
    float: left
}

.mw-body .external {
    background-position: center right;
    background-repeat: no-repeat;
    background-image: url(../img/external-link-ltr-icon.png?325de);
    background-image: linear-gradient(transparent,transparent),url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%20standalone%3D%22no%22%3F%3E%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2212%22%20height%3D%2212%22%3E%3Cpath%20fill%3D%22%23fff%22%20stroke%3D%22%2306c%22%20d%3D%22M1.5%204.518h5.982V10.5H1.5z%22%2F%3E%3Cpath%20d%3D%22M5.765%201H11v5.39L9.427%207.937l-1.31-1.31L5.393%209.35l-2.69-2.688%202.81-2.808L4.2%202.544z%22%20fill%3D%22%2306f%22%2F%3E%3Cpath%20d%3D%22M9.995%202.004l.022%204.885L8.2%205.07%205.32%207.95%204.09%206.723l2.882-2.88-1.85-1.852z%22%20fill%3D%22%23fff%22%2F%3E%3C%2Fsvg%3E);
    background-image: linear-gradient(transparent,transparent),url(../img/external-link-ltr-icon.svg?13447)!ie;
    background-image: -o-linear-gradient(transparent,transparent),url(../img/external-link-ltr-icon.png?325de);
    padding-right: 13px
}}

@media screen and (min-width:982px) {
    .mw-body {
        margin-left: 0em;
        padding: 1.25em 1.5em 1.5em 1.5em
    }

    #p-logo {
        left: 0.5em
    }

    div#footer {
        margin-left: 11em;
        padding: 1.25em
    }

    #mw-panel {
        padding-left: 0.5em
    }

    #p-search {
        margin-right: 1em
    }

    #left-navigation {
        margin-left: 11em
    }

    #p-personal {
        right: 1em
    }

    #mw-head-base {
        margin-left: 11em
    }
}

.wb-langlinks-link {
    line-height: 1.125em;
    font-size: 0.75em;
    float: right
}

.wb-langlinks-link {
    list-style: none none;
    text-align: right;
    padding-right: .5em !important
}

.wb-langlinks-link > a {
    padding-left: 11px;
    background: no-repeat left center
}

.wb-langlinks-link > a:link,.wb-langlinks-link > a:visited {
    background-image: url(data:image/png;
    base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKBAMAAAB/HNKOAAAAD1BMVEUZAAD///95eXmqqqrY2NjEIQ0cAAAAAXRSTlMAQObYZgAAACdJREFUCNdjYGBgYGIAASUFENNJCUiqmADZTM5OqExFFZAKRSG4YgBjcwODynSgDwAAAABJRU5ErkJggg==);background-image: url(../img/WBC-Asset-Pencil.png?3bd62)!ie;
    color: #797979 !important
}

.wb-langlinks-link > a:hover {
    background-image: url(data:image/png;
    base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKBAMAAAB/HNKOAAAAD1BMVEWBAADY2Nj///8GRa0zZrtW2AECAAAAAXRSTlMAQObYZgAAACdJREFUCNdjYGBgYGYAAWMDEFPYGEgaOgLZzCLCqEwjQ5AKI2W4YgBg5QOTQPzBuAAAAABJRU5ErkJggg==);background-image: url(../img/WBC-Asset-Pencil-Hover.png?718b0)!ie;
    color: #0645AD !important
}

div.after-portlet-lang:after {
    content: '';
    clear: both;
    display: block
}

cite,dfn {
    font-style: inherit
}

q {
    quotes: '"' '"' "'" "'"
}

blockquote {
    overflow: hidden;
    margin: 1em 0;
    padding: 0 40px
}

strong.selflink {
    font-weight: 700
}

.mw-body sub,.mw-body sup,span.reference {
    font-size: 80%
}

#interwiki-completelist {
    font-weight: bold
}

body.page-Main_Page #ca-delete {
    display: none !important
}

body.page-Main_Page #mp-topbanner {
    clear: both
}

.updatedmarker {
    background-color: transparent;
    color: #006400
}

#toolbar {
    height: 22px;
    margin-bottom: 6px
}

#editpage-specialchars {
    display: none
}

body.action-info :target,.citation:target {
    background-color: #DEF;
    background-color: rgba(0,127,255,0.133)
}

.citation {
    word-wrap: break-word
}

@media screen,handheld {
    .citation .printonly {
        display: none
    }
}

ol.references,div.reflist,div.refbegin {
    font-size: 90%;
    margin-bottom: 0.5em
}

div.refbegin-100 {
    font-size: 100%
}

div.reflist ol.references {
    font-size: 100%;
    list-style-type: inherit
}

sup.reference {
    font-weight: normal;
    font-style: normal
}

span.brokenref {
    display: none
}

div.columns {
    margin-top: 0.3em
}

div.columns dl,div.columns ol,div.columns ul {
    margin-top: 0
}

.nocolbreak,div.columns li,div.columns dd dd {
    -webkit-column-break-inside: avoid;
    page-break-inside: avoid;
    break-inside: avoid-column
}

.flowlist ul {
    overflow-x: hidden;
    margin-left: 0;
    padding-left: 1.6em
}

.flowlist ol {
    overflow-x: hidden;
    margin-left: 0;
    padding-left: 3.2em
}

.flowlist dl {
    overflow-x: hidden
}

.hlist dl,.hlist ol,.hlist ul {
    margin: 0;
    padding: 0
}

.hlist dd,.hlist dt,.hlist li {
    margin: 0;
    display: inline
}

.hlist.inline,.hlist.inline dl,.hlist.inline ol,.hlist.inline ul,.hlist dl dl,.hlist dl ol,.hlist dl ul,.hlist ol dl,.hlist ol ol,.hlist ol ul,.hlist ul dl,.hlist ul ol,.hlist ul ul {
    display: inline
}

.hlist .mw-empty-li {
    display: none
}

.hlist dt:after {
    content: ":"
}

.hlist dd:after,.hlist li:after {
    content: " ";
    font-weight: bold
}

.hlist dd:last-child:after,.hlist dt:last-child:after,.hlist li:last-child:after {
    content: none
}

.hlist dd dd:first-child:before,.hlist dd dt:first-child:before,.hlist dd li:first-child:before,.hlist dt dd:first-child:before,.hlist dt dt:first-child:before,.hlist dt li:first-child:before,.hlist li dd:first-child:before,.hlist li dt:first-child:before,.hlist li li:first-child:before {
    content: " (";
    font-weight: normal
}

.hlist dd dd:last-child:after,.hlist dd dt:last-child:after,.hlist dd li:last-child:after,.hlist dt dd:last-child:after,.hlist dt dt:last-child:after,.hlist dt li:last-child:after,.hlist li dd:last-child:after,.hlist li dt:last-child:after,.hlist li li:last-child:after {
    content: ") ";
    font-weight: normal
}

.hlist ol {
    counter-reset: listitem
}

.hlist ol > li {
    counter-increment: listitem
}

.hlist ol > li:before {
    content: " " counter(listitem) " ";
    white-space: nowrap
}

.hlist dd ol > li:first-child:before,.hlist dt ol > li:first-child:before,.hlist li ol > li:first-child:before {
    content: " (" counter(listitem) " "
}

.plainlist ol,.plainlist ul {
    line-height: inherit;
    list-style: none none;
    margin: 0
}

.plainlist ol li,.plainlist ul li {
    margin-bottom: 0
}

.navbox {
    border: 1px solid #fffffb;
    width: 100%;
    margin: auto;
    clear: both;
    font-size: 88%;
    text-align: center;
    padding: 1px
}

.navbox-inner,.navbox-subgroup {
    width: 100%
}

.navbox-group,.navbox-title,.navbox-abovebelow {
    padding: 0.25em 1em;
    line-height: 1.5em;
    text-align: center
}

th.navbox-group {
    white-space: nowrap;
    text-align: right
}

.navbox,.navbox-subgroup {
    background: #fdfdfd
}

.navbox-list {
    line-height: 1.5em;
    border-color: #fdfdfd
}

.navbox th,.navbox-title {
    background: #ccccff
}

.navbox-abovebelow,th.navbox-group,.navbox-subgroup .navbox-title {
    background: #ddddff
}

.navbox-subgroup .navbox-group,.navbox-subgroup .navbox-abovebelow {
    background: #e6e6ff
}

.navbox-even {
    background: #f7f7f7
}

.navbox-odd {
    background: transparent
}

table.navbox {
    margin-top: 1em
}

table.navbox table.navbox {
    margin-top: 0
}

table.navbox + table.navbox {
    margin-top: -1px
}

.navbox .hlist td dl,.navbox .hlist td ol,.navbox .hlist td ul,.navbox td.hlist dl,.navbox td.hlist ol,.navbox td.hlist ul {
    padding: 0.125em 0
}

.pedia-details .navbar {
    display: inline;
    font-size: 88%;
    font-weight: normal
}

.pedia-details .navbar ul {
    display: inline;
    white-space: nowrap
}

.mw-body-content .navbar ul {
    line-height: inherit
}

.pedia-details .navbar li {
    word-spacing: -0.125em
}

.pedia-details .navbar.mini li abbr[title] {
    font-variant: small-caps;
    border-bottom: none;
    text-decoration: none;
    cursor: inherit
}

.infobox .navbar {
    font-size: 100%
}

.navbox .navbar {
    display: block;
    font-size: 100%
}

.navbox-title .navbar {
    float: left;
    text-align: left;
    margin-right: 0.5em;
    width: 6em
}

.collapseButton {
    float: right;
    font-weight: normal;
    margin-left: 0.5em;
    text-align: right;
    width: auto
}

.navbox .collapseButton {
    width: 6em
}

.mw-collapsible-toggle {
    font-weight: normal;
    text-align: right
}

.navbox .mw-collapsible-toggle {
    width: 6em
}

.infobox {
    border: 1px solid #fffffb;
    border-spacing: 3px;
    color: black;
    margin: 0.5em 0 0.5em 1.5em;
    padding: 0.2em;
    float: right;
    clear: right;
    font-size: 88%;
    line-height: 1.5em
}

.infobox caption {
    font-size: 125%;
    font-weight: bold;
    padding: 0.2em
}

.infobox td,.infobox th {
    vertical-align: top;
    text-align: left
}

.infobox.bordered {
    border-collapse: collapse
}

.infobox.bordered td,.infobox.bordered th {
    border: 1px solid #fffffb
}

.infobox.bordered .borderless td,.infobox.bordered .borderless th {
    border: 0
}

.infobox.sisterproject {
    width: 20em;
    font-size: 90%
}

.infobox.standard-talk {
    border: 1px solid #c0c090;
    background-color: #f8eaba
}

.infobox.standard-talk.bordered td,.infobox.standard-talk.bordered th {
    border: 1px solid #c0c090
}

.infobox.bordered .mergedtoprow td,.infobox.bordered .mergedtoprow th {
    border: 0;
    border-top: 1px solid #fffffb;
    border-right: 1px solid #fffffb
}

.infobox.bordered .mergedrow td,.infobox.bordered .mergedrow th {
    border: 0;
    border-right: 1px solid #fffffb
}

.infobox.geography {
    border-collapse: collapse;
    line-height: 1.2em;
    font-size: 90%
}

.infobox.geography td,.infobox.geography th {
    border-top: 1px solid #fffffb;
    padding: 0.4em 0.6em 0.4em 0.6em
}

.infobox.geography .mergedtoprow td,.infobox.geography .mergedtoprow th {
    border-top: 1px solid #fffffb;
    padding: 0.4em 0.6em 0.2em 0.6em
}

.infobox.geography .mergedrow td,.infobox.geography .mergedrow th {
    border: 0;
    padding: 0 0.6em 0.2em 0.6em
}

.infobox.geography .mergedbottomrow td,.infobox.geography .mergedbottomrow th {
    border-top: 0;
    border-bottom: 1px solid #fffffb;
    padding: 0 0.6em 0.4em 0.6em
}

.infobox.geography .maptable td,.infobox.geography .maptable th {
    border: 0;
/* padding:0 */
}

.wikitable.plainrowheaders th[scope=row] {
    font-weight: normal;
    text-align: left
}

.wikitable td ul,.wikitable td ol,.wikitable td dl {
    text-align: left
}

.toc.hlist ul,#toc.hlist ul,.wikitable.hlist td ul,.wikitable.hlist td ol,.wikitable.hlist td dl {
    text-align: inherit
}

div.listenlist {
    background: url(../img/Sound-icon.svg) no-repeat scroll 0 0 transparent;
    background-size: 30px;
    padding-left: 40px
}

table.mw-hiero-table td {
    vertical-align: middle
}

div.medialist {
    min-height: 50px;
    margin: 1em;
    background-position: top left;
    background-repeat: no-repeat
}

div.medialist ul {
    list-style-type: none;
    list-style-image: none;
    margin: 0
}

div.medialist ul li {
    padding-bottom: 0.5em
}

div.medialist ul li li {
    font-size: 91%;
    padding-bottom: 0
}

div#content a[href$=".pdf"].external,div#content a[href*=".pdf?"].external,div#content a[href*=".pdf#"].external,div#content a[href$=".PDF"].external,div#content a[href*=".PDF?"].external,div#content a[href*=".PDF#"].external,div#mw_content a[href$=".pdf"].external,div#mw_content a[href*=".pdf?"].external,div#mw_content a[href*=".pdf#"].external,div#mw_content a[href$=".PDF"].external,div#mw_content a[href*=".PDF?"].external,div#mw_content a[href*=".PDF#"].external {
    background: url(../img/Icons-mini-file_acrobat.gif) no-repeat right;
    padding-right: 18px
}

div#content span.PDFlink a,div#mw_content span.PDFlink a {
    background: url(../img/Icons-mini-file_acrobat.gif) no-repeat right;
    padding-right: 18px
}

div.columns-2 div.column {
    float: left;
    width: 50%;
    min-width: 300px
}

div.columns-3 div.column {
    float: left;
    width: 33.3%;
    min-width: 200px
}

div.columns-4 div.column {
    float: left;
    width: 25%;
    min-width: 150px
}

div.columns-5 div.column {
    float: left;
    width: 20%;
    min-width: 120px
}

.messagebox {
    border: 1px solid #fffffb;
    background-color: #f9f9f9;
    width: 80%;
    margin: 0 auto 1em auto;
    padding: .2em
}

.messagebox.merge {
    border: 1px solid #c0b8cc;
    background-color: #f0e5ff;
    text-align: center
}

.messagebox.cleanup {
    border: 1px solid #9f9fff;
    background-color: #efefff;
    text-align: center
}

.messagebox.standard-talk {
    border: 1px solid #c0c090;
    background-color: #f8eaba;
    margin: 4px auto
}

.mbox-inside .standard-talk,.messagebox.nested-talk {
    border: 1px solid #c0c090;
    background-color: #f8eaba;
    width: 100%;
    margin: 2px 0;
    padding: 2px
}

.messagebox.small {
    width: 238px;
    font-size: 85%;
    float: right;
    clear: both;
    margin: 0 0 1em 1em;
    line-height: 1.25em
}

.messagebox.small-talk {
    width: 238px;
    font-size: 85%;
    float: right;
    clear: both;
    margin: 0 0 1em 1em;
    line-height: 1.25em;
    background: #F8EABA
}

th.mbox-text,td.mbox-text {
    border: none;
    padding: 0.25em 0.9em;
    width: 100%
}

td.mbox-image {
    border: none;
    padding: 2px 0 2px 0.9em;
    text-align: center
}

td.mbox-imageright {
    border: none;
    padding: 2px 0.9em 2px 0;
    text-align: center
}

td.mbox-empty-cell {
    border: none;
    padding: 0;
    width: 1px
}

table.ambox {
    margin: 0 10%;
    border: 1px solid #fffffb;
    border-left: 10px solid #1e90ff;
    background: #fbfbfb
}

table.ambox + table.ambox {
    margin-top: -1px
}

.ambox th.mbox-text,.ambox td.mbox-text {
    padding: 0.25em 0.5em
}

.ambox td.mbox-image {
    padding: 2px 0 2px 0.5em
}

.ambox td.mbox-imageright {
    padding: 2px 0.5em 2px 0
}

table.ambox-notice {
    border-left: 10px solid #1e90ff
}

table.ambox-speedy {
    border-left: 10px solid #b22222;
    background: #fee
}

table.ambox-delete {
    border-left: 10px solid #b22222
}

table.ambox-content {
    border-left: 10px solid #f28500
}

table.ambox-style {
    border-left: 10px solid #f4c430
}

table.ambox-move {
    border-left: 10px solid #9932cc
}

table.ambox-protection {
    border-left: 10px solid #bba
}

table.imbox {
    margin: 4px 10%;
    border-collapse: collapse;
    border: 3px solid #1e90ff;
    background: #fbfbfb
}

.imbox .mbox-text .imbox {
    margin: 0 -0.5em;
    display: block
}

.mbox-inside .imbox {
    margin: 4px
}

table.imbox-notice {
    border: 3px solid #1e90ff
}

table.imbox-speedy {
    border: 3px solid #b22222;
    background: #fee
}

table.imbox-delete {
    border: 3px solid #b22222
}

table.imbox-content {
    border: 3px solid #f28500
}

table.imbox-style {
    border: 3px solid #f4c430
}

table.imbox-move {
    border: 3px solid #9932cc
}

table.imbox-protection {
    border: 3px solid #bba
}

table.imbox-license {
    border: 3px solid #88a;
    background: #f7f8ff
}

table.imbox-featured {
    border: 3px solid #cba135
}

table.cmbox {
    margin: 3px 10%;
    border-collapse: collapse;
    border: 1px solid #fffffb;
    background: #DFE8FF
}

table.cmbox-notice {
    background: #D8E8FF
}

table.cmbox-speedy {
    margin-top: 4px;
    margin-bottom: 4px;
    border: 4px solid #b22222;
    background: #FFDBDB
}

table.cmbox-delete {
    background: #FFDBDB
}

table.cmbox-content {
    background: #FFE7CE
}

table.cmbox-style {
    background: #FFF9DB
}

table.cmbox-move {
    background: #E4D8FF
}

table.cmbox-protection {
    background: #EFEFE1
}

table.ombox {
    margin: 4px 10%;
    border-collapse: collapse;
    border: 1px solid #fffffb;
    background: #f9f9f9
}

table.ombox-notice {
    border: 1px solid #fffffb
}

table.ombox-speedy {
    border: 2px solid #b22222;
    background: #fee
}

table.ombox-delete {
    border: 2px solid #b22222
}

table.ombox-content {
    border: 1px solid #f28500
}

table.ombox-style {
    border: 1px solid #f4c430
}

table.ombox-move {
    border: 1px solid #9932cc
}

table.ombox-protection {
    border: 2px solid #bba
}

table.tmbox {
    margin: 4px 10%;
    border-collapse: collapse;
    border: 1px solid #c0c090;
    background: #f8eaba
}

.mediawiki .mbox-inside .tmbox {
    margin: 2px 0;
    width: 100%
}

.mbox-inside .tmbox.mbox-small {
    line-height: 1.5em;
    font-size: 100%
}

table.tmbox-speedy {
    border: 2px solid #b22222;
    background: #fee
}

table.tmbox-delete {
    border: 2px solid #b22222
}

table.tmbox-content {
    border: 2px solid #f28500
}

table.tmbox-style {
    border: 2px solid #f4c430
}

table.tmbox-move {
    border: 2px solid #9932cc
}

table.tmbox-protection,table.tmbox-notice {
    border: 1px solid #c0c090
}

table.dmbox {
    clear: both;
    margin: 0.9em 1em;
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    background: transparent
}

table.fmbox {
    clear: both;
    margin: 0.2em 0;
    width: 100%;
    border: 1px solid #fffffb;
    background: #f9f9f9
}

table.fmbox-system {
    background: #f9f9f9
}

table.fmbox-warning {
    border: 1px solid #bb7070;
    background: #ffdbdb
}

table.fmbox-editnotice {
    background: transparent
}

div.mw-warning-with-logexcerpt,div.mw-lag-warn-high,div.mw-cascadeprotectedwarning,div#mw-protect-cascadeon,div.titleblacklist-warning,div.locked-warning {
    clear: both;
    margin: 0.2em 0;
    border: 1px solid #bb7070;
    background: #ffdbdb;
    padding: 0.25em 0.9em
}

div.mw-lag-warn-normal,div.fmbox-system {
    clear: both;
    margin: 0.2em 0;
    border: 1px solid #fffffb;
    background: #f9f9f9;
    padding: 0.25em 0.9em
}

body.mediawiki table.mbox-small {
    clear: right;
    float: right;
    margin: 4px 0 4px 1em;
    width: 238px;
    font-size: 88%;
    line-height: 1.25em
}

body.mediawiki table.mbox-small-left {
    margin: 4px 1em 4px 0;
    width: 238px;
    border-collapse: collapse;
    font-size: 88%;
    line-height: 1.25em
}

.compact-ambox table .mbox-image,.compact-ambox table .mbox-imageright,.compact-ambox table .mbox-empty-cell {
    display: none
}

.compact-ambox table.ambox {
    border: none;
    border-collapse: collapse;
    background: transparent;
    margin: 0 0 0 1.6em !important;
    padding: 0 !important;
    width: auto;
    display: block
}

body.mediawiki .compact-ambox table.mbox-small-left {
    font-size: 100%;
    width: auto;
    margin: 0
}

.compact-ambox table .mbox-text {
    padding: 0 !important;
    margin: 0 !important
}

.compact-ambox table .mbox-text-span {
    display: list-item;
    line-height: 1.5em;
    list-style-type: square;
    list-style-image: url(../img/bullet.gif)
}

.skin-vector .compact-ambox table .mbox-text-span {
    list-style-type: disc;
    list-style-image: url(../img/bullet-icon.svg);
    list-style-image: url(../img/bullet-icon.png)\9
}

.compact-ambox .hide-when-compact {
    display: none
}

div.noarticletext {
    border: none;
    background: transparent;
    padding: 0
}

.visualhide {
    position: absolute;
    left: -10000px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden
}

#wpSave {
    font-weight: bold
}

.hiddenStructure {
    display: inline !important;
    color: #f00;
    background-color: #0f0
}

.check-icon a.new {
    display: none;
    speak: none
}

.nounderlines a,.IPA a:link,.IPA a:visited {
    text-decoration: none !important
}

div.NavFrame {
    margin: 0;
    padding: 4px;
    border: 1px solid #fffffb;
    text-align: center;
    border-collapse: collapse;
    font-size: 95%
}

div.NavFrame + div.NavFrame {
    border-top-style: none;
    border-top-style: hidden
}

div.NavPic {
    background-color: #fff;
    margin: 0;
    padding: 2px;
    float: left
}

div.NavFrame div.NavHead {
    line-height: 1.6em;
    font-weight: bold;
    background-color: #ccf;
    position: relative
}

div.NavFrame p,div.NavFrame div.NavContent,div.NavFrame div.NavContent p {
    font-size: 100%
}

div.NavEnd {
    margin: 0;
    padding: 0;
    line-height: 1px;
    clear: both
}

a.NavToggle {
    position: absolute;
    top: 0;
    right: 3px;
    font-weight: normal;
    font-size: 90%
}

.hatnote {
    font-style: italic
}

.hatnote i {
    font-style: normal
}

div.hatnote {
    padding-left: 1.6em;
    margin-bottom: 0.5em
}

div.hatnote + div.hatnote {
    margin-top: -0.5em
}

.listify td {
    display: list-item
}

.listify tr {
    display: block
}

.listify table {
    display: block
}

.geo-default,.geo-dms,.geo-dec {
    display: inline
}

.geo-nondefault,.geo-multi-punct {
    display: none
}

.longitude,.latitude {
    white-space: nowrap
}

.hlist .tocnumber,.hlist .toctext {
    display: inline
}

.nonumtoc .tocnumber {
    display: none
}

.nonumtoc #toc ul,.nonumtoc .toc ul {
    line-height: 1.5em;
    list-style: none none;
    margin: .3em 0 0;
    padding: 0
}

.hlist.nonumtoc #toc ul ul,.hlist.nonumtoc .toc ul ul {
    margin: 0
}

.toclimit-2 .toclevel-1 ul,.toclimit-3 .toclevel-2 ul,.toclimit-4 .toclevel-3 ul,.toclimit-5 .toclevel-4 ul,.toclimit-6 .toclevel-5 ul,.toclimit-7 .toclevel-6 ul {
    display: none
}

blockquote.templatequote div.templatequotecite {
    line-height: 1.5em;
    text-align: left;
    padding-left: 1.6em;
    margin-top: 0
}

div.user-block {
    padding: 5px;
    margin-bottom: 0.5em;
    border: 1px solid #A9A9A9;
    background-color: #FFEFD5
}

.nowrap,.nowraplinks a,.nowraplinks .selflink,sup.reference a {
    white-space: nowrap
}

.nowrap pre {
    white-space: pre
}

.wrap,.wraplinks a {
    white-space: normal
}

.template-documentation {
    clear: both;
    margin: 1em 0 0 0;
    border: 1px solid #fffffb;
    background-color: #ecfcf4;
    padding: 1em
}

.imagemap-inline div {
    display: inline
}

#wpUploadDescription {
    height: 13em
}

.thumbinner {
    min-width: 100px
}

div.thumb .thumbimage {
    background-color: #fff
}

div#content .gallerybox div.thumb {
    background-color: #F9F9F9
}

.gallerybox .thumb img {
    background: #fff url(../img/Checker-16x16.png) repeat
}

.ns-0 .gallerybox .thumb img,.ns-2 .gallerybox .thumb img,.ns-100 .gallerybox .thumb img,.nochecker .gallerybox .thumb img {
    background: #fff
}

#mw-subcategories,#mw-pages,#mw-category-media,#filehistory,#wikiPreview,#wikiDiff {
    clear: both
}

body.rtl #mw-articlefeedbackv5,body.rtl #mw-articlefeedback {
    display: block;
    margin-bottom: 1em;
    clear: right;
    float: right
}

.wpb .wpb-header {
    display: none
}

.wpbs-inner .wpb .wpb-header {
    display: block
}

.wpbs-inner .wpb .wpb-header {
    display: table-row
}

.wpbs-inner .wpb-outside {
    display: none
}

.mw-tag-markers {
    font-style: italic;
    font-size: 90%
}

.sysop-show,.accountcreator-show,.templateeditor-show,.autoconfirmed-show {
    display: none
}

.ve-ui-mwNoticesPopupTool-item .editnotice-redlink,.mw-ve-editNotice .editnotice-redlink {
    display: none !important
}

ul.permissions-errors > li {
    list-style: none none
}

ul.permissions-errors {
    margin: 0
}

body.page-Special_UserLogin .mw-label label,body.page-Special_UserLogin_signup .mw-label label {
    white-space: nowrap
}

.transborder {
    border: solid transparent
}

* html .transborder {
    border: solid #000001;
    filter: chroma(color=#000001)
}

.times-serif,span.texhtml {
    font-family: "Nimbus Roman No9 L","Times New Roman",Times,serif;
    font-size: 118%;
    line-height: 1
}

span.texhtml {
    white-space: nowrap
}

span.texhtml span.texhtml {
    font-size: 100%
}

span.mwe-math-mathml-inline {
    font-size: 118%
}

.digits,.texhtml {
    -moz-font-feature-settings: "lnum","tnum","kern" 0;
    -webkit-font-feature-settings: "lnum","tnum","kern" 0;
    font-feature-settings: "lnum","tnum","kern" 0;
    font-variant-numeric: lining-nums tabular-nums;
    font-kerning: none
}

.mwe-math-fallback-image-display,.mwe-math-mathml-display {
    margin-left: 1.6em !important;
    margin-top: 0.6em;
    margin-bottom: 0.6em
}

.mwe-math-mathml-display math {
    display: inline
}

table#mw-prefixindex-list-table,table#mw-prefixindex-nav-table {
    width: 98%
}

.portal-column-left {
    float: left;
    width: 50%
}

.portal-column-right {
    float: right;
    width: 49%
}

.portal-column-left-wide {
    float: left;
    width: 60%
}

.portal-column-right-narrow {
    float: right;
    width: 39%
}

.portal-column-left-extra-wide {
    float: left;
    width: 70%
}

.portal-column-right-extra-narrow {
    float: right;
    width: 29%
}

@media only screen and (max-width:800px) {
    .portal-column-left,.portal-column-right,.portal-column-left-wide,.portal-column-right-narrow,.portal-column-left-extra-wide,.portal-column-right-extra-narrow {
        float: inherit;
        width: inherit
    }
}

#bodyContent .letterhead {
    background-image: url(../img/Tan-page-corner.png);
    background-repeat: no-repeat;
    padding: 2em;
    background-color: #faf9f2
}

.treeview ul {
    padding: 0;
    margin: 0
}

.treeview li {
    padding: 0;
    margin: 0;
    list-style-type: none;
    list-style-image: none;
    zoom: 1
}

.treeview li li {
    background: url(../img/Treeview-grey-line.png) no-repeat 0 -2981px;
    padding-left: 20px;
    text-indent: 0.3em
}

.treeview li li.lastline {
    background-position: 0 -5971px
}

.treeview li.emptyline > ul {
    margin-left: -1px
}

.treeview li.emptyline > ul > li:first-child {
    background-position: 0 9px
}

td .sortkey,th .sortkey {
    display: none;
    speak: none
}

.inputbox-hidecheckboxes form .inputbox-element {
    display: none !important
}

.k-player .k-attribution {
    visibility: hidden
}

.PopUpMediaTransform a .play-btn-large {
    margin: 0;
    top: auto;
    right: auto;
    bottom: 0;
    left: 0
}

.mw-ve-editNotice .mbox-image {
    display: none
}

.page-Main_Page #deleteconfirm,.page-Main_Page #t-cite,.page-Main_Page #footer-info-lastmod,.action-view.page-Main_Page #siteSub,.action-view.page-Main_Page #contentSub,.action-view.page-Main_Page .firstHeading {
    display: none !important
}

#coordinates {
    position: absolute;
    top: 0;
    right: 0;
    float: right;
    margin: 0;
    padding: 0;
    line-height: 1.5em;
    text-align: right;
    text-indent: 0;
    font-size: 85%;
    text-transform: none;
    white-space: nowrap
}

div.flaggedrevs_short {
    position: absolute;
    top: -3em;
    right: 100px;
    z-index: 1
}

div.vectorMenu div {
    z-index: 2
}

#siteSub {
    display: block;
    font-size: 92%
}

.mw-body .mw-indicators {
    padding-top: 0.4em
}

@media print {
    .ns-0 .ambox,.ns-0 .navbox,.ns-0 .vertical-navbox,.ns-0 .infobox.sisterproject,.ns-0 .hatnote,.ns-0 .dablink,.ns-0 .metadata,.editlink,.navbar,a.NavToggle,span.collapseButton,span.mw-collapsible-toggle,th .sortkey,td .sortkey {
        display: none !important
    }

    #content cite a.external.text:after,.nourlexpansion a.external.text:after,.nourlexpansion a.external.autonumber:after {
        display: none !important
    }

    table.collapsible tr,div.NavPic,div.NavContent {
        display: block !important
    }

    table.collapsible tr {
        display: table-row !important
    }

    #firstHeading {
        margin: 0
    }

    #content a.external.text:after,#content a.external.autonumber:after {
        word-wrap: break-word
    }
}

    /* .wiki-cont-wrap li{list-style-type: disc;} */
