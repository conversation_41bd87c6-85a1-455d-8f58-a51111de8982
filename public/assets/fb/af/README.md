# PHP-Wiki-API

This is a simple class to get short Wikipedia info boxes from a given Keyword.

**Filelist**

- LICENSE	 (LGPL)
- README.md	 (This File)
- example.jpg  (The Example Image on this Page)
- index.htm	 (Start this File to give it a try)
- wiki-image-proxy.php	(Image Proxy for German DSGVO)
- wiki2api.php	(Main API Class)
- wiki2tpl.phtm	 (Main Template)
- wikipedia-logo.svg	(Wikipedia W Logo)
- wikipedia.php  (File that is used by index.htm for the Demo)

**Example**

![example.jpg](https://raw.githubusercontent.com/gaffling/PHP-Wiki-API/master/example.jpg)
