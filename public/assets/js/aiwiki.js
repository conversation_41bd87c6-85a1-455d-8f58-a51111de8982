async function msCopy(content, successText, errorText, contentId) {
    try {
        await navigator.clipboard.writeText(content);
        $.ajax({
            url: '/detail/' + contentId + '/share',
            type: 'GET',
            success: function (data) {
            },
            error: function (error) {
            }
        });
        updateAlert(successText, true);
    } catch (err) {
        updateAlert(errorText, true);
    }
}

$(function () {
    $(".change_language").on('click', function () {
        var key = $(this).data('language');
        $.ajax({
            url: '/ajax/language',
            type: 'GET',
            data: {
                language: key,
            },
            success: function (data) {
                console.log(data);
                if (data.code == 200) {
                    location.reload();
                } else {
                    alert(data.message);
                }
            },
            error: function (error) {
                alert(error.message);
            }
        });
    });

    $('.ajax-get').on('click', function (event) {
        event.preventDefault();

        var target;
        var $this = $(this);
        $this.attr("disabled", "disabled");

        var $tips = $this.attr('tip') || '确认要执行该操作吗?';
        if ($this.hasClass('confirm')) {
            if (!confirm($tips)) {
                return false;
            }
        }
        if ((target = $this.data('href')) || (target = $this.attr('href')) || (target = $this.attr('url'))) {
            $.get(target, function (data) {
                if (data.code == 200) {
                    updateAlert(data.message, true);
                    setTimeout(function () {
                        if ($this.hasClass('no-refresh')) {
                            $this.removeAttr("disabled");
                        } else if (data.url) {
                            location.href = data.url;
                        } else {
                            location.reload();
                        }
                    }, 1500);
                } else {
                    updateAlert(data.message, false);
                    $this.removeAttr('disabled');
                    setTimeout(function () {
                        if (data.url) {
                            location.href = data.url;
                        } else {

                        }
                    }, 1500);
                }
            });
        }
        return false;
    });

// ajax POST 请求
    $(".ajax-post").on('click', function (event) {
        event.preventDefault();
        var $this = $(this);
        $this.attr('disabled', 'disabled');
        var $form = $this.parents('form');
        var $action = $form.attr("action");
        var $tips = $this.attr('tip') || '确认要执行该操作吗?';
        if ($this.hasClass('confirm')) {
            if (!confirm($tips)) {
                return false;
            }
        }
        var query = $form.serialize();
        $.post($action, query, function (data) {
            console.log(data);
            if (data.code == 200) {
                updateAlert(data.message, true);
                setTimeout(function () {
                    if (data.url) {
                        location.href = data.url;
                    } else if ($this.hasClass('no-refresh')) {
                        $this.removeAttr("disabled", "disabled");
                    } else {
                        location.reload();
                    }
                }, 1500);
            } else {
                updateAlert(data.message, false);
                setTimeout(function () {
                    if (data.url) {
                        location.href = data.url;
                    }
                }, 1500);
                $this.removeAttr('disabled');
            }
        });
    });

    $('[data-href]').on('click', function (event) {
        event.preventDefault();
        if ($(this).hasClass('ajax-get') || $(this).hasClass('ajax-post')) {
            return;
        }
        location.href = $(this).data('href');
    });
    /*顶部消息提示*/
    window.updateAlert = function (text, type) {
        var type = type ? 'success' : 'error';
        // alert(type + text);
        GrowlNotification.notify({
            title: '',
            description: text,
            type: type,
            position: 'top-right',
            closeTimeout: 3000
        });
    };

    $(function () {
        $(".menuloadings").click(function () {
            $(this).find("img").show();
        })
    })

});
