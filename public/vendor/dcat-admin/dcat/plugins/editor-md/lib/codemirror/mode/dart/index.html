<!doctype html>

<title>CodeMirror: Dart mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">
<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../clike/clike.js"></script>
<script src="dart.js"></script>
<style>.CodeMirror {border: 1px solid #dee;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Dart</a>
  </ul>
</div>

<article>
<h2>Dart mode</h2>
<form>
<textarea id="code" name="code">
import 'dart:math' show Random;

void main() {
  print(new Die(n: 12).roll());
}

// Define a class.
class Die {
  // Define a class variable.
  static Random shaker = new Random();

  // Define instance variables.
  int sides, value;

  // Define a method using shorthand syntax.
  String toString() => '$value';

  // Define a constructor.
  Die({int n: 6}) {
    if (4 <= n && n <= 20) {
      sides = n;
    } else {
      // Support for errors and exceptions.
      throw new ArgumentError(/* */);
    }
  }

  // Define an instance method.
  int roll() {
    return value = shaker.nextInt(sides) + 1;
  }
}
</textarea>
</form>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
    lineNumbers: true,
    mode: "application/dart"
  });
</script>

</article>
