@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <div class="container fbody">
        <style>
            .mycoin {
                font-size: 40px;
                font-weight: 600;
                margin: 20px 0
            }

            .card {
                background-color: #172b41;
                min-height: 180px;
                border-radius: 10px;
                position: relative
            }

            .card-detail {
                position: absolute;
                bottom: 20px;
                left: 20px;
                color: #92a7b7;
            }

            .cdetail .account_status {
                background-color: rgb(240, 248, 250);
                padding: 5px 10px;
                color: rgb(103, 183, 106);
                border-radius: 5px
            }

            .cdetail .account_toaccount {
                background-color: rgb(240, 248, 250);
                padding: 5px 10px;
                color: rgb(0, 41, 204);
                border-radius: 5px
            }

            .f-card {
                background-color: #f9f9f9;
                min-height: 150px;
                border: 1px solid #f1f1f1;
                padding: 20px;
                border-radius: 5px
            }

            .font12 {
                font-size: 12px;
                color: #999999
            }

            .font20 {
                font-size: 20px
            }

            .lecard {
                background-color: #f9f9f9;
                border-radius: 5px
            }

            .section {
                flex: 1; /* 等同于设置 flex-grow: 1; 表示该元素会占据可用空间的一部分 */
                box-sizing: border-box; /* 防止边框导致宽度计算错误 */
                padding: 10px; /* 内边距 */
            }
        </style>
        @include("layout.clubHeader")
        <div class="row">
            <div class="col-sm-12 col-md-2 mb-10 pr-20">
                @include("users.menu")
            </div>
            <div class="col-sm-12 col-md-10">
                @if(!$user->center_key)
                    <div class="alert alert-warning" role="alert">
                        {{lecho('bangding')}}
                    </div>
                @endif
                <div class="row card-account">
                    <div class="col-md-3">
                        <div class="lecard">
                            <div class="card">
                                <div class="card-detail">
                                    <div style="color:#ffffff">No.{{ $user->id }}</div>
                                    <div style="font-size:12px">{{lecho('WikiGroup Education Card')}}.</div>
                                </div>
                                <div style="position: absolute;right:10px;top:10px">
                                    <svg t="*************" class="icon" viewBox="0 0 1732 1024" version="1.1"
                                         xmlns="http://www.w3.org/2000/svg" p-id="13119" width="24" height="24">
                                        <path d="M861.891597 109.48985l255.999606 18.510741v832.038104l-255.999606-45.528545-319.960123-31.82272 117.523511-696.24016z"
                                              fill="#FF5F00" p-id="13120"></path>
                                        <path d="M665.520207 512c0-163.367133 76.799882-308.77491 196.37139-402.51015A513.417056 513.417056 0 0 0 544.137009 0.000788C260.174369 0.000788 29.932262 229.218897 29.932262 512S260.174369 1023.999212 544.137009 1023.999212c119.965354 0 230.320876-40.959937 317.754588-109.489062A510.345061 510.345061 0 0 1 665.598976 512"
                                              fill="#EB001B" p-id="13121"></path>
                                        <path d="M1677.860803 829.281973v-19.849815h-5.198761l-5.986452 13.627056-5.986453-13.627056h-5.198761v19.849815h3.623379v-14.96613l5.671376 12.918134h3.780917l5.671376-12.996904v14.966131h3.623379z m-33.004257 0v-16.541513h6.695375v-3.308302h-17.014128v3.387072h6.695374v16.462743h3.623379z m49.073156-317.281973c0 282.781103-230.242107 511.999212-514.204748 511.999212a513.338287 513.338287 0 0 1-317.833357-109.489062 510.266292 510.266292 0 0 0 196.450159-402.51015c0-163.288364-76.799882-308.77491-196.450159-402.51015A513.338287 513.338287 0 0 1 1179.724954 0.000788c283.96264 0 514.204747 229.218109 514.204748 511.999212z"
                                              fill="#F79E1B" p-id="13122"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="d-flex">
                                <div class="section text-center" id="toCoin">
                                    <svg t="1732191149438" class="icon cursor" viewBox="0 0 1024 1024" version="1.1"
                                         xmlns="http://www.w3.org/2000/svg" p-id="4277" width="26" height="26">
                                        <path d="M512 1024C229.225412 1024 0 794.774588 0 512S229.225412 0 512 0s512 229.225412 512 512-229.225412 512-512 512z m0-120.470588c216.244706 0 391.529412-175.284706 391.529412-391.529412S728.244706 120.470588 512 120.470588 120.470588 295.755294 120.470588 512s175.284706 391.529412 391.529412 391.529412z m-75.294118-421.647059H256l180.705882-195.764706v75.294118h331.294118v120.470588H436.705882z m150.588236 60.235294h180.705882L587.294118 737.882353v-75.294118H256v-120.470588h331.294118z"
                                              fill="#3481F5" p-id="4278"></path>
                                    </svg>
                                    <div class="cursor">{{lecho('transfer')}}</div>
                                </div>
                                <div class="section text-center">
                                    <svg t="1732191345848" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                         xmlns="http://www.w3.org/2000/svg" p-id="6355" width="26" height="26">
                                        <path d="M932.098 994.668H92.236c-50.764 0-92.063-41.3-92.063-92.063V343.313c0-50.764 41.299-92.063 92.063-92.063h839.862c50.764 0 92.063 41.299 92.063 92.063v132.463c0 18.33-14.859 33.189-33.189 33.189H804.786c-62.856 0-113.993 51.138-113.993 113.993s51.137 113.993 113.993 113.993h186.185c18.33 0 33.189 14.859 33.189 33.189v132.464c0 50.764-41.299 92.064-92.062 92.064zM92.236 317.63c-14.162 0-25.684 11.521-25.684 25.683v559.292c0 14.161 11.521 25.683 25.684 25.683h839.862c14.161 0 25.683-11.521 25.683-25.683v-99.274H804.786c-99.458 0-180.373-80.915-180.373-180.372s80.915-180.372 180.373-180.372H957.78v-99.274c0-14.162-11.521-25.683-25.683-25.683H92.236z"
                                              p-id="6356"></path>
                                        <path d="M885.553 622.959c0 44.705-36.244 80.95-80.95 80.95-44.707 0-80.951-36.245-80.951-80.95 0-44.711 36.244-80.951 80.951-80.951 44.706 0 80.95 36.24 80.95 80.951zM826.692 96.155c5.802-1.686 11.897 1.662 13.584 7.464l35.816 123.183h69.128L904.016 85.087c-11.905-40.95-54.904-64.575-95.855-52.672L139.596 226.802h237.756l449.34-130.647z"
                                              p-id="6357"></path>
                                    </svg>
                                    <div>{{lecho('Withdrawal')}}</div>
                                </div>
                                <div class="section text-center">
                                    <svg t="1732191463166" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                         xmlns="http://www.w3.org/2000/svg" p-id="12059" width="26" height="26">
                                        <path d="M974.04928 512c0 152.30976-88.1664 284.09856-215.9616 347.9552 97.56672-78.2336 161.36192-204.86144 161.36192-347.9552s-63.7952-269.7216-161.36192-347.9552C885.88288 227.90144 974.04928 359.69024 974.04928 512zM885.32992 512c0-214.56896-174.55104-389.12-389.12-389.12s-389.12 174.55104-389.12 389.12 174.55104 389.12 389.12 389.12S885.32992 726.56896 885.32992 512zM782.92992 512c0 158.1056-128.63488 286.72-286.72 286.72-158.12608 0-286.72-128.6144-286.72-286.72s128.59392-286.72 286.72-286.72C654.27456 225.28 782.92992 353.8944 782.92992 512zM526.6432 387.74784c34.28352 0 51.4048 13.7216 51.4048 41.12384l0.24576 6.69696 60.39552 0 0-8.47872c0-36.33152-8.6016-61.15328-25.84576-74.52672-17.2032-13.35296-49.19296-20.04992-96.01024-20.04992l0-35.96288-42.12736 0 0 35.96288c-48.96768 0-82.51392 7.00416-100.59776 20.93056-18.04288 13.98784-27.11552 39.87456-27.11552 77.7216 0 39.05536 8.99072 65.69984 26.99264 79.91296 18.00192 14.21312 51.56864 21.31968 100.72064 21.31968l0 100.20864-9.23648-0.24576c-25.1904 0-41.39008-3.29728-48.72192-9.89184-7.2704-6.59456-10.87488-21.2992-10.87488-44.07296L405.87264 572.0064l-62.44352 0-0.24576 12.5952c0 37.35552 8.92928 63.54944 26.80832 78.62272 17.92 15.07328 48.88576 22.60992 92.91776 22.60992L474.7264 686.08l0 41.3696 42.12736 0L516.85376 686.08l12.88192-0.26624c44.19584 0 75.1616-7.8848 92.91776-23.63392 17.67424-15.7696 26.56256-43.15136 26.56256-82.2272 0-36.29056-8.15104-61.29664-24.39168-74.89536-16.2816-13.63968-47.63648-21.72928-94.04416-24.28928l-13.9264-0.512 0-92.52864L526.6432 387.72736zM474.7264 478.208c-5.79584-0.32768-8.88832-0.49152-9.23648-0.49152-36.84352 0-55.23456-15.31904-55.23456-45.99808 0-29.30688 18.51392-43.95008 55.48032-43.95008l8.99072-0.26624L474.7264 478.208zM516.83328 534.75328c28.32384 0 46.87872 2.99008 55.808 8.97024 8.92928 6.00064 13.35296 18.41152 13.35296 37.2736 0 34.42688-20.0704 51.63008-60.17024 51.63008l-8.99072 0L516.83328 534.75328z"
                                              p-id="12060"></path>
                                    </svg>
                                    <div>{{lecho('Recharge')}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-9 cdetail">
                        <div class="f-card">
                            <div class="d-flex">
                                <div class="section">
                                    <div class="font12">{{lecho('USD balance')}}
                                        <svg t="1732190310731" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                             xmlns="http://www.w3.org/2000/svg" p-id="4285" width="12" height="12">
                                            <path d="M512 1024c-281.6 0-512-230.4-512-512s230.4-512 512-512 512 230.4 512 512-230.4 512-512 512z m0-938.666667c-234.666667 0-426.666667 192-426.666667 426.666667s192 426.666667 426.666667 426.666667 426.666667-192 426.666667-426.666667-192-426.666667-426.666667-426.666667z m0 512c-25.6 0-42.666667-17.066667-42.666667-42.666666v-298.666667c0-25.6 17.066667-42.666667 42.666667-42.666667s42.666667 17.066667 42.666667 42.666667v298.666667c0 21.333333-17.066667 42.666667-42.666667 42.666666z m0 81.066667c25.6 0 46.933333 21.333333 46.933333 46.933333s-21.333333 46.933333-46.933333 46.933334-46.933333-21.333333-46.933333-46.933334 21.333333-46.933333 46.933333-46.933333z"
                                                  fill="#999999" p-id="4286"></path>
                                        </svg>
                                    </div>
                                    <div class="font20">${{$usd['total']}}</div>
                                </div>
                                <div class="section">
                                    <div class="font12">{{lecho('Available in US dollars')}}</div>
                                    <div class="font20">${{$usd['balance']}}</div>
                                </div>
                                <div class="section">
                                    <div class="font12">{{lecho('USD frozen amount')}}</div>
                                    <div class="font20">${{$usd['freeze']}}</div>
                                </div>
                                <div class="section">
                                    <div class="font12">{{lecho('Membership level')}}</div>
                                    <div class="font20">GVIP</div>
                                </div>
                            </div>
                            <div class="mt-10 d-flex">
                                <div class="section">
                                    <div class="font12">{{lecho('RMB balance')}}</div>
                                    <div class="font20">&yen;{{$account['total']}}</div>
                                </div>
                                <div class="section">
                                    <div class="font12">{{lecho('RMB available')}}</div>
                                    <div class="font20">&yen;{{$account['balance']}}</div>
                                </div>
                                <div class="section">
                                    <div class="font12">{{lecho('RMB frozen amount')}}</div>
                                    <div class="font20">&yen;{{$account['freeze']}}</div>
                                </div>
                                <div class="section"></div>
                            </div>
                            <div class="mt-10 d-flex">
                                <div class="section">
                                    <div class="font12">{{lecho('Card label')}}</div>
                                    <div class="font20">{{lecho('default')}}</div>
                                </div>
                                <div class="section">
                                    <div class="font12">{{lecho('Usage scenario')}}</div>
                                    <div class="font20">{{lecho('unlimited')}}</div>
                                </div>
                                <div class="section"></div>
                                <div class="section"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-50">
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link @if(blank(request('sub')) && blank(request('usd'))) active @endif"
                               href="{{route('account.balance')}}">&yen; {{lecho('Income Details')}}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @if(request('sub') && blank(request('usd'))) active @endif"
                               href="{{route('account.balance')}}?sub=1">&yen; {{lecho('Payment details')}}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @if(blank(request('sub')) && request('usd')) active @endif"
                               href="{{route('account.balance')}}?usd=1">$ {{lecho('Income Details')}}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @if(request('sub') && request('usd')) active @endif"
                               href="{{route('account.balance')}}?usd=1&sub=1">$ {{lecho('Payment details')}}</a>
                        </li>
                    </ul>
                </div>
                <div class="tables">
                    <table class="table table-striped table-hover">
                        <thead>
                        <tr>
                            <th scope="col">{{lecho('type')}}</th>
                            <th scope="col">{{lecho('describe')}}</th>
                            <th scope="col">{{lecho('Number Count')}}</th>
                            <th scope="col">{{lecho('state')}}</th>
                            <th scope="col">{{lecho('Expected release time')}}</th>
                            <th scope="col">{{lecho('Time')}}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($logs as $log)
                            <tr>
                                <td>{{$log['source']['class']??''}}</td>
                                <td>{{$log['remark']}}</td>
                                <td>{{$log['amount']}}</td>
                                <td>{{$log['freeze']===0?lecho('normal'):lecho('To be released')}}</td>
                                <td>{{$log['freezed_at']?:lecho('Released')}}</td>
                                <td>{{$log['created_at']}}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
                @if($pages)
                    @include('layout.page',['pages'=>$pages])
                @endif
            </div>
        </div>
    </div>
    <div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
         aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form action="{{route('account.balance.transfer')}}" method="POST">
                    <div class="modal-header">
                        <div>{{lecho('transfer')}}</div>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="type" class="form-label">{{lecho('Transfer out account')}}</label>
                            <select class="form-control" id="type" name="type"
                                    placeholder="{{lecho('Please select the target account')}}">
                                <option value="balance">CNY</option>
                                <option value="usd">USD</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="to_username" class="form-label">{{lecho('Target account or email')}}</label>
                            <input type="text" class="form-control" name="to_username" id="to_username"
                                   placeholder="{{lecho('Target account or email')}}">
                        </div>
                        <div class="mb-3">
                            <label for="amount" class="form-label">{{lecho('Transfer amount')}}</label>
                            <input type="number" class="form-control" name="amount" id="amount" placeholder="0.00">
                        </div>
                        <div class="mb-3">
                            <input type="password" class="form-control" name="password" id="password"
                                   placeholder="{{lecho('Please enter the payment password')}}">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-bs-dismiss="modal">{{lecho('close')}}</button>
                        <button type="button" class="btn btn-primary"
                                id="transferButton">{{lecho('Confirm transfer')}}</button>
                    </div>
                    @csrf
                </form>
            </div>
        </div>
    </div>
@endsection
@section('script')
    <script>
        $(function () {
            $("#toCoin").click(function () {
                $("#staticBackdrop").modal("show")
            })
            var logId = 0;
            var time = 30;
            var token = '{{csrf_token()}}';
            var ingText = "{{lecho('Wait for the result')}}";

            function startResult(button) {
                var resCode = 0;
                if (time % 3 === 0) {
                    $.post("/account/transfer/" + logId + "/result", {
                        _token: token
                    }, function (res) {
                        if (res.code == 200) {
                            if (res.data.code == 1) {
                                updateAlert(res.data.message, true);
                                setTimeout(function () {
                                    location.reload();
                                }, 1500);
                                time = 0;
                                return false;
                            }
                            if (res.data.code == 2) {
                                updateAlert(res.data.message, false);
                                button.removeAttr('disabled');
                                setTimeout(function () {
                                    location.reload();
                                }, 1500);
                                time = 0;
                                return false;
                            }
                            token = res.data.new_token;
                        } else {
                            updateAlert(res.message, false);
                            button.removeAttr('disabled')
                        }
                    })
                }
                time--;
                if (time <= 0) {
                    button.html("{{lecho('Fruitless')}}");
                    return;
                }
                button.html(ingText + "(" + time + ")");
                setTimeout(function () {
                    startResult(button);
                }, 1000);
            }

            $("#transferButton").on('click', function (event) {
                event.preventDefault();
                var $this = $(this);
                $this.attr('disabled', 'disabled');
                var $form = $this.parents('form');
                var $action = $form.attr("action");
                var query = $form.serialize();
                $.post($action, query, function (data) {
                    if (data.code == 200) {
                        updateAlert(data.data.message, true);
                        logId = data.data.log_id;
                        startResult($this);
                    } else {
                        updateAlert(data.message, false);
                        $this.removeAttr('disabled');
                    }
                });
            })
        })
    </script>

@endsection
