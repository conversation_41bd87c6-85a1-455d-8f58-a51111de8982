@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <section class="contact-wrapper single-post-wrapper blog-list-wrapper">
        <div class="container">
            <div class="section-head">
                <h2 class="heading-secondary">{{lecho('Forgot password')}}</h2>
                <span class="c-bredcrumb">
                    <a href="{{route('login')}}"> {{lecho('login')}} </a>
                </span>
            </div>
            <div class="row">
                <div class="col-lg-6 offset-lg-1 login_rd" style="margin:0 auto">
                    <div class="right-content">
                        <div class="contact_us_contact_form contact-form">
                            <form action="{{route('forgot.email')}}">
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" id="sendMailInput" name="send_mail" value=""
                                           placeholder="{{lecho('input_email')}}">
                                    <span class="input-group-text cursor"
                                          id="sendMail">{{lecho('Obtain_verification_code')}}</span>
                                </div>
                                @csrf
                            </form>
                            <form action="{{route('forgotDo')}}" name="contact-form">
                                <input type="hidden" id="checkMail" name="email">
                                <div class="mb-3">
                                    <label for="code"
                                           class="form-label">{{lecho('email_code')}}</label>
                                    <input type="text" class="form-control" name="code"
                                           placeholder="{{lecho('input_email_code')}}">
                                </div>
                                <div class="mb-3">
                                    <label for="new_password"
                                           class="form-label">{{lecho('Please enter new password')}}</label>
                                    <input type="password" class="form-control" id="new_password"
                                           name="new_password"
                                           placeholder="{{lecho('Please enter new password')}}">
                                </div>
                                <div class="mb-3">
                                    <label for="new_password_confirmation"
                                           class="form-label">{{lecho('repassword')}}</label>
                                    <input type="password" class="form-control" id="new_password_confirmation"
                                           name="new_password_confirmation"
                                           placeholder="{{lecho('repassword')}}">
                                </div>
                                @csrf
                                <button class="button button-primary mt-10 mr-10 ajax-post"
                                        type="button">{{lecho('forgot_button')}}</button>
                            </form>
                            <div style="margin-top:20px">
                                <a style="margin-right:20px" href="{{route('register')}}">{{lecho("goto_register")}}</a>
                                <a href="{{route('login')}}">{{lecho("login")}}</a>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('style')
    <style>
        .login_rd input {
            background-color: #ffffff !important;
        }
    </style>
@endsection
@section('script')
    <script>
        $(function () {
            $("#sendMail").on('click', function (event) {
                event.preventDefault();
                var $this = $(this);
                $this.attr('disabled', 'disabled');
                var $form = $this.parents('form');
                var $action = $form.attr("action");
                var query = $form.serialize();
                $.post($action, query, function (data) {
                    console.log(data);
                    if (data.code == 200) {
                        updateAlert(data.data.message, true);
                        $("#sendMailInput").attr('disabled', 'disabled');
                        $("#checkMail").val(data.data.email);
                    } else {
                        updateAlert(data.message, false);
                        setTimeout(function () {
                            if (data.url) {
                                location.href = data.url;
                            }
                        }, 1500);
                        $this.removeAttr('disabled');
                    }
                });
            })
        });
    </script>
@endsection
