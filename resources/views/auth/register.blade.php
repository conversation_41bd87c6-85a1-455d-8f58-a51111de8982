@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <section class="contact-wrapper single-post-wrapper blog-list-wrapper">
        <div class="container">
            <div class="section-head">
                <h2 class="heading-secondary">{{lecho('register')}}</h2>
                <span class="c-bredcrumb">
                    <a href="{{route('login')}}">{{lecho('login')}}</a>
                </span>
            </div>
            <div class="row align-items-center gy-5 gy-lg-0">
                <div class="col-lg-6">
                    <div class="left-content">
                        <h2 class="heading-primary">{{lecho("Unlock_the_treasury_of_wisdom_and_decode_the_key_to_wealth")}}</h2>
                        <p class="body-text mt-20 mb-30">
                            {{lecho('Login_Page_description')}}
                        </p>

                        <div class="contact-profile"></div>
                    </div>
                </div>
                <div class="col-lg-5 offset-lg-1">
                    <div class="right-content login_rd">
                        <div class="contact_us_contact_form contact-form">
                            <form action="{{route('registerPost')}}" name="contact-form">
                                <div class="form-heading heading-2 white mb-10">{{lecho('account')}}</div>
                                <input type="text" class="form-control form-field" name="username"
                                       placeholder=""/>
                                <div class="form-heading heading-2 white mb-10">{{lecho('password')}}</div>
                                <input type="password" class="form-control form-field" name="password"
                                       placeholder=""/>
                                <div class="form-heading heading-2 white mb-10">{{lecho('repassword')}}</div>
                                <input type="password" class="form-control form-field" name="password_confirmation"
                                       placeholder=""/>
                                @csrf
                                <input type="hidden" name="callback" value="{{request('callback','')}}">
                                <input type="hidden" id="shareCode" name="code" value="{{$code}}">
                                <button class="button button-primary mt-10 ajax-post"
                                        type="button">{{lecho('register_button')}}</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('script')
    <script>
        var newcode = '{{$code}}';
        if (newcode) {
            localStorage.setItem('share_code', newcode);
        } else {
            var code = localStorage.getItem('share_code');
            $("#shareCode").val(code);
        }
    </script>
@endsection

@section('style')
    <style>
        .login_rd input {
            background-color: #ffffff !important;
        }
    </style>
@endsection
