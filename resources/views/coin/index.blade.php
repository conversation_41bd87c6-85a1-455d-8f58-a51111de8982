@extends('layout.base')

@section('content')
    <div class="container fbody">
        <style>
            .mycoin {
                font-size: 40px;
                font-weight: 600;
                margin: 20px 0
            }
        </style>
        @include("layout.clubHeader") <div class="row">
        <div class="col-sm-12 col-md-2 mb-10 pr-20">
            @include("users.menu")
        </div>
        <div class="col-sm-12 col-md-10">
                <div class="flex" style="align-items: center;justify-content: space-between">
                    <div>
                        <div>我的BitToken</div>
                        <div class="mycoin text-bg-warning">12564.00</div>
                    </div>
                    <div>
                        <button type="button" class="btn btn-primary" id="toCoin">转账</button>
                    </div>
                </div>
                <div class="mt-50">
                    <ul class="nav nav-tabs">
                        <li class="nav-item"><a class="nav-link active" aria-current="page" href="#">{{lecho('Income Details')}}</a></li>
                        <li class="nav-item"><a class="nav-link" href="#">{{lecho('Payment details')}}</a></li>
                    </ul>
                </div>
                <div class="tables">
                    <table class="table table-striped table-hover">
                        <thead>
                        <tr>
                            <th scope="col">{{lecho('Detail type')}}</th>
                            <th scope="col">{{lecho('describe')}}</th>
                            <th scope="col">{{lecho('leave a message')}}</th>
                            <th scope="col">{{lecho('BitToken')}}</th>
                            <th scope="col">{{lecho('Time')}}</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>{{lecho('Platform recharge')}}</td>
                            <td>{{str_replace('{count}',2,lecho('Recharge 2 BitTokens on the platform'))}}</td>
                            <td>Otto</td>
                            <td>21</td>
                            <td>2024-10-07 21:19</td>
                        </tr>
                        <tr>
                            <td>平台充值</td>
                            <td>平台充值2个BitToken</td>
                            <td>Otto</td>
                            <td>1</td>
                            <td>2024-10-07 21:19</td>
                        </tr>
                        <tr>
                            <td>平台充值</td>
                            <td>平台充值2个BitToken</td>
                            <td>Otto</td>
                            <td>231</td>
                            <td>2024-10-07 21:19</td>
                        </tr>
                        <tr>
                            <td>平台充值</td>
                            <td>平台充值2个BitToken</td>
                            <td>Otto</td>
                            <td>123</td>
                            <td>2024-10-07 21:19</td>
                        </tr>

                        </tbody>

                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(function () {
            $("#toCoin").click(function () {
                $("#staticBackdrop").modal("show")
            })
        })
    </script>

@endsection
