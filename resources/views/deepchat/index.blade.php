@extends('layout.base')

@section('style')
    <link rel="stylesheet" href="{{asset('assets/css/deepseek.css')}}?v=1.0"/>
@endsection

@section('content')
    <style>
        .sp_text {
            font-size: 0.8rem;
            background-color: unset;
            color: #999;
            margin-bottom: 10px;
            border-left: 2px solid #f1f1f1;
            padding-left: 10px;
            text-indent: 2em;
        }

        .msg_hiden {
            display: none;
        }

        .msg_text {
            color: #000;
            font-size: 1rem;
            text-indent: 2em;
            line-height: 180%;
            margin-bottom: 20px;
        }

        .upbotton {
            position: absolute;
            bottom: 0;
            width: 85%;
            background-color: #faf9fd;
            padding-top: 10px
        }

        .message-day {
            height: calc(100vh - 100px);
            overflow-y: auto;
            margin-bottom: 20px;
        }
    </style>
    <div class="container">
        <div class="chat-body">
            <div style="max-height:calc(100vh - 100px);">
                <div class="chat-content p-2">
                    <!-- Message Day Start -->
                    <div class="message-day" id="message-day">
                        <!-- Received Message Start -->
                        <div class="c-init" style="padding-top:10%;text-align: center;color:#ccc">Please enter the
                            question you want to ask in the dialog box below
                        </div>
                    </div>
                    <div class="chat-finished" id="chat-finished"></div>
                    <div class="container sticky-bottom upbotton">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="deepseek-text"
                                   placeholder="Please input keyword" onkeyup="if(event.keyCode==13){sendPrompt()}">
                            <button class="btn btn-outline-secondary" type="button" onclick="sendPrompt()"
                                    id="deepseek-btn">Send
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
@section('script')
    <script src="{{asset('assets/js/marked.min.js')}}"></script>
    <script>
        let groupId = '';
        const messageBody = $('#message-day');
        let itemId = 0;
        
        async function sendPrompt() {
            const text = $('#deepseek-text').val();
            if (text === '' || text === null || text === undefined) {
                updateAlert('Please enter the conversation content');
                return false;
            }
            $(".c-init").hide();
            itemId++;
            messageBody.append(`<div class="message self">
                            <div class="message-wrapper">
                                <div class="message-content">` + text + `</div>
                            </div>
                        </div>`)
            messageBody.append(`<div class="message">
                            <div class="message-wrapper">
                                <div class="message-content">
                                    <h6 class="text-dark">DeepChat</h6>
                                    <div class="spinner-border" role="status">
                                      <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div id="sp_` + itemId + `" class="small sp_text"></div>
                                    <div id="msg_` + itemId + `" class="msg_text"></div>
                                    <div id="dis_` + itemId + `" class="msg_hiden"></div>
                                </div>
                            </div>
                        </div>`)
            $('#deepseek-text').val('');
            const response = await fetch('/chat/deepseek', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    _token: '{{csrf_token()}}',
                    message: text,
                    group_id: groupId
                })
            });

            const reader = response.body.getReader();
            const decoder = new TextDecoder('utf-8');
            $(".spinner-border").remove();
            while (true) {
                const {done, value} = await reader.read();
                if (done) break;
                const chunk = decoder.decode(value, {stream: true});
                let lines = chunk.split('\n');
                for (let line of lines) {
                    if (line.trim() === '') continue;
                    const res = JSON.parse(line);
                    const message = res.msg;
                    if (res.type === 'error') {
                        updateAlert(message);
                        return;
                    }
                    if (res.type === 'reasoning') {
                        $('#sp_' + itemId).append(message);
                    }
                    if (res.type === 'msg') {
                        $('#dis_' + itemId).append(message);
                        $('#msg_' + itemId).html(marked.parse($('#dis_' + itemId).html()));
                    }
                    if (res.type === 'item') {
                        groupId = res.data.group_id;
                    }
                }

            }
        }
    </script>
@endsection
