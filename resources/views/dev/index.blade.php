@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <style>
        .blog-list-wrapper .left-content {
            padding-right: 30px
        }

        .blog-list-wrapper .left-content .card-thumb {
            min-height: 200px
        }

        .recent-post-card {
            border: 1px solid #d1d1d1;
            border-radius: 15px;
            width: 340px;
            max-width: 100%;
            margin-right: 10px
        }

        .recent-post-card .card-thumb {
            margin-top: 0;
            border-top-left-radius: 14px;
            border-top-right-radius: 14px;
        }

        .recent-post-card .card-content {
            padding: 0 10px 10px 10px
        }

        .section-head {
            margin-bottom: 20px
        }

        .blog-list-wrapper .left-content .card-content {
            margin-top: 0;
            padding: 20px
        }

        .meta-item {
            font-size: 12px
        }

        .headingr-secondary {
            font-size: 16px
        }

        .meta-item {
            margin-right: 10px
        }

        .meta-item:not(:last-child)::after {
            right: -10px
        }

        .blog-list-wrapper .right-content .card-thumb {
            margin-top: 0
        }

        .meta-item a {
            font-size: 12px;
            color: #777
        }
        @media screen and (max-width: 600px) {
            .recent-post-card {
                margin:10px auto;
                width: 100%;
            }
            .section-padding{padding:0}
            .blog-list-wrapper .left-content{padding-right:0}
            .footer-heading{margin-top:30px}
        }
    </style>
    <div class="container">
        <section class="blog-list-wrapper section-padding">
            <div class="container">
                <div class="section-head">
                    <h2 class="heading-secondary">{{lecho($category->name)}}</h2>
                    <ul class="c-bredcrumb">
                        <li>
                            <a href="{{route('index')}}"> {{lecho('index')}} </a>
                        </li>
                        <li><a href="javascript:void(0);">{{lecho($category->name)}}</a></li>
                    </ul>
                </div>
                <div class="row gy-5">
                    <div class="col-lg-9">
                        <div class="left-content">
                            <div class="d-flex" style="justify-content: flex-start;flex-wrap: wrap">
                                @if($lists->isEmpty())
                                    @include('layout.empty')
                                @else
                                    @foreach($lists as $item)
                                        <div class="recent-post-card mr-2">
                                            <a href="{{route('ArticleDetailIndex',$item)}}">
                                                <div class="card-thumb bg-cover"
                                                     style="background-image: url('{{$item->cover_url}}')"></div>
                                            </a>
                                            <div class="card-content">
                                                <a href="{{route('ArticleDetailIndex',$item)}}">
                                                    <div class="headingr-secondary">{{$item->title}}</div>
                                                </a>
                                                <div class="post-meta">
                                            <span class="meta-item">
                                                <a href="{{route('categoryIndex',$item->category)}}">
                                                {{lecho($item->category->name)}}
                                                </a>
                                            </span>
                                                    <span class="meta-item">{{$item->created_at}}</span>
                                                    <span class="meta-item"><i class="fal fa-comment"></i> {{$item->comments_count}}</span>
                                                </div>

                                            </div>
                                        </div>
                                    @endforeach
                                @endif


                            </div>

                        </div>
                        {{$lists->links()}}
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
