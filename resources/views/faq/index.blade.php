@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <style>
        .fbody h3 {
            margin: 20px 0;
        }

        .uls li {
            line-height: 40px
        }

        .uls li a {
            font-size: 20px
        }

        .uls h5 {
            font-size: 20px;
            margin-bottom: 10px
        }
    </style>
    <div class="container fbody">
        <h3>{{lecho('Familiar_Problem')}}</h3>
        @foreach($children as $child)
            <ul class="uls mt-10">
                <h5>[{{lecho($child->name)}}]</h5>
                @if($child->contents()->where('language',$language)->OfEnabled()->get()->isEmpty())
                    @include('layout.empty')
                @else
                    @foreach($child->contents()->where('language',$language)->orderByDesc('sort')->OfEnabled()->get() as $item)
                        <li><a href="{{route('FaqDetail',['item'=>$item])}}">{{$item->title}}</a></li>
                    @endforeach
                @endif

            </ul>
        @endforeach
    </div>
@endsection
