@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <div class="container fbody">
        <style>
            .mycoin {
                font-size: 40px;
                font-weight: 600;
                margin: 20px 0
            }

            .chart-stat {
                background: #F2F6FE;
                border-radius: 15px;
                padding: 15px 15px;
                margin-bottom: 15px;
            }

            .kaitong {
                font-weight: 600
            }

            .kaitong:hover {
                background-color: #cedbee
            }
        </style>
        @include("layout.clubHeader") <div class="row">
        <div class="col-sm-12 col-md-2 mb-10 pr-20">
            @include("users.menu")
        </div>
        <div class="col-sm-12 col-md-10">
                <div class="row">
                    <div class="col-xl-3 col-sm-6 col-6">
                        <div class="chart-stat">
                            <p class="mb-1" style="font-size:12px">{{lecho('Current user level')}}</p>
                            <h5>{{lecho($identity->name)}}</h5>
                            <div style="font-size:12px">
                                @if($identity->ended_at)
                                    <div>{{lecho('effective time')}} : {{$identity->ended_at}}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-sm-6 col-6 text-center"
                         style="height:92px;line-height:62px;font-weight:600">
                        <div class="chart-stat kaitong cursor" data-href="{{route('club.open.identity')}}">
                            @if($identity->id==1)
                                {{lecho("Open membership")}}
                            @elseif($identity->id<5)
                                {{lecho('Upgrade Membership')}}
                            @endif
                        </div>
                    </div>
                </div>

                <div class="mt-50">
                    <ul class="nav nav-tabs">
                        <li class="nav-item"><a class="nav-link active"
                                                aria-current="page">{{lecho('member benefits')}}</a></li>
                    </ul>
                </div>
                <div class="tables">
                    <table class="table table-striped table-hover">
                        <thead>
                        <tr>
                            <th scope="col">{{lecho('Present interest')}}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @if(count($identity->rules?:[])<=0)
                            <tr>
                                <td colspan="3">{{lecho('You currently do not have any special privileges')}}</td>
                            </tr>
                        @else
                            @foreach($identity->rules as $item)
                                <tr>
                                    <td>{{$item['desc']}}</td>
                                </tr>
                            @endforeach
                        @endif
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
@endsection
