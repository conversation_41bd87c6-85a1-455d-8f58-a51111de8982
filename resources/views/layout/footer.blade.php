<footer class="footer-wrapper">
    <div class="footer-top">
        <div class="container2">
            <div class="row gy-4">
                <div class="col-lg-8">
                    <div class="widget-about">
                        <h3 class="footer-heading">{{lecho('About')}}</h3>
                        <p class="body-text">
                            <label class="badge bg-secondary cursor"
                                   data-href="{{route('PageDetail','about')}}">{{lecho('AboutMe')}}</label>
                            <label class="badge bg-secondary cursor"
                                   data-href="{{route('PageDetail','policy')}}">{{lecho('User Conduct')}}</label>
                            <label class="badge bg-secondary cursor"
                                   data-href="{{route('PageDetail','privacy')}}">{{lecho('Privacy_Policy')}}</label>
                            <label class="badge bg-secondary cursor"
                                   data-href="{{route('PageDetail','disclaimer')}}">{{lecho('Disclaimer')}}</label>
                            <label class="badge bg-secondary cursor"
                                   data-href="{{route('PageDetail','agreement')}}">{{lecho('Usage_Agreement')}}</label>
                            <label class="badge bg-secondary cursor"
                                   data-href="{{route('PageDetail','integral')}}">{{lecho('Integral_Rule')}}</label>
                            <label class="badge bg-secondary cursor"
                                   data-href="{{route('PageDetail','contact')}}">{{lecho('Contact_Us')}}</label>
                            <label class="badge bg-secondary cursor"
                                   data-href="{{route('PageDetail','coin')}}">{{lecho('BitToken')}}</label>
                        </p>
                    </div>
                </div>
                <div class="col-lg-4" style="display: none">
                    <div class="widget-categories">
                        <h3 class="footer-heading">{{lecho('Categories')}}</h3>
                        <div class="categories-tags">
                            @foreach($categories as $item)
                                <a href="{{route('categoryIndex',$item)}}"> {{lecho($item->name)}} </a>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 offset-xl-1">
                    <div class="widget-search">
                        <h3 class="footer-heading">{{lecho('Search')}}</h3>
                        <p class="body-text">{{lecho('You can search for the content you want here')}}</p>
                        <form action="{{ route('index.search') }}" method="get">
                            <div class="widget-search-box">
                                <input type="text" name="title" value="{{ $search_title??'' }}"
                                       placeholder="{{lecho('Search')}}"/>
                                <button class="search-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="footer-bottom">
    </div>
</footer>
