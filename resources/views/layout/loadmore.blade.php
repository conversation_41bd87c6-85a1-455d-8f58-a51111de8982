<div class="text-center m-4">
    <a type="button" id="ajax-page" class=" ">{{lecho('Load more')}}</a>
</div>
<script>
    $(function () {
        var current = 1;
        var url = '{{$url}}';
        var target = '{{$target}}';
        $('#ajax-page').on('click', function () {
            $.get(url, {
                page: ++current,
            }, function (res) {
                if (res.code == 200) {
                    if (res.data.content != '') {
                        $('#' + target).append(res.data.content);
                    } else {
                        $("#ajax-page").hide()
                        --current;
                    }
                } else {
                    updateAlert(res.message, false);
                }
            })
        });
    })
</script>
