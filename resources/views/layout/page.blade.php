<nav aria-label="Page navigation example">
    <ul class="pagination">
        <li class="page-item">
            <a class="page-link" href="{{request()->fullUrlWithQuery(['page'=>1])}}">{{lecho('First Page')}}</a>
        </li>
        @if($pages['current']>1)
            <li class="page-item">
                <a class="page-link"
                   href="{{request()->fullUrlWithQuery(['page'=>$pages['current']-1])}}">{{lecho('Previous Page')}}</a>
            </li>
        @else
            <li class="page-item disabled">
                <a class="page-link" href="#">{{lecho('Previous Page')}}</a>
            </li>
        @endif

        @for($i=max($pages['current']-3,1);$i<=$pages['total_page'] && $i<=$pages['current']+3;$i++)
            <li class="page-item @if($i==$pages['current']) active @endif"><a class="page-link"
                                                                              href="{{request()->fullUrlWithQuery(['page'=>$i])}}">{{$i}}</a>
            </li>
        @endfor
        @if($pages['has_more'])
            <li class="page-item "><a class="page-link"
                                      href="{{request()->fullUrlWithQuery(['page'=>$pages['current']+1])}}">{{lecho('Next Page')}}</a>
            </li>
        @else
            <li class="page-item disabled">
                <a class="page-link" href="#">{{lecho('Next Page')}}</a>
            </li>
        @endif
        <li class="page-item"><a class="page-link"
                                 href="{{request()->fullUrlWithQuery(['page'=>$pages['total_page']])}}">{{lecho('Last Page')}}</a>
        </li>
    </ul>
</nav>