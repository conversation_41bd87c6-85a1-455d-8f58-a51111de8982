@extends('layout.base')
@section('title',$sub_title)

@section('content')
    <style>
        .detail {
            margin-top: 20px
        }

        .detail p {
            font-size: 20px;
            color: #333
        }

        .d3 {
            font-size: 20px;
            display: flex;
            align-items: center
        }

        .d3 a {
            font-size: 12px;
            border: 1px solid #ffd662;
            background-color: #ffe69c;
            padding: 0px 10px;
            border-radius: 4px;
            margin-right: 10px
        }
    </style>
    <div class="container fbody">
        <div class="d3"><<a href="javascript:window.history.back()">返回</a> {{$item->title}}</div>
        @if(!blank($item->description))
            <div class="alert alert-primary mt-20" role="alert">
                {{$item->description}}
            </div>
        @endif
        <div class="detail mt-20">
            {!! $item->content !!}
        </div>
    </div>
@endsection
