@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <div class="container fbody">
        <style>
            .mycoin {
                font-size: 40px;
                font-weight: 600;
                margin: 20px 0
            }
        </style>
        @include("layout.clubHeader") <div class="row">
        <div class="col-sm-12 col-md-2 mb-10 pr-20">
            @include("users.menu")
        </div>
        <div class="col-sm-12 col-md-10">
                <div class="mb-5">
                    <a href="javascript:window.history.back()">< {{lecho("Go back")}}</a>
                </div>
                <div class="flex" style="align-items: center;justify-content: space-between">
                    <div>
                        <div>{{notificationType($notification->type)}}</div>
                    </div>
                </div>
                <div class="tables">
                    {{ lecho('Notice_Content') }}:{{$notification->data['content']}}<br>
                    {{ lecho('Notice_read_time') }}:{{$notification->read_at?:''}}<br>
                    {{ lecho('Notice_time') }}:{{$notification->created_at?:''}}
                </div>
            </div>
        </div>
    </div>
@endsection
