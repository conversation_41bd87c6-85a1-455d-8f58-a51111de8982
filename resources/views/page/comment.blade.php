@foreach($comments as $comment)
    <div class="comment-card ">
        <div class="card-top">
            <div class="card-meta">
                <div class="meta-item post-author">
                    <div class="author-avatar bg-cover"
                         style="background-image: url('{{$comment->user->info->avatar_url}}');"
                    ></div>
                    <a href="#"
                       class="author-name">{{$comment->user->info->nickname}}</a>
                </div>
                <span class="meta-item"> {{$comment->created_at->toDateString()}} </span>
                <span class="meta-item"> {{$comment->created_at->toTimeString()}}</span>
            </div>
        </div>
        <p class="body-text">
            {{$comment->content}}
        </p>
    </div>
@endforeach