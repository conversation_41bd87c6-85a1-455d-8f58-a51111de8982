@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <style>
        .pay_title {
            font-size: 28px;
            margin: 20px 0
        }

        .pay_item {
            font-size: 14px;
        }

        .wechat svg {
            width: 100px;
            margin-left: 20px
        }

        .paypal svg {
            width: 100px;
            margin-left: 20px
        }

        .ajax-get {
            border-radius: 20px;
            height: 40px
        }

        .monos {
            font-size: 20px
        }
    </style>
    <div style="padding:100px 0">
        <div class="container">
            <div style="margin:0 auto;width:500px;max-width:100%;border:1px solid #d9d9d9;border-radius:10px;padding:20px;color:#000000">
                <div class="pay_title">{{lecho('Ordering Information')}}</div>
                <div class="pay_item">{{lecho('Order')}}：{{$order->getTitle()}}</div>
                <div class="pay_item">{{lecho('Order No.')}}：{{$order->no}}</div>
                <div class="pay_item">{{lecho('amount of money')}}：<span
                            class="monos">{{env('MONEY_UNIT','￥')}}{{$order->amount}}</span> {{  $order->getPaymentUnit() }}
                </div>
                <div class="pay_select">
                    <div class="pay_select_title mt-20">{{lecho('Please choose a payment method')}}:</div>
                    <div class="mt-10">
                        @if($switch['wechat'])
                            <div class="form-check wechat d-flex" style="align-items: center">
                                <input class="form-check-input" type="radio" name="paychat" value="1" id="paychat"
                                       checked>
                                <label class="form-check-label" for="paychat">
                                    <svg t="1732114153830" class="icon" viewBox="0 0 2867 1024" version="1.1"
                                         xmlns="http://www.w3.org/2000/svg" p-id="7697" width="134.390625" height="48"
                                         xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <path d="M1180.16 408.064c30.72-35.84 57.344-78.336 78.848-127.488l27.136 12.8c-10.24 21.504-20.48 41.472-31.744 59.904v215.04h-27.648V396.288c-11.264 16.384-23.552 31.232-35.84 44.544-3.072-10.752-6.656-22.016-10.752-32.768z m2.048-132.096c34.304-34.816 60.416-70.656 77.312-107.008l25.6 14.336c-24.064 46.08-55.296 87.552-93.184 124.416-2.56-10.24-6.144-20.992-9.728-31.744z m206.336 159.744h-54.272v18.944c0 49.152-13.824 83.968-41.472 104.96-4.608-7.68-10.752-16.384-18.432-26.112 21.504-14.336 32.256-41.472 32.256-81.92v-43.52h109.056v71.168c12.8-10.752 27.136-23.552 43.008-38.4 2.56 11.776 4.608 22.528 7.168 31.744-14.848 11.776-32.256 26.624-51.2 44.032-6.144 5.632-11.264 11.264-16.384 16.384l-20.992-26.112c7.168-7.168 11.264-17.408 11.264-29.696v-41.472z m-94.72-131.072V204.288h26.112v72.704h33.792V172.544h27.136V276.48h33.792v-72.704h26.112v114.176h-26.112V304.64h-120.832z m8.192 36.352h130.56v28.672h-130.56v-28.672z m186.368-174.08l27.648 5.12c-4.608 25.088-9.728 49.152-15.36 72.192h88.576v29.696h-19.456c-5.632 78.336-18.944 140.288-39.424 185.856 15.872 30.72 36.352 56.32 61.952 78.336-6.144 10.752-11.776 20.992-17.408 31.744-24.064-23.552-44.032-49.664-59.904-79.36-16.384 28.672-37.376 55.808-63.488 82.432-5.632-7.68-12.288-16.384-19.968-25.6 29.696-28.16 52.736-57.344 69.12-88.064-15.36-37.888-24.576-80.384-27.648-127.488-4.608 10.752-9.216 20.48-13.824 30.72-5.632-7.168-11.776-14.336-19.456-21.504 22.528-48.128 39.424-105.472 48.64-174.08z m53.248 106.496h-49.152l-1.024 4.096c0.512 55.808 8.192 104.96 23.552 146.944 11.776-33.792 20.48-83.968 26.624-151.04zM1600 363.52c39.936-59.392 70.144-125.44 90.624-197.632l31.744 9.216c-11.776 36.864-25.088 70.656-38.912 101.888V570.88h-29.696V336.896c-12.8 22.016-25.6 41.984-39.936 60.416L1600 363.52z m118.784-141.824h133.632c-8.192-13.824-16.896-28.16-25.6-41.984l27.648-14.336c9.728 14.848 19.456 30.208 29.184 46.592l-17.92 9.216h141.824v28.672h-288.768v-28.16z m22.528 66.56h241.664v27.648h-241.664v-27.648z m0 66.048h241.664v27.648h-241.664v-27.648z m4.608 66.56h233.472V570.88h-31.744v-24.576h-169.984v24.576h-31.744V420.864z m201.728 27.648h-169.984v69.632h169.984v-69.632z m77.824-224.768h186.368v-58.88h33.792v58.88h177.664v29.696h-177.664v60.928h115.712v28.672c-24.576 54.272-59.392 99.84-104.448 136.192 44.544 24.064 101.888 41.472 172.032 51.712-9.728 13.824-17.408 25.6-23.552 35.84-77.824-15.36-137.728-37.376-179.712-65.536-44.544 27.648-105.472 51.712-183.296 71.68-5.632-12.8-11.264-23.552-17.92-32.256 74.24-16.896 132.608-36.864 174.08-59.904-39.424-33.792-70.144-79.36-92.16-135.68h-40.448v-29.696h145.92v-61.44h-186.368v-30.208z m295.424 120.832h-180.224c19.968 49.152 48.64 88.064 86.528 116.224 42.496-32.256 73.728-70.656 93.696-116.224z m119.808 16.896c37.888-53.248 69.12-118.272 93.696-195.584l33.792 12.8c-11.264 30.72-23.04 58.88-34.816 84.992v304.64h-32.768V328.704c-15.36 27.136-30.72 50.688-46.592 71.68-5.632-18.432-9.728-31.232-13.312-38.912zM2560 259.072h187.904v-89.6h33.28v89.6h59.392v31.232h-59.392v207.36c0 42.496-19.456 64-57.856 65.024-12.8 0.512-33.28 0-61.44-0.512-1.536-10.752-3.584-23.04-6.656-36.864 26.112 2.048 46.592 3.072 60.928 3.072 12.8 0.512 20.992-2.56 25.6-8.192 4.608-5.12 6.656-15.872 6.656-32.256V290.304H2560v-31.232z m26.624 96.256l27.648-17.408c16.896 24.064 36.352 55.808 58.88 94.72l-29.696 18.432c-19.456-37.376-38.4-69.632-56.832-95.744zM1509.376 681.984l-56.32 201.216h-30.208L1382.4 738.304c-2.048-6.656-3.072-13.312-3.072-20.992h-0.512c-0.512 7.168-1.536 13.824-3.584 20.48l-40.96 145.408h-30.208l-58.368-201.216h28.672l40.96 152.064c2.048 6.656 3.072 13.824 3.072 20.48h1.024c0.512-5.12 2.048-12.288 4.096-20.48l43.52-152.064h25.6l41.472 153.088c1.536 5.12 2.56 11.264 3.584 19.456h0.512c0.512-5.632 1.536-12.288 3.584-19.968l39.424-152.576h28.16z m136.192 136.192h-100.864c0.512 15.36 4.608 27.136 12.8 35.328s18.944 12.8 33.28 12.8c15.872 0 30.72-5.12 44.544-15.36v22.528c-12.8 8.704-29.184 13.312-50.176 13.312s-36.864-6.656-48.64-19.968-17.408-31.232-17.408-54.784c0-22.016 6.144-39.936 18.944-54.272s28.672-21.504 47.616-21.504 33.28 6.144 44.032 17.92 15.872 29.184 15.872 50.688v13.312z m-25.6-20.48c0-12.8-3.072-23.04-9.216-30.208s-14.848-10.752-25.6-10.752c-10.24 0-18.944 3.584-26.112 11.264s-12.288 17.408-13.824 29.696h74.752z m200.704 77.312c-14.848 7.68-33.28 11.776-55.808 11.776-28.672 0-51.712-9.216-69.12-27.648s-26.112-42.496-26.112-72.704c0-32.256 9.728-58.368 29.184-78.336s44.032-29.696 73.728-29.696c19.456 0 35.328 2.56 47.616 8.192v27.136c-14.336-8.192-30.208-11.776-47.616-11.776-22.528 0-40.96 7.68-54.784 22.528s-20.992 35.328-20.992 60.416c0 24.064 6.656 43.008 19.968 57.344s30.208 21.504 51.712 21.504c19.968 0 37.376-4.608 52.224-13.312v24.576z m158.208 8.704h-25.6V801.28c0-29.184-10.752-43.52-32.256-43.52-10.752 0-19.968 4.096-27.648 12.8s-10.752 18.944-10.752 31.744v81.408h-25.6V670.72h25.6v92.672h0.512c10.752-17.92 26.624-27.136 47.104-27.136 32.256 0 48.64 19.456 48.64 58.88v88.576z m46.08-134.144c14.336-8.704 30.72-13.312 49.664-13.312 34.816 0 52.224 18.432 52.224 54.784v92.672h-25.088v-22.016h-0.512c-9.728 16.896-24.576 25.6-44.032 25.6-13.824 0-25.088-3.584-33.28-11.264s-12.288-17.92-12.288-30.72c0-26.624 15.872-42.496 47.616-47.104l42.496-6.144c0-23.552-9.728-35.328-29.184-35.328-17.408 0-32.768 5.632-47.104 17.408v-24.576z m42.496 66.048c-11.776 1.536-19.968 4.608-24.576 8.704s-6.656 10.24-6.656 18.432c0 6.656 2.56 12.288 7.168 16.896s11.264 6.656 19.456 6.656c11.264 0 20.48-4.096 27.648-11.776s10.752-17.408 10.752-29.184V811.52l-33.792 4.096z m169.984 66.56c-5.632 3.072-12.8 4.608-22.528 4.608-25.6 0-38.4-14.336-38.4-43.008v-82.944h-24.576V739.84h24.576v-34.816l25.6-8.192v42.496h35.84v20.992h-35.84v78.336c0 9.216 1.536 16.384 4.608 20.48s8.704 6.144 15.872 6.144c5.632 0 10.752-1.536 14.848-4.608v21.504z m138.24-73.728v75.264h-25.6V682.496h57.344c21.504 0 38.4 5.12 50.688 15.872s17.92 25.6 17.92 45.056c0 19.968-7.168 35.84-20.992 48.128s-31.744 17.92-52.736 17.408h-26.624z m0-103.424v80.384h24.064c15.872 0 28.16-3.584 36.352-10.752s12.288-17.408 12.288-30.72c0-26.112-15.36-38.912-46.08-38.912h-26.624z m128 44.544c14.336-8.704 30.72-13.312 49.664-13.312 34.816 0 52.224 18.432 52.224 54.784v92.672h-25.088v-22.016h-0.512c-9.728 16.896-24.576 25.6-44.032 25.6-13.824 0-25.088-3.584-33.28-11.264s-12.288-17.92-12.288-30.72c0-26.624 15.872-42.496 47.616-47.104l42.496-6.144c0-23.552-9.728-35.328-29.184-35.328-17.408 0-32.768 5.632-47.104 17.408v-24.576z m42.496 66.048c-11.776 1.536-19.968 4.608-24.576 8.704s-6.656 10.24-6.656 18.432c0 6.656 2.56 12.288 7.168 16.896s11.264 6.656 19.456 6.656c11.264 0 20.48-4.096 27.648-11.776s10.752-17.408 10.752-29.184V811.52l-33.792 4.096z m218.112-75.776l-65.024 165.376c-12.288 30.72-29.184 46.08-51.2 46.08-6.144 0-11.776-0.512-16.384-2.048V926.72c5.12 2.048 9.728 3.072 14.336 3.072 11.776 0 19.968-6.656 26.112-20.48l11.264-26.112-56.32-143.36h28.16l37.376 108.032 3.072 11.776h0.512c0.512-2.56 1.536-6.656 3.072-11.776l39.424-108.544h25.6z"
                                              fill="#393738" p-id="7698"></path>
                                        <path d="M384 631.808c-4.608 2.56-9.728 3.584-15.36 3.584-12.8 0-24.064-7.168-29.696-17.408l-2.048-5.12-93.184-204.288c-1.024-2.048-1.536-4.608-1.536-7.168 0-9.216 7.68-16.896 16.896-16.896 3.584 0 7.168 1.024 10.24 3.584L378.88 465.92c8.192 5.12 17.408 8.192 28.16 8.192 6.144 0 12.288-1.024 17.408-3.072l517.12-230.4C848.896 131.584 696.32 59.904 523.264 59.904c-282.624 0-512 190.976-512 426.496 0 128.512 69.12 244.224 177.152 322.56 8.704 6.144 14.336 16.384 14.336 27.648 0 3.584-1.024 7.168-2.048 10.752-8.704 32.256-22.528 83.456-23.04 86.016-1.024 4.096-2.56 8.192-2.56 12.288 0 9.216 7.68 16.896 16.896 16.896 3.584 0 6.656-1.536 9.728-3.072L314.368 896c8.192-5.12 17.408-7.68 27.136-7.68 5.12 0 10.24 1.024 14.848 2.048 52.224 14.848 108.544 23.552 166.912 23.552 282.624 0 512-190.976 512-426.496 0-71.168-20.992-138.752-58.368-197.632L387.584 629.76 384 631.808z"
                                              fill="#00AC1C" p-id="7699"></path>
                                    </svg>
                                </label>
                            </div>
                        @endif
                        @if($switch['paypal'])
                            <div class="form-check paypal d-flex" style="align-items: center">
                                <input class="form-check-input" type="radio" name="paychat" value="2" id="paychat2">
                                <label class="form-check-label" for="paychat2">
                                    <svg t="1732114128254" class="icon" viewBox="0 0 4220 1024" version="1.1"
                                         xmlns="http://www.w3.org/2000/svg" p-id="4986" width="197.8125" height="48"
                                         xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <path d="M3249.722604 229.409369h-232.537679c-15.641548 0-29.197556 11.470468-32.325865 27.112016l-93.849287 595.421589c-2.08554 11.470468 7.299389 21.898167 18.769857 21.898167h118.875764c11.470468 0 20.855397-8.342159 21.898167-18.769858l27.112016-168.928717c2.08554-15.641548 15.641548-27.112016 32.325866-27.112016h72.99389c153.287169 0 240.879837-74.03666 263.820774-221.06721 10.427699-63.608961 0-114.704684-29.197556-150.158859-31.283096-38.582485-89.678208-58.395112-167.885947-58.395112z m27.112016 216.89613c-12.513238 83.421589-76.1222 83.421589-137.645621 83.421589h-35.454175l25.026476-155.372709c1.04277-9.384929 9.384929-16.684318 18.769858-16.684318h15.641547c41.710794 0 81.336049 0 102.191447 23.983707 12.513238 14.598778 15.641548 35.454175 11.470468 64.651731z"
                                              fill="#009CDE" p-id="4987"></path>
                                        <path d="M1594.84684 229.409369h-232.537678c-15.641548 0-29.197556 11.470468-32.325866 27.112016l-93.849287 595.421589c-2.08554 11.470468 7.299389 21.898167 18.769857 21.898167h110.533605c15.641548 0 29.197556-11.470468 32.325866-27.112017l25.026476-160.586558c2.08554-15.641548 15.641548-27.112016 32.325866-27.112016h72.99389c153.287169 0 240.879837-74.03666 263.820774-221.06721 10.427699-63.608961 0-114.704684-29.197556-150.158859-31.283096-38.582485-89.678208-58.395112-167.885947-58.395112z m27.112016 216.89613c-12.513238 83.421589-76.1222 83.421589-137.645621 83.421589h-35.454175l25.026476-155.372709c1.04277-9.384929 9.384929-16.684318 18.769858-16.684318h15.641548c41.710794 0 81.336049 0 102.191446 23.983707 12.513238 14.598778 15.641548 35.454175 11.470468 64.651731zM2288.288795 443.177189h-111.576375c-9.384929 0-17.727088 7.299389-18.769857 16.684318l-5.213849 31.283096-7.299389-11.470469c-23.983707-34.411405-77.164969-46.924644-131.389002-46.924643-123.046843 0-227.323829 92.806517-248.179226 223.152749-10.427699 64.651731 4.171079 127.217923 41.710794 171.014257 34.411405 39.625255 82.378819 56.309572 139.731161 56.309572 99.063136 0 153.287169-63.608961 153.287169-63.608961l-5.21385 31.283096c-2.08554 11.470468 7.299389 21.898167 18.769858 21.898167h100.105906c15.641548 0 29.197556-11.470468 32.325866-27.112017l60.480652-380.610998c2.08554-10.427699-6.256619-21.898167-18.769858-21.898167z m-154.329939 216.896131c-10.427699 63.608961-61.523422 106.362525-125.132383 106.362525-32.325866 0-58.395112-10.427699-75.079429-30.240326-16.684318-19.812627-22.940937-46.924644-17.727088-78.207739 10.427699-62.566191 61.523422-107.405295 124.089613-107.405295 31.283096 0 57.352342 10.427699 74.03666 30.240326 17.727088 20.855397 25.026477 47.967413 19.812627 79.250509z"
                                              fill="#003087" p-id="4988"></path>
                                        <path d="M3943.164559 443.177189h-111.576375c-9.384929 0-17.727088 7.299389-18.769857 16.684318l-5.21385 31.283096-7.299389-11.470469c-23.983707-34.411405-77.164969-46.924644-131.389002-46.924643-123.046843 0-227.323829 92.806517-248.179226 223.152749-10.427699 64.651731 4.171079 127.217923 41.710795 171.014257 34.411405 39.625255 82.378819 56.309572 139.73116 56.309572 99.063136 0 153.287169-63.608961 153.28717-63.608961l-5.21385 31.283096c-2.08554 11.470468 7.299389 21.898167 18.769858 21.898167h100.105906c15.641548 0 29.197556-11.470468 32.325866-27.112017l60.480651-380.610998c2.08554-10.427699-6.256619-21.898167-18.769857-21.898167z m-154.329939 216.896131c-10.427699 63.608961-61.523422 106.362525-125.132383 106.362525-32.325866 0-58.395112-10.427699-75.07943-30.240326-16.684318-19.812627-22.940937-46.924644-17.727087-78.207739 10.427699-62.566191 61.523422-107.405295 124.089613-107.405295 31.283096 0 57.352342 10.427699 74.03666 30.240326 17.727088 20.855397 25.026477 47.967413 19.812627 79.250509z"
                                              fill="#009CDE" p-id="4989"></path>
                                        <path d="M2880.582074 443.177189h-111.576375c-10.427699 0-20.855397 5.213849-27.112016 14.598778l-154.329939 227.323829-65.694501-217.9389c-4.171079-13.556008-16.684318-22.940937-31.283096-22.940937h-109.490835c-13.556008 0-22.940937 13.556008-18.769857 26.069247l123.046843 360.79837-115.747454 162.672098c-9.384929 12.513238 0 30.240326 15.641548 30.240326h111.576375c10.427699 0 20.855397-5.213849 26.069246-13.556008l371.226069-535.983707c11.470468-13.556008 2.08554-31.283096-13.556008-31.283096z"
                                              fill="#003087" p-id="4990"></path>
                                        <path d="M4074.553561 245.050916l-94.892057 605.849288c-2.08554 11.470468 7.299389 21.898167 18.769857 21.898167h95.934827c15.641548 0 29.197556-11.470468 32.325866-27.112017l93.849287-595.421588c2.08554-11.470468-7.299389-21.898167-18.769857-21.898167h-107.405296c-10.427699 1.04277-18.769857 7.299389-19.812627 16.684317z"
                                              fill="#009CDE" p-id="4991"></path>
                                        <path d="M782.529121 259.649695c12.513238-79.250509 0-133.474542-42.753564-182.484726C691.808143 22.940937 606.301015 0 496.81018 0H178.765374c-21.898167 0-41.710794 16.684318-44.839104 38.582485L0.451728 879.05499c-3.12831 16.684318 10.427699 31.283096 27.112016 31.283096h196.040733l-13.556008 85.507128c-2.08554 14.598778 9.384929 27.112016 23.983707 27.112016h165.800407c19.812627 0 36.496945-14.598778 39.625255-33.368635l2.08554-8.342159 31.283095-198.126273 2.08554-10.427699c3.12831-19.812627 19.812627-33.368635 39.625255-33.368635h25.026476c160.586558 0 285.718941-64.651731 322.215886-253.393075 15.641548-79.250509 7.299389-144.94501-33.368635-190.826884-12.513238-13.556008-28.154786-26.069246-45.881874-35.454175"
                                              fill="#009CDE" p-id="4992"></path>
                                        <path d="M782.529121 259.649695c12.513238-79.250509 0-133.474542-42.753564-182.484726C691.808143 22.940937 606.301015 0 496.81018 0H178.765374c-21.898167 0-41.710794 16.684318-44.839104 38.582485L0.451728 879.05499c-3.12831 16.684318 10.427699 31.283096 27.112016 31.283096h196.040733l49.010184-312.830958-1.04277 9.384929c3.12831-21.898167 21.898167-38.582485 44.839104-38.582485h93.849287c183.527495 0 327.429735-74.03666 369.140529-289.89002l3.12831-18.769857"
                                              fill="#012169" p-id="4993"></path>
                                        <path d="M326.838693 260.692464c2.08554-13.556008 10.427699-23.983707 21.898167-30.240326 5.213849-2.08554 11.470468-4.171079 16.684318-4.171079h250.264766c29.197556 0 57.352342 2.08554 82.378819 6.256619 7.299389 1.04277 14.598778 2.08554 20.855397 4.17108 7.299389 1.04277 13.556008 3.12831 19.812627 5.213849l9.384929 3.128309c12.513238 4.171079 23.983707 9.384929 34.411405 14.598779 12.513238-79.250509 0-133.474542-42.753564-182.484726C691.808143 22.940937 606.301015 0 496.81018 0H178.765374c-21.898167 0-41.710794 16.684318-44.839104 38.582485L0.451728 879.05499c-3.12831 16.684318 10.427699 31.283096 27.112016 31.283096h196.040733l49.010184-312.830958L326.838693 260.692464z"
                                              fill="#003087" p-id="4994"></path>
                                    </svg>
                                </label>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="mt-20">
                    @if($switch['wechat'])
                        <div class="col p-2 mt-10 wechat_div" style="display:block">
                            <button class="btn btn-success ajax-get" style="width:100%" data-href="{{route('payment.create',[
                                'type'=>$type,
                                'no'=>$no,
                                'gateway'=>\Modules\Payment\Enums\Gateway::WECHAT->value,
                                'channel'=>\Modules\Payment\Enums\Channel::WECHAT_SCAN->value
                                ])}}">{{lecho('Immediate payment')}}&nbsp; {{$order->amount}}
                            </button>
                        </div>
                    @endif


                    @if($switch['paypal'])
                        <div class="col p-2 mt-10 paypal_div" style="display: none">
                            <button class="btn btn-success ajax-get" style="width:100%" data-href="{{route('payment.create',[
                                'type'=>$type,
                                'no'=>$no,
                                'gateway'=>\Modules\Payment\Enums\Gateway::PAYPAL->value,
                            'channel'=>\Modules\Payment\Enums\Channel::PAYPAL_WEB->value
                            ])}}">{{lecho('Immediate payment')}}&nbsp; {{$order->amount}}
                            </button>
                        </div>
                    @endif
                </div>
            </div>
            <div class="text-center mt-20">
                <a href="javascript:window.history.back(-1);" class="mt-10">{{lecho('Back')}}</a>
            </div>
        </div>
    </div>
@endsection
@section("script")
    <script>
        $(function () {
            $('input[type=radio][name=paychat]').change(function () {
                var selectedValue = $('input[name="paychat"]:checked').val();
                console.log(selectedValue)
                if (selectedValue == "1") {
                    $(".wechat_div").show();
                    $(".paypal_div").hide();
                } else {
                    $(".wechat_div").hide();
                    $(".paypal_div").show();
                }
            });
            $('input[name="paychat"]').eq(0).change();

        })
    </script>
@endsection
