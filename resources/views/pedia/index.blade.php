@extends('layout.base')

@section('content')
    <style>
        .card-body{padding:10px}
        .finput{padding:100px 0 70px 0;width:800px;max-width:95%;margin:0 auto;}
        .finput input{padding:20px}
        .finput input::placeholder{font-size:20px}
        .a-title{font-size:40px;padding:30px 0 50px 0;text-align: center;color:#000}
    </style>
    <div class="container fbody">
        <div class="finput">
            <div class="a-title">{{lecho('wikipedia')}}</div>
            <form class="row g-3" id="aform" action="/pedia/search">
                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="word" placeholder="{{lecho('this search')}}" id="query_input">
                    <span id="pedia_search" class="input-group-text cursor"><i class="fas fa-search mr-5"></i>{{lecho('Encyclopedia search')}}</span>
                </div>
            </form>
        </div>
{{--        <div class="fbody">--}}
{{--            <div class="card mb-3" style="max-width: 540px;">--}}
{{--                <div class="row g-0">--}}
{{--                    <div class="col-md-4">--}}
{{--                        <img src="/assets/img/cccf11abb9eeae3e4d97eb5406850120.png" class="img-fluid rounded-start">--}}
{{--                    </div>--}}
{{--                    <div class="col-md-8">--}}
{{--                        <div class="card-body">--}}
{{--                            <h5 class="card-title">Card title</h5>--}}
{{--                            <p class="card-text">This is a wider card with supporting text below as a natural lead-in to additional content. This content is a little bit longer.</p>--}}
{{--                            <p class="card-text"><small class="text-muted">Last updated 3 mins ago</small></p>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
    </div>
@endsection

@section('script')
    <script>
        $(function(){
            $("#pedia_search").click(function(){
                if($("#query_input").val() == "") {
                    alert("{{lecho('Please enter keywords')}}");
                    return false;
                }
                $("#aform").submit();
            })
        })
    </script>
@endsection
