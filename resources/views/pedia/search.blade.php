@extends('layout.base')

@section('content')
    <style>
        .card-body {
            padding: 10px
        }

        .finput {
            padding: 20px 0 20px 0;
            width: 800px;
            max-width: 95%;
            margin: 0;
        }

        .finput input {
            padding: 20px
        }

        .finput input::placeholder {
            font-size: 20px
        }

        .fbody h3 {
            margin: 20px 0;
        }

        .uls li {
            line-height: 40px
        }

        .uls li a {
            font-size: 20px
        }

        .uls h5 {
            font-size: 20px;
            margin-bottom: 10px
        }
    </style>
    <div class="container fbody">
        <h3>{{ lecho('Encyclopedia search') }}</h3>
        <div class="finput">
            <form class="row g-3" id="aform" action="/pedia/search">
                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="word" placeholder="{{lecho('this search')}}"
                           id="query_input">
                    <span id="pedia_search" class="input-group-text cursor"><i class="fas fa-search mr-5"></i>{{lecho('Encyclopedia search')}}</span>
                </div>
            </form>
        </div>
        <div class="fbody">
            <div class="result">{{lecho('Search result')}}:</div>
            <div class="mt-20">
                @foreach($view_result as $vo)
                    <div class="d-flex">
                        <div class="mb-2 mr-3">
                            @if(!empty($vo['thumbnail']))
                                <a target="_blank" href="/wiki/{{str_replace(' ','_',$vo['title'])}}">
                                    <span><img style="width:60px;" src="{{$vo['thumbnail']['source']}}"></span>
                                </a>
                            @else
                                <a target="_blank" href="/wiki/{{str_replace(' ','_',$vo['title'])}}">
                                    <span><img style="width:60px;" src="/assets/img/no1.jpg"></span>
                                </a>
                            @endif
                        </div>
                        <div style="padding-left:10px">
                            <a target="_blank" href="/wiki/{{str_replace(' ','_',$vo['title'])}}">
                                {{$vo['title']}}
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(function () {
            $("#pedia_search").click(function () {
                if ($("#query_input").val() == "") {
                    alert("{{lecho('Please enter keywords')}}");
                    return false;
                }
                $("#aform").submit();
            })
        })
    </script>
@endsection
