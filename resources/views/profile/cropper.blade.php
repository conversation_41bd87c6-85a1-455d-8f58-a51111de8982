<link href="/assets/css/cropper.min.css" rel="stylesheet">
<link href="/assets/css/sitelogo.css" rel="stylesheet">
<script src="/assets/js/cropper.js"></script>
<script src="/assets/js/sitelogo.js"></script>
<div class="user_pic" style="margin: 10px;">
    <img src="" id=""/>
</div>


<div class="modal fade" id="avatar-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <form class="avatar-form">
                <div class="modal-header">
                    <div class="modal-title fs-5">{{lecho('Modify avatar')}}</div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">


                    <form class="avatar-form">
                        <div class="avatar-body">
                            <div class="avatar-upload">
                                <input class="avatar-src" name="avatar_src" type="hidden">
                                <input class="avatar-data" name="avatar_data" type="hidden">
                                <button class="btn btn-primary" type="button" style="height: 35px;"
                                        onclick="$('input[id=avatarInput]').click();">{{lecho('Please select an image')}}
                                </button>
                                <span id="avatar-name"></span>
                                <input class="avatar-input hide" style="display: none" id="avatarInput"
                                       name="avatar_file" type="file"></div>
                            <div class="row">
                                <div class="col-md-9">
                                    <div class="avatar-wrapper"></div>
                                </div>
                                <div class="col-md-3">
                                    <div class="avatar-preview preview-lg" id="imageHead"></div>
                                </div>
                            </div>
                            <div class="row avatar-btns d-flex" style="justify-content: space-between">
                                <div class="col-md-8">
                                    <div class="btn-group">
                                        <button class="btn btn-primary fa fa-undo" data-method="rotate"
                                                data-option="-90" type="button" title="Rotate -90 degrees"></button>
                                    </div>
                                    <div class="btn-group">
                                        <button class="btn btn-primary fa fa-repeat" data-method="rotate"
                                                data-option="90" type="button" title="Rotate 90 degrees"></button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-primary btn-block avatar-save fa fa-save" type="button"
                                            data-dismiss="modal"> {{lecho('Save modifications')}}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="loading" aria-label="Loading" role="img" tabindex="-1"></div>
<script src="/assets/js/html2canvas.min.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    //做个下简易的验证  大小 格式
    $(function () {
        $('#avatarInput').on('change', function (e) {
            var filemaxsize = 1024 * 5;//5M
            var target = $(e.target);
            var Size = target[0].files[0].size / 1024;
            if (Size > filemaxsize) {
                alert('{{lecho('The image is too large, please choose again')}}!');
                $(".avatar-wrapper").childre().remove;
                return false;
            }
            if (!this.files[0].type.match(/image.*/)) {
                alert('{{lecho('Please select the correct image')}}!')
            } else {
                var filename = document.querySelector("#avatar-name");
                var texts = document.querySelector("#avatarInput").value;
                var teststr = texts; //你这里的路径写错了
                testend = teststr.match(/[^\\]+\.[^\(]+/i); //直接完整文件名的
                filename.innerHTML = testend;
            }

        });

        $(".avatar-save").on("click", function () {
            var img_lg = document.getElementById('imageHead');
            // 截图小的显示框内的内容
            html2canvas(img_lg, {
                allowTaint: true,
                taintTest: false,
                onrendered: function (canvas) {
                    canvas.id = "mycanvas";
                    //生成base64图片数据
                    var dataUrl = canvas.toDataURL("image/jpeg");
                    var newImg = document.createElement("img");
                    newImg.src = dataUrl;
                    imagesAjax(dataUrl)
                }
            });
        })

        function imagesAjax(src) {

            var data = {};
            data.img = src;
            data.jid = $('#jid').val();
            data._token = "{{csrf_token()}}";
            $.ajax({
                url: "{{route('storage.avatar')}}",
                data: data,
                type: "POST",
                dataType: 'json',
                success: function (re) {
                    if (re.code == 200) {
                        updateAlert(re.message, true);
                        if (re.data.url) {
                            location.href = re.data.url;
                        } else {
                            location.reload();
                        }
                    } else {
                        updateAlert(re.message, false);
                    }
                }
            });
        }
    })
</script>
