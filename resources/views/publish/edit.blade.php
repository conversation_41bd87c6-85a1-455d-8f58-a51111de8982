@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <style>
        .editors {
            border: 1px solid #d1d1d1;
            border-radius: 5px
        }

        #toolbar-container {
            border-bottom: 1px solid #d1d1d1
        }
    </style>
    <div class="container fbody">
        @include("layout.clubHeader")
        <div class="row">
            <div class="col-sm-12 col-md-2 mb-10 pr-20">
                @include("users.menu")
            </div>
            <div class="col-sm-12 col-md-10">
                <form action="{{route('PublishEditDo',$item)}}" id="pushForm" method="post"
                      enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="blog_title" class="form-label">{{lecho('title')}} *</label>
                        <input type="text" name="title" class="form-control" id="blog_title"
                               value="{{$item->title}}"
                               placeholder="{{lecho('Please enter the title of the blog post')}}">
                    </div>
                    <div class="mb-3" style="display: none">
                        <label for="blog_description" class="form-label">{{lecho('description')}}</label>
                        <textarea class="form-control" name="blog_description">{{$item->description}}</textarea>
                    </div>

                    <div class="mb-3" style="display: none">
                        <label for="category_id" class="form-label">{{lecho('Category')}}</label>
                        <select class="form-select" name="category_id" id="category_id" aria-label="category_id">
                            <option value="" selected>Open this select menu</option>
                            @foreach($category as $class)
                                <option value="{{$class->id}}" @if($class->id==$item->category_id) selected @endif>
                                    {{ lecho($class->name) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3" style="display: none">
                        <label for="blog_tags" class="form-label">{{lecho('Tags')}}</label>
                        <input type="text" name="tags" class="form-control" id="blog_tags"
                               value="{{implode(',',$item->sub_title?:[])}}"
                               placeholder="{{lecho('Please enter the title of the blog post')}}">
                        {{lecho('Please use the [,] symbol to separate the labels. A maximum of 5 labels can be used, and each label cannot exceed 4 characters')}}
                    </div>
                    <div class="editors mb-3">
                        <div id="editor—wrapper">
                            <div id="toolbar-container"><!-- tool --></div>
                            <div id="editor-container"><!-- editor --></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="img-box full">
                            <section class="img-section">
                                <p class="up-p">{{lecho('Blog Cover')}} : <span
                                            class="up-span">{{lecho('Up to 3 images can be uploaded')}}</span></p>
                                <div class="z_photo upimg-div clear d-flex">
                                    <section class="z_file fl cursor" style="cursor: pointer">
                                        <img src="/assets/img/a11.jpg" class="add-img cursor">
                                        <input type="file" name="file" id="file" class="file" value=""
                                               accept="image/jpg,image/jpeg,image/png,image/bmp" multiple/>
                                    </section>
                                </div>
                            </section>
                        </div>
                        <aside class="mask works-mask">
                            <div class="mask-content">
                                <p class="del-p">{{lecho('Are you sure you want to delete the image')}}？</p>
                                <p class="check-p">
                                    <span class="del-com wsdel-ok">{{lecho('Ok')}}</span>
                                    <span class="wsdel-no">{{lecho('Cancel')}}</span></p>
                            </div>
                        </aside>
                    </div>
                    <textarea style="display: none" type="hidden" id="content" name="full_content"></textarea>
                    <div class="mb-3">
                        <button type="button" id="submit" class="btn btn-primary btn-lg">{{lecho('Publish')}}</button>
                    </div>
                    @csrf
                </form>
            </div>
        </div>
    </div>
    <textarea id="editContent" style="display: none;">{!! $item->content !!}</textarea>
@endsection

@section('script')
    <link href="{{asset('assets/css/upload.css')}}" rel="stylesheet">
    <link href="{{asset('assets/css/editor.css')}}" rel="stylesheet">
    <script src="{{asset('assets/js/editor.js')}}"></script>
    <script src="{{asset('assets/js/imgUp.js')}}?ver=1.1"></script>
    <script>
        var upfiles = [
            @foreach($item->pictureUrls as $pic)
                '{{$pic}}',
            @endforeach
        ];
        const {DomEditor, createEditor, createToolbar, i18nChangeLanguage} = window.wangEditor
        i18nChangeLanguage('en')
        const editorConfig = {
            placeholder: 'Type here...',
            onMaxLength: 4500,
            onChange(editor) {
                const html = editor.getHtml();
                $("#content").val(html);
            },
            MENU_CONF: {
                uploadImage: {
                    server: '{{route('editor.upload')}}?_token={{csrf_token()}}',
                },
                insertImage: {
                    onInsertedImage(imageNode) {
                        if (imageNode == null) return
                        const {src, alt, url, href} = imageNode
                        allPic.push(src);
                    },
                }
            },
        };
        const editor = createEditor({
            selector: '#editor-container',
            html: $("#editContent").val(),
            config: editorConfig,
            mode: 'simple', // or 'simple'
        })
        const toolbar = createToolbar({
            editor,
            selector: '#toolbar-container',
            config: {
                excludeKeys: [
                    'uploadVideo'
                ],
            },
            mode: 'default', // or 'simple'
        })
        const uploadButton = document.getElementById('submit');
        uploadButton.addEventListener('click', async () => {
            var $form = $("#pushForm");
            var $action = $form.attr("action");
            var formData = new FormData();
            var queryData = $form.serializeArray();
            for (i in queryData) {
                formData.append(queryData[i].name, queryData[i].value);
            }
            var pp = document.getElementsByClassName('up-img-form');
            for (var i = 0; i < pp.length; i++) {
                let blobUrl = pp[i].src;
                if (blobUrl.startsWith("http")) {
                    formData.append('pics[]', blobUrl);
                } else {
                    let blob = await (await fetch(blobUrl)).blob();
                    formData.append('files[]', blob);
                }
            }

            if ($("#blog_title").val().length <= 5) {
                updateAlert("blog title length min 5", false);
                return false;
            }

            if ($("#content").val() == "<p><br></p>") {
                updateAlert("please enter content", false);
                return false;
            }

            if ($("#content").val() == "") {
                updateAlert("please enter content", false);
                return false;
            }

            if ($("#content").val().length < 30) {
                updateAlert("content min:30", false);
                return false;
            }

            let loading = '/assets/img/upload_loading.gif';
            let value = $('#submit').text();
            $('#submit').html('<img style="width:24px;height:24px" src="' + loading + '">&nbsp;&nbsp;&nbsp;' + value);
            $('#submit').attr('disabled', 'disabled');


            $.ajax({
                async: false,
                url: $action,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (re) {
                    if (re.code == 200) {
                        updateAlert(re.message, true);
                        if (re.url) {
                            location.href = re.url;
                        } else {
                            location.reload();
                        }
                    } else {
                        $('#submit').removeAttr('disabled');
                        $('#submit').text(value);
                        updateAlert(re.message, false);
                    }
                }
            });

        });
    </script>
@endsection
