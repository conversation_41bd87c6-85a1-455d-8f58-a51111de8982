@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <style>
        .scoreh2 {
            text-align: center;
            font-size: 30px;
            margin: 30px 0;
            font-weight: 600;
            color: #000
        }

        .atable {
            border: 1px solid #f9f9f9;
            padding: 10px;
            border-radius: 5px
        }

        .atable table tr {
            border-bottom: 1px solid #f9f9f9
        }

        .atable table td {
            padding: 10px;
        }
    </style>
    <div class="container">
        <div class="scoreh2">
            <div>{{lecho('Points Ranking')}}</div>
            {{--            <div style="font-size:12px;" class="mt-10">--}}
            {{--                <span class="mr-20">{{lecho('Total Score')}}：{{$total}}</span>--}}
            {{--                <span>{{lecho("Today's points")}}：{{$totalDay}}</span>--}}
            {{--            </div>--}}
        </div>
        <div class="atable">
            <table class="table table-hover">
                <thead>
                <tr>
                    <th scope="col">Id</th>
                    <th scope="col">{{lecho('Avatar')}}</th>
                    <th scope="col">{{lecho('Nickname')}}</th>
                    <th scope="col">{{lecho('points')}}</th>
                    <th scope="col">{{lecho('Number of fans')}}</th>
                    <th scope="col">{{lecho('Thumbs total')}}</th>
                </tr>
                </thead>
                <tbody>
                @foreach($lists as $item)
                    <tr @if($loop->even) class="table-secondary" @endif>
                        <th style="vertical-align: middle;" scope="row">{{bcadd($loop->iteration,(request('page',1)-1)*50,0)}}</th>
                        <td style="vertical-align: middle;"><a href="{{route('user.info',$item)}}"><img src="{{$item->info->avatar_url}}" width="50"></a></td>
                        <td style="vertical-align: middle;"><a href="{{route('user.info',$item)}}">{{$item->info->nickname}}</a></td>
                        <td style="vertical-align: middle;">{{$item->score?:0}}</td>
                        <td style="vertical-align: middle;">{{$item->subscribe_count?:0}}</td>
                        <td style="vertical-align: middle;">{{$item->like_sum?:0}}</td>
                    </tr>
                @endforeach

                </tbody>
            </table>
        </div>
        <div class="mt-20">
            {{$lists->links()}}
        </div>

    </div>
@endsection
