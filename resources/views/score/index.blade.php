@extends('layout.base')

@section('content')
    <div class="container fbody">
        <style>
            .mycoin{font-size:40px;font-weight:600;margin:20px 0}
        </style>
        @include("layout.clubHeader") <div class="row">
        <div class="col-sm-12 col-md-2 mb-10 pr-20">
            @include("users.menu")
        </div>
        <div class="col-sm-12 col-md-10">
                <div class="flex" style="align-items: center;justify-content: space-between">
                    <div>
                        <div class="mycoin">12564</div>
                        <div>{{lecho('Integral is calculated in real-time')}},<a href="javascript;">{{lecho('Points acquisition rules')}}</a></div>
                    </div>
                </div>
                <div class="mt-50">
                    <ul class="nav nav-tabs">
                        <li class="nav-item"><a class="nav-link active" aria-current="page" href="javascript:">{{lecho('Points Details')}}</a></li>
                    </ul>
                </div>
                <div class="tables">
                    <table class="table table-striped table-hover">
                        <thead>
                        <tr>
                            <th scope="col">{{lecho('Integral behavior')}}</th>
                            <th scope="col">{{lecho('Integral value')}}</th>
                            <th scope="col">{{lecho('Time')}}</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>读者阅读博文赠送积分</td>
                            <td>1</td>
                            <td>2024-10-07 21:19</td>
                        </tr>


                        </tbody>

                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(function(){
            $("#toCoin").click(function(){
                $("#staticBackdrop").modal("show")
            })
        })
    </script>
    <div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <div>转账</div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="eusername" class="form-label">对方ID或是EMAIL</label>
                        <input type="text" class="form-control" id="eusername" placeholder="对方ID或是EMAIL">
                    </div>
                    <div class="mb-3">
                        <label for="coin" class="form-label">转账金额</label>
                        <input type="number" class="form-control" id="coin" placeholder="转账金额">
                    </div>
                    <div class="mb-3">
                        <label for="coin" class="form-label">支付密码</label>
                        <input type="password" class="form-control" id="coin" placeholder="请输入支付密码">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary">确认转账</button>
                </div>
            </div>
        </div>
    </div>
@endsection
