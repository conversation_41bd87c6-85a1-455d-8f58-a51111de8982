@extends('layout.base')
@section('title',$sub_title)
@section('content')
    <div class="container fbody">
        <style>
            .mycoin {
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 20px
            }

            #qrcode_input {
                width: 100%;
                max-width: 50%
            }
        </style>
        @include("layout.clubHeader")
        <div class="row">
        <div class="col-sm-12 col-md-2 mb-10 pr-20">
            @include("users.menu")
        </div>
        <div class="col-sm-12 col-md-10">
                <div>
                    <div>
                        <div class="mycoin">{{lecho("My Share qrcode")}}</div>
                        <div><img style="width:200px;height:200px"
                                  src="data:image/png;base64,{{base64_encode($qrcode)}}"/></div>
                        <form class="row mt-30 g-3">
                            <div>
                                <input type="text" class="form-control" id="qrcode_input" readonly value="{{$url}}">
                            </div>
                            <div>
                                <button type="button" id="copy_link"
                                        class="btn btn-primary mb-3">{{lecho("Copy Link")}}</button>
                            </div>
                        </form>

                    </div>
                </div>
                <div class="mt-50">
                    <ul class="nav nav-tabs">
                        <li class="nav-item"><a class="nav-link active"
                                                aria-current="page">{{lecho('Invite users')}}</a></li>
                    </ul>
                </div>
                <div class="tables">
                    <table class="table table-striped table-hover">
                        <thead>
                        <tr>
                            <th scope="col">{{lecho('user')}}</th>
                            <th scope="col">{{lecho('identity')}}</th>
                            <th scope="col">{{lecho('Registration time')}}</th>
                        </tr>
                        </thead>
                        <tbody>
                        @if($lists->isEmpty())
                            <tr>
                                <td colspan="3">{{lecho('You have not invited any users to join yet')}}</td>
                            </tr>
                        @else
                            @foreach($lists as $item)
                                <tr>
                                    <td>{{$item->user->showName}}</td>
                                    <td>{{ lecho($item->user->identityFirst()?->name) }}</td>
                                    <td>{{$item->user->created_at}}</td>
                                </tr>
                            @endforeach

                        @endif
                        </tbody>

                    </table>
                    {{$lists->links()}}
                </div>
            </div>
        </div>
    </div>
    <textarea style="width:1px;height:1px;" id="contents">{{$url}}</textarea>
@endsection
@section('script')
    <script>
        $(function () {
            $("#copy_link").click(function () {
                var textArea = document.getElementById('contents'); // 获取文本区域元素
                textArea.select(); // 选择文本区域中的所有文字
                document.execCommand('copy'); // 将选定的文本复制到剪贴板
                updateAlert("{{lecho("Copy Success")}}", true);
            })
        })
    </script>
@endsection
