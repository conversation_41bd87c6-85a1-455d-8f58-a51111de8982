@php use Illuminate\Support\Facades\Auth;use Modules\Interaction\Enums\CommentStatus; @endphp
@extends('layout.base')
@section('title',$sub_title)

@section('style')
    <style>
        .comment-body {
            overflow-wrap: break-word;
        }

        @media screen and (max-width: 600px) {
            .blog-list-wrapper .left-content .card-meta {
                padding: 10px 0
            }
        }
    </style>
@endsection

@section('content')
    <div class="container">
        <section class="single-post-wrapper blog-list-wrapper section-padding">
            <div class="container">

                <style>
                    .heading-primary {
                        font-size: 30px
                    }

                    .blog-list-wrapper .left-content .card-content {
                        margin-top: 0px
                    }

                    .post-content {
                        color: #000000
                    }

                    .post-point img, .post-content img {
                        width: 100%;
                        margin: 0 auto
                    }

                    .charCount {
                        height: 50px;
                    }
                </style>
                <div class="row gy-5">
                    <div style="font-size:30px;">
                        {{$content->title}}
                        <div style="margin-top:10px;font-size:16px;">{!! str_replace(PHP_EOL,'<br>',$content->description) !!}</div>
                    </div>

                    <div class="col-lg-1"><img src="{{ $content->cover_url }}"></div>
                    <div class="col-lg-11">
                        <div class="left-content">
                            {!! $content->content !!}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection

