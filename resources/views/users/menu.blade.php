<div class="menus-1">
    <div class="dropdown">
        <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-bars"></i>
        </button>
        <ul class="dropdown-menu">
            @include('users.item')
        </ul>
    </div>
</div>
<div class="menus">
    <ul class="dropdown-menu position-static d-grid gap-1 p-2 rounded-3 mx-0 shadow">
        @include('users.item')
    </ul>
</div>
<style>
    .menus .dropdown-item {
        padding: 5px 10px;
        display: flex;
        align-items: center;
    }
    .menus .dropdown-item img ,.menus-1 .dropdown-item img{
        margin-left: 10px;
        display: none
    }

</style>

<script>
    $(function(){
        if($(window).width()<=430){
            $('.menus').hide()
            $('.menus-1').show()
        }else{
            $('.menus').show()
            $('.menus-1').hide()
        }

    })
</script>
