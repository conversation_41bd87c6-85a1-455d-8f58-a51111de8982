@extends('layout.base')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">{{ lecho('buy') }}WT</div>

                    <div class="card-body">
                        <div class="alert alert-info">
                            {{ lecho('Current WT Token Price') }}: ${{ number_format($wtPrice, 2) }} / WT
                        </div>

                        <form id="buyForm" method="POST" action="{{ route('wallet.create-order') }}">
                            @csrf
                            <div class="form-group mb-3">
                                <label for="amount">{{ lecho('buy') }}{{ lecho('Quantity') }}</label>
                                <input type="number" class="form-control" id="amount" name="amount" min="1" step="1"
                                       required>
                                <small class="form-text text-muted">{{ lecho('WT token amount') }}</small>
                            </div>

                            <div class="form-group mb-3">
                                <label>{{ lecho('Estimated Total') }}</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text" class="form-control" id="totalAmount" readonly>
                                </div>
                            </div>

                            <div id="errorMessage" class="alert alert-danger mt-3" style="display: none;"></div>
                            <button type="button" id="submitBtn"
                                    class="btn btn-primary">{{ lecho('Create Order') }}</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function () {
            console.log('WT buy page script loaded');
            const amountInput = document.getElementById('amount');
            const totalAmountInput = document.getElementById('totalAmount');
            const wtPrice = {{ $wtPrice }};

            console.log('WT price:', wtPrice);
            console.log('Amount input element:', amountInput);
            console.log('Total amount input element:', totalAmountInput);

            // 计算总金额
            function calculateTotal() {
                const amount = parseFloat(amountInput.value) || 0;
                console.log('Input amount:', amount);
                const total = (amount * wtPrice).toFixed(2);
                console.log('Calculated total:', total);
                totalAmountInput.value = total;
            }

            // 监听输入变化
            $(amountInput).on('input', function () {
                console.log('Input event triggered');
                calculateTotal();
            });

            // 初始计算
            calculateTotal();

            // 表单提交
            $('#submitBtn').on('click', function () {
                const amount = parseFloat(amountInput.value);
                if (!amount || amount < 1) {
                    $('#errorMessage').text('{{ lecho('Enter a valid amount') }}').show();
                    return;
                }

                // 隐藏错误信息
                $('#errorMessage').hide();

                // 禁用按钮
                const $btn = $(this);
                $btn.prop('disabled', true).text('{{ lecho('Processing...') }}');

                // 发送AJAX请求
                $.ajax({
                    url: $('#buyForm').attr('action'),
                    type: 'POST',
                    data: $('#buyForm').serialize(),
                    dataType: 'json',
                    success: function (response) {
                        if (response.code == 200) {
                            updateAlert(response.message, true);

                            // 跳转到支付页面 怎么延迟1秒跳转
                            setTimeout(function () {
                                window.location.href = response.url;
                            }, 1000);
                        } else {
                            // 显示错误信息
                            $('#errorMessage').text(response.message).show();
                            $btn.prop('disabled', false).text('{{ lecho('Create Order') }}');
                        }
                    },
                    error: function (xhr) {
                        let errorMsg = '{{ lecho('Failed. Retry') }}';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        }
                        $('#errorMessage').text(errorMsg).show();
                        $btn.prop('disabled', false).text('{{ lecho('Create Order') }}');
                    }
                });
            });
        });
    </script>
@endsection
