<?php

namespace Modules\Payment\Models\Traits;

use Illuminate\Database\Eloquent\Relations\MorphOne;
use Modules\Payment\Enums\CorporateStatus;
use Modules\Payment\Models\Corporate;

trait MorphOneCorporate
{

    /**
     * Notes: 关联对公账户信息
     *
     * @Author: 玄尘
     * @Date: 2023/10/9 16:09
     * @return mixed
     */
    public function corporate(): morphOne
    {
        return $this->morphOne(Corporate::class, 'orderable');
    }

    /**
     * Notes: 是否有线下打款
     *
     * @Author: 玄尘
     * @Date: 2023/10/9 16:10
     * @return bool
     */
    public function isCorporate(): bool
    {
        return $this->corporate()->exists();
    }

    /**
     * Notes: 线下打款信息
     * 0 未打款 1 待审核 2 通过 3 驳回
     *
     * @Author: 玄尘
     * @Date: 2023/10/9 16:10
     */
    public function getCorporateStatus(): int
    {
        return match ($this->corporate?->status) {
            CorporateStatus::INIT => 1,
            CorporateStatus::PASS => 2,
            CorporateStatus::REFUSE => 3,
            default => 0,
        };
    }
}