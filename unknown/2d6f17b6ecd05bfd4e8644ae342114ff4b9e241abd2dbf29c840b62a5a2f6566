<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Support\Facades\Hash;

class Security extends Model
{
    use BelongsToUser;

    protected $table = 'payment_securities';

    protected $hidden = [
        'password',
    ];

    public function setPasswordAttribute(string $value): void
    {
        $this->attributes['password'] = bcrypt($value);
    }

    public function verify(string $value): bool
    {
        return Hash::check($value, $this->password);
    }
}
