<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;

class LakalaAdapter implements PaymentAdapter
{
    private string $apiUrl;

    private array $config;

    public function __construct(protected Payment|null $payment)
    {
        if (config('payment.LAKALA_ENV') == 'sandbox') {
            $this->apiUrl = 'https://test.wsmsd.cn/sit/api/v3/';
        } else {
            $this->apiUrl = 'https://s2.lakala.com/api/v3/';
        }

        $this->config = $this->getConfig();
    }

    /**
     * Notes   : 获取配置信息
     *
     * @Date   : 2023/6/13 15:32
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public function getConfig(): array
    {
        return [
            'app_id'        => config('payment.LAKALA_APP_ID'),
            'merchant_no'   => config('payment.LAKALA_MERCHANT_NO'),
            'term_no'       => config('payment.LAKALA_TERM_NO'),
            'mch_serial_no' => config('payment.LAKALA_MCH_SERIAL_NO'),
            'vpos_id'       => config('payment.LAKALA_VPOS_ID'),
            'public_key'    => config('payment.LAKALA_PUBLIC_KEY'),
            'private_key'   => config('payment.LAKALA_PRIVATE_KEY'),
            'schema'        => 'LKLAPI-SHA256withRSA',
            'version'       => '1.0.0',
        ];
    }

    /**
     * Notes   : 聚合主扫，支付宝
     *
     * @Date   : 2023/7/25 16:46
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Exception
     */
    public function scan_alipay(): array
    {
        return Arr::only($this->scan('ALIPAY', '41')['acc_resp_fields'], 'code');
    }

    /**
     * Notes   : 聚合主扫，微信
     *
     * @Date   : 2023/7/25 17:01
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Exception
     */
    public function scan_wechat(): array
    {
        return $this->scan('WECHAT', '51', [
            'user_id' => request()->open_id,
        ])['acc_resp_fields'];
    }

    /**
     * @throws \Exception
     */
    private function scan(string $type, string $trans, array $extends = [])
    {
        $params = [
            'out_trade_no'    => $this->payment->no.rand(1, ************),
            'account_type'    => $type,
            'trans_type'      => $trans,
            'total_amount'    => intval($this->payment->amount * 100),
            'location_info'   => [
                'request_ip' => request()->ip(),
            ],
            'subject'         => $this->payment->paymentable->getTitle(),
            'notify_url'      => route('api.payment.notify', Gateway::LAKALA->value),
            'acc_busi_fields' => $extends,
        ];

        $result = $this->post('labs/trans/preorder', $params);

        if ($result['code'] == 'BBS00000') {
            return $result['resp_data'];
        } else {
            throw new Exception($result['msg'], 95210);
        }
    }

    /**
     * Notes   : 收银台地址
     *
     * @Date   : 2023/6/13 15:32
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Exception
     */
    public function ccss(): array
    {
        $result = $this->post('ccss/counter/order/create', [
            'vpos_id'              => $this->config['vpos_id'],
            'out_order_no'         => $this->payment->no,
            'total_amount'         => intval($this->payment->amount * 100),
            'order_efficient_time' => Carbon::now()->addDay()->format('YmdHis'),
            'notify_url'           => route('api.payment.notify', Gateway::LAKALA->value),
            'support_cancel'       => 1,
            'order_info'           => $this->payment->paymentable->getTitle(),
            'support_refund'       => 1,
            'counter_param'        => json_encode(['pay_mode' => 'WECHAT']),
            'support_repeat_pay'   => 1,
        ]);

        if ($result['code'] == '000000') {
            return $result['resp_data'];
        } else {
            throw new Exception($result['msg'], 95210);
        }
    }

    /**
     * Notes   : 获取回调信息
     *
     * @Date   : 2023/6/13 15:33
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Exception
     */
    public function callback(): array
    {
        if ($this->verifySignature(request()->header('Authorization'), request()->all())) {
            return request()->all();
        } else {
            throw new Exception('验签失败', 95210);
        }
    }

    /**
     * Notes   : 成功响应
     *
     * @Date   : 2023/6/13 15:33
     * <AUTHOR> <Jason.C>
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function success(): ResponseInterface
    {
        return new Response(200, [], [
            'code'    => 'SUCCESS',
            'message' => '执行成功',
        ]);
    }

    /**
     * Notes   : 查询支付结果
     *
     * @Date   : 2023/6/13 15:33
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Exception
     */
    public function query(): array
    {
        $result = $this->post('labs/query/tradequery', ['out_order_no' => $this->payment->no]);

        if ($result['code'] == 'BBS00000') {
            return $result['resp_data'];
        } else {
            throw new Exception($result['msg'], 95210);
        }
    }

    /**
     * Notes   : 退款
     *
     * @Date   : 2023/6/13 15:33
     * <AUTHOR> <Jason.C>
     * @param  string  $refundNo
     * @param  float  $refundAmount
     * @param  string  $desc
     * @return array
     * @throws \Exception
     */
    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        $request = $this->post('labs/relation/refund', [
            'out_trade_no'        => $refundNo,
            'refund_amount'       => intval($refundAmount * 100),
            'refund_reason'       => $desc,
            'origin_out_trade_no' => $this->payment->no,
            'location_info'       => [
                'request_ip' => request()->ip(),
            ],
        ]);

        if ($request['code'] == 'BBS00000') {
            return $request;
        } else {
            throw new Exception($request['msg'], 95220);
        }
    }

    /**
     * Notes   : 发送 POST 请求
     *
     * @Date   : 2023/6/12 16:21
     * <AUTHOR> <Jason.C>
     * @throws \Exception
     */
    private function post(string $uri, array $body): array
    {
        $uri    = ltrim($uri, '/');
        $body   = array_merge([
            'merchant_no' => $this->config['merchant_no'],
            'term_no'     => $this->config['term_no'],
        ], $body);
        $params = [
            'version'  => $this->config['version'],
            'req_time' => Carbon::now()->format('YmdHis'),
            'req_data' => $body,
        ];

        try {
            $client = new Client([
                'base_uri' => $this->apiUrl,
            ]);

            $headers = $this->headers($params);
            $result  = $client->post($uri, [
                'body'    => json_encode($params, JSON_UNESCAPED_UNICODE),
                'headers' => $headers,
            ]);

            return json_decode($result->getBody()->getContents(), true);
        } catch (GuzzleException $e) {
            throw new Exception($e->getMessage(), 95200);
        }
    }

    /**
     * Notes   : 组装请求header
     *
     * @Date   : 2023/6/13 15:33
     * <AUTHOR> <Jason.C>
     * @param  array  $body
     * @return array
     */
    private function headers(array $body): array
    {
        return [
            'Authorization' => $this->getAuthorization($body),
            'Accept'        => 'application/json',
            'Content-Type'  => 'application/json',
        ];
    }

    /**
     * Notes   : 身份鉴权
     *
     * @Date   : 2023/6/13 15:33
     * <AUTHOR> <Jason.C>
     * @param  array  $body
     * @return string
     */
    private function getAuthorization(array $body): string
    {
        $config = $this->getConfig();

        $nonceStr  = Str::random(8);
        $timestamp = time();
        $body      = json_encode($body, JSON_UNESCAPED_UNICODE);

        $message = $config['app_id']."\n".$config['mch_serial_no']."\n".$timestamp."\n".$nonceStr."\n".$body."\n";

        $key = openssl_get_privatekey(file_get_contents($config['private_key']));

        openssl_sign($message, $signature, $key, OPENSSL_ALGO_SHA256);

        return $config['schema']." appid=\"".$config['app_id']."\","."serial_no=\"".$config['mch_serial_no']."\","."timestamp=\"".$timestamp."\","."nonce_str=\"".$nonceStr."\","."signature=\"".base64_encode($signature)."\"";
    }

    /**
     * Notes   : 回调验签
     *
     * @Date   : 2023/6/13 15:33
     * <AUTHOR> <Jason.C>
     * @param  string  $authorization
     * @param  array  $response
     * @return bool
     */
    public function verifySignature(string $authorization, array $response): bool
    {
        $authorization = str_replace($this->config['schema']." ", "", $authorization);
        $authorization = str_replace(",", "&", $authorization);
        $authorization = str_replace("\"", "", $authorization);
        $authorization = $this->convertUrlQuery($authorization);

        $message = sprintf(
            "%s\n%s\n%s\n",
            $authorization['timestamp'],
            $authorization['nonce_str'],
            json_encode($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
        );

        // 替换掉 $response 中的null元素
        $message = str_replace(['null', 'NULL'], '""', $message);
        $key     = openssl_get_publickey(file_get_contents($this->config['public_key']));

        return openssl_verify($message, base64_decode($authorization['signature']), $key, OPENSSL_ALGO_SHA256);
    }

    private function convertUrlQuery($query): array
    {
        $queryParts = explode('&', $query);

        $params = [];
        foreach ($queryParts as $param) {
            $item             = explode('=', $param);
            $params[$item[0]] = $item[1];
        }
        if ($params['signature']) {
            $params['signature'] = substr($query, strrpos($query, 'signature=') + 10);
        }

        return $params;
    }
}
