<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use GuzzleHttp\Psr7\Response;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Exceptions\CallbackParamsException;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;

class ChinaUmsAdapter implements PaymentAdapter
{
    public function __construct(protected Payment|null $payment)
    {
    }

    public function getConfig(): array
    {
        return [
            'notify_url' => route('api.payment.notify', Gateway::CHINA_UMS->value),
        ];
    }

    public function pc()
    {
    }

    public function wap(): array
    {
        return [];
    }

    /**
     * @throws \Exception
     */
    public function callback(): array
    {
        $shaKey = '44n8E7TeEceTFkpFStkhZ8PYkyGXZpFAjSB3Fd3EGEXBP7Cw';
        $notify = request()->post();
        $sign   = $notify['sign'];
        unset($notify['sign']);
        ksort($notify);

        $str    = http_build_query($notify).$shaKey;
        $sha256 = hash('sha256', urldecode($str));
        if ($sign != strtoupper($sha256)) {
            throw new CallbackParamsException('验签错误');
        } else {
            return request()->all();
        }
    }

    public function success(): ResponseInterface
    {
        return new Response(200, [], 'SUCCESS');
    }

    public function query(): array
    {
        return [];
    }

    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        return [];
    }
}